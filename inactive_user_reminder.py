"""
定时检查非活跃用户并发送消息模块
负责检查72小时未交互的用户并发送AI生成的消息

功能：
1. 定期检查最近72小时未与Bot交互的用户
2. 使用AI生成自然对话消息
3. 以受控速率发送消息，遵守Telegram API限制
4. 如果用户已拉黑Bot，则更新状态不再尝试发送
"""

import asyncio
import aiosqlite
import random
import json
from datetime import datetime, timedelta
from logging_config import logger
from config import DB_PATH, INACTIVE_USER_CONFIG

# 导入Telegram错误处理类，兼容不同版本的python-telegram-bot
try:
    from telegram.error import Forbidden, TelegramError
except ImportError:
    try:
        # Python-Telegram-Bot v20+
        from telegram.constants import ChatAction
        from telegram.error import Forbidden, TelegramError
    except ImportError:
        # 定义占位符类，用于开发环境
        class Forbidden(Exception): pass
        class TelegramError(Exception): pass

from database import get_user_data, save_user_data
from ai_dialogue.chat_processor import chat_with_ai
from ai_dialogue.response_sender import send_response
from ai_dialogue.user_manager import determine_voice_usage

# 导入自定义API所需模块
import aiohttp
from ai_dialogue.content_processor import build_system_prompt, comprehensive_content_filter
from utils import process_ai_response

# 从配置获取消息发送频率限制
MESSAGES_PER_MINUTE = INACTIVE_USER_CONFIG.get("messages_per_minute", 10)
MESSAGE_INTERVAL = 60 / MESSAGES_PER_MINUTE  # 每条消息之间的间隔秒数

# 添加全局变量用于控制任务状态
inactive_reminder_running = False
inactive_reminder_task = None

async def call_custom_api(user_id, conversation):
    """
    使用自定义API配置调用AI接口
    
    Args:
        user_id: 用户ID
        conversation: 对话历史
        
    Returns:
        AI生成的回复
    """
    # 获取API配置
    api_config = INACTIVE_USER_CONFIG.get("api_config", {})
    if not api_config:
        logger.warning(f"未找到自定义API配置，使用默认API")
        # 使用默认的chat_with_ai函数
        return None
    
    try:
        url = api_config.get("url")
        api_key = api_config.get("key")
        model = api_config.get("model")
        break_limit = api_config.get("break_limit", False)
        
        if not all([url, api_key, model]):
            logger.warning(f"自定义API配置不完整，使用默认API")
            return None
        
        # 检查是否需要使用破限模式
        # 如果API配置为不使用破限，移除系统提示词或使用简化版本
        processed_conversation = conversation
        if not break_limit:
            # 移除系统提示词
            system_msgs = [msg for msg in conversation if msg["role"] == "system"]
            user_assistant_msgs = [msg for msg in conversation if msg["role"] != "system"]
            
            if system_msgs:
                logger.info(f"用户 {user_id} 自定义API不支持破限模式，移除复杂系统提示词")
                # 使用简化版的系统提示词
                processed_conversation = [{"role": "system", "content": "You are a helpful assistant."}] + user_assistant_msgs
                logger.debug(f"用户 {user_id} 原系统提示词长度: {len(system_msgs[0]['content'])}, 替换为简化版")
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        payload = {
            "model": model,
            "messages": processed_conversation,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload, timeout=60) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"用户 {user_id} 自定义API调用失败: 状态码 {response.status}, 错误: {error_text}")
                    return None
                
                result = await response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"用户 {user_id} 自定义API返回格式错误: {result}")
                    return None
    except Exception as e:
        logger.error(f"用户 {user_id} 自定义API调用异常: {str(e)}")
        return None

async def get_inactive_users(hours=None):
    """
    获取指定时间内未交互且未拉黑的用户列表
    
    Args:
        hours: 不活跃的小时数阈值，默认从配置读取
        
    Returns:
        包含用户ID的列表
    """
    # 如果未提供hours参数，则从配置读取
    if hours is None:
        hours = INACTIVE_USER_CONFIG.get("inactive_threshold", 72)
        
    cutoff_time = (datetime.now() - timedelta(hours=hours)).isoformat()
    
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        query = """
        SELECT user_id 
        FROM users 
        WHERE is_blocked = 0 
          AND (last_interaction_time IS NULL OR last_interaction_time < ?) 
        ORDER BY last_interaction_time ASC
        """
        
        cursor = await conn.execute(query, (cutoff_time,))
        users = await cursor.fetchall()
        return [str(user[0]) for user in users]

async def send_reminder_message(bot, user_id):
    """
    给指定用户发送AI生成的消息
    
    Args:
        bot: Telegram Bot对象
        user_id: 用户ID
        
    Returns:
        布尔值，表示发送是否成功
    """
    try:
        # 获取用户数据
        user_data = await get_user_data(user_id)
        
        # 再次检查用户是否已拉黑，以防在获取用户列表和实际发送之间的时间里状态发生变化
        if user_data.get("is_blocked", 0) == 1:
            logger.info(f"用户 {user_id} 已拉黑Bot，跳过发送消息")
            return False
        
        # 构建一个简单的提示，让AI生成适当的消息
        message = "好久不见了，想你了"
        
        # 使用determine_voice_usage函数确定是否使用语音回复
        use_voice, deduct_type = await determine_voice_usage(user_id, user_data, bot, user_id)
        logger.info(f"用户 {user_id} 语音设置: use_voice={use_voice}, deduct_type={deduct_type}")
        
        # 使用自定义API或默认API生成回复
        if INACTIVE_USER_CONFIG.get("use_custom_api", False):
            logger.info(f"为非活跃用户 {user_id} 使用自定义API生成消息")
            
            # 获取API配置
            api_config = INACTIVE_USER_CONFIG.get("api_config", {})
            break_limit = api_config.get("break_limit", False)
            logger.info(f"用户 {user_id} API配置: {api_config.get('name', '未命名')}, break_limit={break_limit}")
            
            # 构建系统提示词
            system_prompt = await build_system_prompt(
                user_id, message, user_data, [], 
                len(user_data.get("conversation", [])) == 0, 
                user_data.get("preferred_language", "zh-cn")
            )
            
            # 构建对话历史
            conversation = [{"role": "system", "content": system_prompt}]
            
            # 添加历史对话（最多10轮）
            if user_data.get("conversation"):
                conversation.extend(user_data["conversation"][-10:])
            
            # 添加当前用户消息
            conversation.append({"role": "user", "content": message})
            
            # 调用自定义API
            ai_response = await call_custom_api(user_id, conversation)
            
            if ai_response:
                # 应用内容过滤
                user_language = user_data.get("preferred_language", "zh-cn")
                filtered_response = comprehensive_content_filter(ai_response, user_language)
                # 处理AI响应
                response_sentences = process_ai_response(filtered_response)
                
                # 保存对话历史
                user_data["conversation"].extend([
                    {"role": "user", "content": message},
                    {"role": "assistant", "content": filtered_response}
                ])
                
                # 限制对话历史长度
                if len(user_data["conversation"]) > 20:
                    user_data["conversation"] = user_data["conversation"][-20:]
                
                await save_user_data(user_id, user_data)
                
                # 发送消息 - 根据用户设置决定是否使用语音
                success = await send_response(bot, user_id, user_id, (response_sentences, deduct_type), use_voice)
            else:
                # 自定义API调用失败，回退到默认API
                logger.warning(f"用户 {user_id} 自定义API调用失败，回退到默认API")
                responses, _ = await chat_with_ai(user_id, message, deduct_type, logger, bot)
                success = await send_response(bot, user_id, user_id, (responses, deduct_type), use_voice)
        else:
            # 使用默认API
            logger.info(f"为非活跃用户 {user_id} 生成AI消息")
            responses, _ = await chat_with_ai(user_id, message, deduct_type, logger, bot)
            success = await send_response(bot, user_id, user_id, (responses, deduct_type), use_voice)
        
        if success:
            logger.info(f"成功向非活跃用户 {user_id} 发送提醒消息，语音模式: {use_voice}")
            # 更新最后交互时间为当前时间减去70小时（这样下一次检查时不会马上再次发送）
            user_data["last_interaction_time"] = (datetime.now() - timedelta(hours=70)).isoformat()
            await save_user_data(user_id, user_data)
            return True
        else:
            logger.warning(f"向非活跃用户 {user_id} 发送消息失败")
            return False
            
    except Forbidden:
        # 用户已拉黑Bot
        logger.info(f"发送消息时发现用户 {user_id} 已拉黑Bot，更新状态")
        try:
            user_data = await get_user_data(user_id)
            user_data["is_blocked"] = 1
            await save_user_data(user_id, user_data)
        except Exception as e:
            logger.error(f"更新用户 {user_id} 的is_blocked状态失败: {str(e)}")
        return False
    except TelegramError as e:
        logger.error(f"向用户 {user_id} 发送消息时发生Telegram错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"向用户 {user_id} 发送消息时发生错误: {str(e)}")
        return False

async def process_inactive_users(bot):
    """
    处理所有不活跃用户，发送AI生成的消息
    
    Args:
        bot: Telegram Bot对象
    """
    try:
        # 获取不活跃用户列表
        inactive_users = await get_inactive_users()
        total_users = len(inactive_users)
        
        if not inactive_users:
            logger.info("没有找到不活跃的用户")
            return
            
        logger.info(f"找到 {total_users} 个不活跃用户，开始发送消息")
        
        # 随机打乱用户顺序，避免每次都按相同顺序发送
        random.shuffle(inactive_users)
        
        # 限制发送频率，遵循Telegram API限制
        count = 0
        for user_id in inactive_users:
            # 检查任务是否已经被停止
            if not inactive_reminder_running:
                logger.info("不活跃用户提醒任务被手动停止")
                return
                
            # 发送消息
            success = await send_reminder_message(bot, user_id)
            
            # 计数
            if success:
                count += 1
                
            # 间隔等待，避免超出API限制
            await asyncio.sleep(MESSAGE_INTERVAL)
            
            # 每处理20个用户打印一次日志
            if count % 20 == 0:
                logger.info(f"已处理 {count}/{total_users} 个不活跃用户")
        
        logger.info(f"不活跃用户处理完成，成功发送: {count}/{total_users}")
        
    except Exception as e:
        logger.error(f"处理不活跃用户时发生错误: {str(e)}", exc_info=True)

async def run_inactive_user_reminder(bot):
    """
    运行不活跃用户提醒任务
    根据配置的检查间隔定期执行
    
    Args:
        bot: Telegram Bot对象
    """
    global inactive_reminder_running
    
    # 启动时先等待5分钟，让系统完全启动
    await asyncio.sleep(300)
    
    # 从配置中获取检查间隔（小时）
    check_interval_hours = INACTIVE_USER_CONFIG.get("check_interval", 6)
    check_interval_seconds = check_interval_hours * 3600
    
    while True:
        try:
            # 检查是否应该执行提醒任务
            if inactive_reminder_running:
                inactive_threshold = INACTIVE_USER_CONFIG.get("inactive_threshold", 72)
                logger.info(f"开始检查不活跃用户（阈值：{inactive_threshold}小时）")
                await process_inactive_users(bot)
                logger.info(f"不活跃用户检查完成，{check_interval_hours}小时后再次检查")
            else:
                logger.debug("不活跃用户提醒任务已暂停，跳过本次检查")
        except Exception as e:
            logger.error(f"不活跃用户提醒任务发生错误: {str(e)}", exc_info=True)
        
        # 根据配置等待
        await asyncio.sleep(check_interval_seconds)

async def start_inactive_user_reminder(bot):
    """
    开始不活跃用户提醒任务
    
    Args:
        bot: Telegram Bot对象
        
    Returns:
        开始状态的消息
    """
    global inactive_reminder_running, inactive_reminder_task
    
    if inactive_reminder_running:
        return "不活跃用户提醒任务已经在运行中"
    
    inactive_reminder_running = True
    
    # 如果任务不存在，创建一个新任务
    if inactive_reminder_task is None:
        inactive_reminder_task = asyncio.create_task(run_inactive_user_reminder(bot))
    
    # 立即执行一次提醒任务
    asyncio.create_task(process_inactive_users(bot))
    
    return "不活跃用户提醒任务已开始运行"

async def stop_inactive_user_reminder():
    """
    停止不活跃用户提醒任务
    
    Returns:
        停止状态的消息
    """
    global inactive_reminder_running
    
    if not inactive_reminder_running:
        return "不活跃用户提醒任务已经停止"
    
    inactive_reminder_running = False
    return "不活跃用户提醒任务已停止" 