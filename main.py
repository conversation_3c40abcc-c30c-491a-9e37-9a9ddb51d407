import asyncio
import os
import fcntl
import sys
import uuid
import signal
from aiohttp import web
import aiosqlite
from telegram.ext import (
    <PERSON><PERSON><PERSON><PERSON>, MessageHandler, CallbackQueryHandler,
    PreCheckoutQueryHandler, filters, ContextTypes, Application
)
from telegram import Update, Bot
from telegram.error import TelegramError
from telegram.request import HTTPXRequest
from config import BOT_TOKEN, BOT_LOCK_PATH, DB_PATH, SERVER_HOST, SERVER_PORT, WEBHOOK_URL, DOWNLOAD_DIR, ENABLE_STREAMING_RESPONSE, MASTER_ID
from handlers import (
    start, clear_memory, ban_user, pay,
    handle_payment_callback, pre_checkout_query, successful_payment,
    forward_message, button_handler, redeem_code, daily_reset, restorememory, cleanmemory,
    set_default_voice, freecredits, handle_training_settings, handle_voice_settings,
    handle_personal_center, handle_free_credits, handle_register,
    handle_channel_pinned_click, handle_reset_step, cancel_pending_payment, handle_search_group_invite,
    user_info, handle_admin_user_input, clean_temp_files_command
)
from handlers.admin import api_status, ai_processing_stats, api_health
from dice_game import handle_dice, handle_dice_callbacks
from database import check_db, get_user_data, save_user_data, backup_conversations
from logging_config import logger
from app import init_app 
from ai_dialogue import (
    ai_worker, chat_with_ai, ai_worker_streaming, chat_with_ai_streaming,
    analyze_user_personality, start_api_keepalive_task, task_manager,
    ai_worker_unified,  # 添加新的统一worker
    start_broadcast, finish_broadcast, confirm_broadcast, cancel_broadcast, 
    handle_broadcast_message, private_chat_with_user  # 添加广播和私聊相关函数
)
from enhanced_memory import enhanced_memory
from enhanced_ai_dialogue import enhanced_ai_dialogue
from enhanced_memory_init import initialize_enhanced_memory, check_compatibility
from globals import global_ai_queue
import json
from datetime import datetime, time
from aiohttp.web_exceptions import HTTPTooManyRequests
from zoneinfo import ZoneInfo  # 导入时区支持
from ai_processing_status import start_periodic_cleanup, ai_processing_status
from collections import defaultdict, deque
from datetime import datetime, timedelta

lock_fd = None

# 全局变量用于存储web服务器引用
web_server_runner = None

# 请求统计
request_stats = {
    'total_requests': 0,
    'webhook_requests': 0,
    'health_checks': 0,
    'dns_queries': 0,
    'invalid_requests': 0,
    'json_errors': 0,
    'last_reset': datetime.now()
}

# 最近请求记录（用于调试）
recent_requests = deque(maxlen=100)

def log_request_stats():
    """记录请求统计信息"""
    if request_stats['total_requests'] > 0:
        logger.info(f"请求统计 - 总计: {request_stats['total_requests']}, "
                   f"Webhook: {request_stats['webhook_requests']}, "
                   f"健康检查: {request_stats['health_checks']}, "
                   f"DNS查询: {request_stats['dns_queries']}, "
                   f"无效请求: {request_stats['invalid_requests']}, "
                   f"JSON错误: {request_stats['json_errors']}")

def record_request(request_type, path, status_code, error=None):
    """记录请求信息"""
    request_stats['total_requests'] += 1
    request_stats[request_type] += 1

    recent_requests.append({
        'timestamp': datetime.now(),
        'type': request_type,
        'path': path,
        'status': status_code,
        'error': str(error) if error else None
    })

    # 每100个请求记录一次统计
    if request_stats['total_requests'] % 100 == 0:
        log_request_stats()

def acquire_lock():
    global lock_fd
    try:
        lock_fd = open(BOT_LOCK_PATH, "w")
        fcntl.lockf(lock_fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
        return lock_fd
    except IOError:
        logger.error("另一个实例正在运行，退出...")
        sys.exit(1)

lock_fd = acquire_lock()

instance_id = uuid.uuid4()
logger.info(f"Bot 实例启动，ID: {instance_id}")

app = init_app()

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_data = await get_user_data(update.effective_user.id) if update and update.effective_user else {}
    lang = user_data.get("preferred_language", "zh-cn")
    logger.error(f"更新 {update} 引发错误: {context.error}", exc_info=True)
    if update and update.effective_message:
        try:
            await update.effective_message.reply_text(
                "贱奴！爷的系统抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! My system crashed, try again later!"
            )
        except TelegramError as e:
            logger.error(f"发送错误提示失败: {str(e)}")

async def daily_report(context: ContextTypes.DEFAULT_TYPE):
    async with aiosqlite.connect(DB_PATH) as conn:
        cursor = await conn.execute("SELECT user_id, daily_new_shares, daily_old_shares, preferred_language FROM users WHERE daily_new_shares > 0 OR daily_old_shares > 0")
        failed_users = []
        for row in await cursor.fetchall():
            user_id, new_shares, old_shares, lang = row
            try:
                # 检查用户聊天是否可用
                await context.bot.get_chat(user_id)
                await context.bot.send_message(
                    user_id,
                    f"今天有{new_shares}只新狗点击你的链接，你获得{new_shares * 5}次文字调教次数；有{old_shares}只老狗点击，无机会"
                    if lang == "zh-cn" else
                    f"Today, {new_shares} new dogs clicked your link, earning you {new_shares * 5} text sessions; {old_shares} old dogs clicked, no rewards."
                )
                user_data = await get_user_data(user_id)
                user_data["daily_new_shares"] = 0
                user_data["daily_old_shares"] = 0
                await save_user_data(user_id, user_data)
                logger.info(f"发送每日报告成功，用户ID: {user_id}, 新分享: {new_shares}, 旧分享: {old_shares}")
            except TelegramError as e:
                logger.error(f"发送每日报告失败，用户ID: {user_id}, 错误: {str(e)}")
                failed_users.append(user_id)
        if failed_users:
            logger.warning(f"每日报告发送失败的用户: {failed_users}")

async def daily_backup(context: ContextTypes.DEFAULT_TYPE):
    try:
        await backup_conversations()
        logger.info("每日数据库备份任务已执行")
    except Exception as e:
        logger.error(f"每日备份失败: {str(e)}", exc_info=True)

async def clean_temp_files(context: ContextTypes.DEFAULT_TYPE):
    """定时清理临时文件（音频、图片、视频等）"""
    try:
        current_time = datetime.now().timestamp()
        deleted_count = 0

        # 检查下载目录是否存在
        if not os.path.exists(DOWNLOAD_DIR):
            logger.warning(f"下载目录不存在: {DOWNLOAD_DIR}")
            return

        for filename in os.listdir(DOWNLOAD_DIR):
            should_delete = False

            # 匹配各种临时文件模式
            # 音频文件
            if filename.endswith((".ogg", ".mp3", ".wav")):
                if (filename.startswith("temp_") or
                    filename.startswith("dice_proof_") or
                    filename.startswith("temp_voice_") or
                    filename.startswith("temp_segment_") or
                    "_compressed" in filename):
                    # 排除漂流瓶相关文件
                    if not filename.startswith("bottle_voice_"):
                        should_delete = True

            # 图片文件
            elif filename.endswith((".jpg", ".jpeg", ".png")):
                if (filename.startswith("dice_proof_") or
                    filename.startswith("temp_") or
                    "_compressed" in filename or
                    filename.startswith("dice_proof_frame_")):
                    should_delete = True

            # 视频文件
            elif filename.endswith((".mp4", ".avi", ".mov")):
                if (filename.startswith("dice_proof_video_") or
                    filename.startswith("temp_") or
                    "_" in filename and any(char.isdigit() for char in filename)):
                    should_delete = True

            if should_delete:
                file_path = os.path.join(DOWNLOAD_DIR, filename)
                try:
                    file_mtime = os.path.getmtime(file_path)
                    # 1小时未使用的文件将被删除
                    if current_time - file_mtime > 3600:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.debug(f"删除过期临时文件: {file_path}")
                except Exception as e:
                    logger.error(f"删除临时文件 {file_path} 失败，错误: {str(e)}")

        logger.info(f"临时文件清理任务完成，删除 {deleted_count} 个文件")
    except Exception as e:
        logger.error(f"临时文件清理任务失败: {str(e)}", exc_info=True)

async def periodic_stats_report(context: ContextTypes.DEFAULT_TYPE):
    """定期统计报告"""
    try:
        log_request_stats()

        # 重置统计（保留总计数）
        current_time = datetime.now()
        time_since_reset = current_time - request_stats['last_reset']

        if time_since_reset.total_seconds() > 3600:  # 每小时重置一次
            logger.info(f"过去 {time_since_reset} 的请求统计已记录，重置计数器")
            for key in request_stats:
                if key != 'last_reset':
                    request_stats[key] = 0
            request_stats['last_reset'] = current_time

    except Exception as e:
        logger.error(f"统计报告任务失败: {str(e)}", exc_info=True)

def register_handlers(app):
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("clearmemory", clear_memory))
    app.add_handler(CommandHandler("ban", ban_user))
    app.add_handler(CommandHandler("pay", pay))
    app.add_handler(CommandHandler("dice", handle_dice))
    app.add_handler(CommandHandler("freecredits", freecredits))
    app.add_handler(CommandHandler("redeem", redeem_code))
    app.add_handler(CommandHandler("restorememory", restorememory))
    app.add_handler(CommandHandler("cleanmemory", cleanmemory))
    app.add_handler(CommandHandler("setdefaultvoice", set_default_voice))
    app.add_handler(CommandHandler("dailyreset", daily_reset))
    app.add_handler(CommandHandler("notice", start_broadcast))  # 注册 /notice 命令
    app.add_handler(CommandHandler("notice_done", finish_broadcast))  # 注册 /notice_done 命令
    app.add_handler(CommandHandler("apistatus", api_status))  # 注册 /apistatus 命令
    app.add_handler(CommandHandler("apihealth", api_health))  # 注册 /apihealth 命令
    app.add_handler(CommandHandler("aiprocessing", ai_processing_stats))  # 注册 /aiprocessing 命令
    app.add_handler(CommandHandler("userinfo", user_info))  # 注册 /userinfo 命令
    app.add_handler(CommandHandler("cleantemp", clean_temp_files_command))  # 注册 /cleantemp 命令
    app.add_handler(CallbackQueryHandler(handle_payment_callback, pattern="^(pay_edit_|confirm_pay_edit_|redeem_code_|go_to_pay_|easy_pay_|confirm_easy_pay_|cancel_payment)"))
    app.add_handler(CallbackQueryHandler(handle_reset_step, pattern="^reset_step_"))
    app.add_handler(CallbackQueryHandler(handle_dice_callbacks, pattern="^(dice_game|next_round|end_game)$"))
    # 确保 confirm_broadcast 和 cancel_broadcast 的处理器优先级高于通用的 button_handler
    app.add_handler(CallbackQueryHandler(confirm_broadcast, pattern="^confirm_broadcast_"))
    app.add_handler(CallbackQueryHandler(cancel_broadcast, pattern="^cancel_broadcast_"))
    app.add_handler(CallbackQueryHandler(button_handler))  # 通用处理器放在最后
    app.add_handler(CallbackQueryHandler(handle_channel_pinned_click, pattern="^channel_pinned_click_"))
    app.add_handler(CallbackQueryHandler(handle_search_group_invite, pattern="^search_group_invite_"))
    app.add_handler(PreCheckoutQueryHandler(pre_checkout_query))
    app.add_handler(MessageHandler(filters.SUCCESSFUL_PAYMENT, successful_payment))
    app.add_handler(MessageHandler(filters.ALL & ~filters.COMMAND, forward_message))
    app.add_handler(CallbackQueryHandler(cancel_pending_payment, pattern="^cancel_pending_payment"))
    
    app.add_error_handler(error_handler)


ai_worker_tasks = []
MIN_WORKERS = 5
MAX_WORKERS = 30
LOAD_THRESHOLD_HIGH = 0.7
LOAD_THRESHOLD_LOW = 0.3
QUEUE_MAXSIZE = 1000

async def monitor_workers(bot, ai_queue):
    # 使用统一的worker函数，不再需要根据配置选择
    worker_func = ai_worker_unified
    worker_type = "统一"
    
    while True:
        current_load = ai_queue.qsize() / ai_queue.maxsize
        current_workers = len([t for t in ai_worker_tasks if not t.done()])
        
        if current_load > LOAD_THRESHOLD_HIGH and current_workers < MAX_WORKERS:
            new_worker_id = len(ai_worker_tasks)
            new_task = task_manager.add_task(worker_func(bot, ai_queue, logger, new_worker_id))
            ai_worker_tasks.append(new_task)
            logger.info(f"队列负载 {current_load:.2%}，增加 {worker_type} AI worker，当前数量: {current_workers + 1}")
            task_manager.add_task(monitor_task(new_task, new_worker_id))
        
        elif current_load < LOAD_THRESHOLD_LOW and current_workers > MIN_WORKERS:
            for task in ai_worker_tasks[:]:
                if not task.done():
                    task.cancel()
                    ai_worker_tasks.remove(task)
                    logger.info(f"队列负载 {current_load:.2%}，减少 {worker_type} AI worker，当前数量: {current_workers - 1}")
                    break
        
        if ai_queue.full():
            logger.warning("AI 队列已满，暂停接收新消息")
            try:
                await bot.send_message(
                    chat_id=MASTER_ID,
                    text="贱奴们！爷忙不过来了，稍后再来跪舔！"
                )
            except TelegramError as e:
                logger.error(f"发送队列满通知失败: {str(e)}")
            await asyncio.sleep(10)
        
        await asyncio.sleep(1)  # 提高监控频率

async def monitor_task(task, worker_id):
    # 使用统一的worker函数
    worker_func = ai_worker_unified
    worker_type = "统一"
    
    try:
        await task
    except asyncio.CancelledError:
        logger.info(f"{worker_type} AI worker {worker_id} 被取消")
    except Exception as e:
        logger.error(f"{worker_type} AI worker {worker_id} 异常退出: {str(e)}", exc_info=True)
        new_task = task_manager.add_task(worker_func(app.bot, global_ai_queue, logger, worker_id))
        ai_worker_tasks[worker_id] = new_task
        task_manager.add_task(monitor_task(new_task, worker_id))

request_semaphore = asyncio.Semaphore(50)

async def health_check_handler(request):
    """健康检查端点"""
    record_request('health_checks', request.path, 200)
    return web.Response(text="OK", status=200)

async def stats_handler(request):
    """统计信息端点"""
    stats_info = {
        'request_stats': request_stats,
        'recent_requests': list(recent_requests)[-10:],  # 最近10个请求
        'queue_size': global_ai_queue.qsize(),
        'queue_maxsize': QUEUE_MAXSIZE,
        'active_workers': len([t for t in ai_worker_tasks if not t.done()])
    }
    return web.json_response(stats_info)

async def webhook_handler(request):
    """Telegram Webhook处理器"""
    async with request_semaphore:
        try:
            start_time = asyncio.get_event_loop().time()

            # 验证请求方法
            if request.method != 'POST':
                logger.warning(f"收到非POST请求: {request.method} {request.path_qs}")
                record_request('invalid_requests', request.path, 405)
                return web.Response(text="Method Not Allowed", status=405)

            # 验证Content-Type
            content_type = request.headers.get('Content-Type', '')
            if not content_type.startswith('application/json'):
                logger.warning(f"收到非JSON请求: Content-Type={content_type}, Path={request.path_qs}")
                record_request('invalid_requests', request.path, 400)
                return web.Response(text="Invalid Content-Type", status=400)

            # 获取请求体
            try:
                request_text = await request.text()
                if not request_text.strip():
                    logger.warning("收到空请求体")
                    return web.Response(text="Empty request body", status=400)

                # 尝试解析JSON
                update_data = json.loads(request_text)

            except json.JSONDecodeError as e:
                # 记录JSON解析错误的详细信息
                logger.error(f"Webhook JSON 解析失败: {str(e)}")
                logger.error(f"原始数据长度: {len(request_text) if 'request_text' in locals() else 'unknown'}")
                logger.error(f"原始数据前100字符: {request_text[:100] if 'request_text' in locals() else 'unknown'}")
                logger.error(f"请求头: {dict(request.headers)}")
                record_request('json_errors', request.path, 400, e)
                return web.Response(text="Invalid JSON", status=400)

            logger.debug(f"收到 Webhook 请求，数据: {json.dumps(update_data, ensure_ascii=False)[:1000]}")

            # 验证是否为有效的Telegram更新
            if not isinstance(update_data, dict) or 'update_id' not in update_data:
                logger.warning(f"收到无效的Telegram更新格式: {json.dumps(update_data, ensure_ascii=False)[:200]}")
                record_request('invalid_requests', request.path, 400)
                return web.Response(text="Invalid Telegram update format", status=400)

            update = Update.de_json(update_data, app.bot)
            if update:
                # 记录支付相关的更新
                if update.pre_checkout_query:
                    logger.info(f"收到 pre_checkout_query Webhook，查询ID: {update.pre_checkout_query.id}，用户ID: {update.pre_checkout_query.from_user.id}")
                elif update.message and update.message.successful_payment:
                    logger.info(f"收到 successful_payment Webhook，支付ID: {update.message.successful_payment.telegram_payment_charge_id}，用户ID: {update.effective_user.id}")

                # 调整队列检查阈值，减少丢弃
                if global_ai_queue.qsize() >= QUEUE_MAXSIZE * 0.9:  # 从 0.8 提高到 0.9
                    logger.warning(f"AI队列接近满载，当前大小: {global_ai_queue.qsize()}, 最大大小: {QUEUE_MAXSIZE}, 拒绝新请求")

                    # 记录被拒绝的用户信息
                    user_info = "未知用户"
                    if update.effective_user:
                        user_info = f"用户ID: {update.effective_user.id}, 用户名: {update.effective_user.username or '无'}"
                    logger.warning(f"队列满载拒绝请求 - {user_info}")

                    # 尝试发送队列满载通知
                    try:
                        await app.bot.send_message(
                            chat_id=update.effective_chat.id,
                            text="贱奴！爷忙得要死，稍后再来跪舔！"
                        )
                    except Exception as notify_error:
                        logger.error(f"发送队列满载通知失败: {str(notify_error)}")

                    return web.Response(text="队列已满", status=429)

                # 记录正常处理的消息
                if update.message:
                    user_info = f"用户ID: {update.effective_user.id}, 用户名: {update.effective_user.username or '无'}"
                    message_type = "未知"
                    if update.message.text:
                        message_type = "文本"
                    elif update.message.photo:
                        message_type = "图片"
                    elif update.message.voice:
                        message_type = "语音"
                    elif update.message.video:
                        message_type = "视频"
                    logger.info(f"处理消息 - {user_info}, 类型: {message_type}, 聊天ID: {update.effective_chat.id}")

                # 异步处理更新
                asyncio.create_task(app.process_update(update))
                elapsed_time = asyncio.get_event_loop().time() - start_time
                logger.info(f"Webhook 处理成功，耗时: {elapsed_time:.2f} 秒")
                record_request('webhook_requests', request.path, 200)
                return web.Response(text="OK", status=200)
            else:
                logger.error(f"无法解析Telegram更新: {json.dumps(update_data, ensure_ascii=False)[:500]}")
                record_request('invalid_requests', request.path, 400)
                return web.Response(text="Invalid Telegram update", status=400)

        except HTTPTooManyRequests:
            logger.warning("Webhook 请求过多，触发限流")
            return web.Response(text="Too Many Requests", status=429)
        except Exception as e:
            logger.error(f"Webhook 处理失败: {str(e)}", exc_info=True)

            # 尝试记录请求的详细信息
            try:
                if 'request_text' not in locals():
                    request_text = await request.text()
                logger.error(f"失败的请求数据: {request_text[:1000]}")
                logger.error(f"请求方法: {request.method}")
                logger.error(f"请求路径: {request.path_qs}")
                logger.error(f"请求头: {dict(request.headers)}")
            except Exception as log_error:
                logger.error(f"无法记录失败请求的数据: {str(log_error)}")

            return web.Response(text="Internal Server Error", status=500)
            
async def set_webhook():
    request = HTTPXRequest(
        connection_pool_size=20,
        connect_timeout=60,
        read_timeout=120,
    )
    bot = Bot(BOT_TOKEN, request=request)
    max_retries = 10
    for attempt in range(max_retries):
        try:
            await bot.set_webhook(url=WEBHOOK_URL)
            logger.info(f"Webhook 设置成功，URL: {WEBHOOK_URL}")
            break
        except TelegramError as e:
            logger.error(f"设置 Webhook 失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(2 ** attempt * 5)  # 更长的指数退避延迟
                continue
            logger.error(f"Webhook 设置最终失败，请检查网络配置或 URL: {WEBHOOK_URL}")
            raise Exception(f"Webhook 设置失败，请检查网络配置: {str(e)}")

async def startup():
    """应用启动时的初始化"""
    logger.info("应用正在启动...")
    try:
        # 检查数据库
        await check_db()
        
        # 初始化增强记忆系统
        try:
            compatibility_result = await check_compatibility()
            logger.info(f"记忆系统兼容性检查: {compatibility_result}")
            
            # 处理布尔值返回或字典返回
            if isinstance(compatibility_result, bool):
                is_compatible = compatibility_result
                reason = None
            else:
                is_compatible = compatibility_result.get("compatible", False)
                reason = compatibility_result.get("reason", "未知原因")
            
            if is_compatible:
                await initialize_enhanced_memory()
                logger.info("增强记忆系统初始化成功")
            else:
                logger.warning(f"记忆系统兼容性检查失败: {reason or '兼容性检查未通过'}")
        except Exception as e:
            logger.error(f"增强记忆系统初始化失败: {str(e)}")
        
        # 启动AI处理状态定期清理任务
        task_manager.add_task(start_periodic_cleanup())
        logger.info("AI处理状态定期清理任务已启动")
        
        # 启动AI工作器
        for i in range(MIN_WORKERS):
            worker_task = task_manager.add_task(ai_worker_unified(app.bot, global_ai_queue, logger, i))
            ai_worker_tasks.append(worker_task)
            task_manager.add_task(monitor_task(worker_task, i))
        
        # 启动工作器监控
        task_manager.add_task(monitor_workers(app.bot, global_ai_queue))
        
        # 启动API保活任务
        task_manager.add_task(start_api_keepalive_task())
        
        # 设置webhook
        if WEBHOOK_URL:
            await set_webhook()
        
        # 初始化并启动Application
        await app.initialize()
        await app.start()
        
        # 注册处理器
        register_handlers(app)
        
        # 启动Web服务器来处理webhook
        web_app = web.Application(client_max_size=1024*1024*10)

        # Telegram webhook端点
        web_app.router.add_post('/webhook', webhook_handler)
        web_app.router.add_post('/', webhook_handler)

        # 健康检查端点
        web_app.router.add_get('/health', health_check_handler)
        web_app.router.add_get('/status', health_check_handler)
        web_app.router.add_get('/ping', health_check_handler)

        # 统计信息端点
        web_app.router.add_get('/stats', stats_handler)

        # DNS查询端点 - 返回404，明确表示不支持
        async def dns_not_supported(request):
            logger.debug(f"收到DNS查询请求: {request.method} {request.path_qs} - 返回404")
            record_request('dns_queries', request.path, 404)
            return web.Response(text="DNS queries not supported", status=404)

        web_app.router.add_route('*', '/dns-query', dns_not_supported)
        web_app.router.add_route('*', '/query', dns_not_supported)
        web_app.router.add_route('*', '/resolve', dns_not_supported)

        # 通用404处理器
        async def handle_404(request):
            logger.debug(f"404请求: {request.method} {request.path_qs}")
            record_request('invalid_requests', request.path, 404)
            return web.Response(text="Not Found", status=404)

        # 添加通用路由处理未匹配的请求
        web_app.router.add_route('*', '/{path:.*}', handle_404)

        runner = web.AppRunner(web_app)
        await runner.setup()
        site = web.TCPSite(runner, SERVER_HOST, SERVER_PORT)
        await site.start()
        logger.info(f"Webhook 服务器启动在 {SERVER_HOST}:{SERVER_PORT}")
        logger.info("已配置路由: /webhook (POST), /health (GET), /status (GET), /ping (GET)")
        logger.info("DNS查询端点已配置为返回404: /dns-query, /query, /resolve")
        
        # 存储web服务器引用
        global web_server_runner
        web_server_runner = runner
        
        # 添加定时任务
        job_queue = app.job_queue
        job_queue.run_daily(daily_report, time=time(hour=0, minute=1, tzinfo=ZoneInfo("Asia/Shanghai")), name="daily_report")
        job_queue.run_daily(daily_backup, time=time(hour=2, minute=0, tzinfo=ZoneInfo("Asia/Shanghai")), name="daily_backup")
        job_queue.run_repeating(clean_temp_files, interval=3600, first=10, name="clean_temp_files")
        job_queue.run_repeating(periodic_stats_report, interval=1800, first=60, name="stats_report")  # 每30分钟统计一次

        # 启动漂流瓶定期清理任务
        from handlers.drift_bottle import periodic_cleanup
        task_manager.add_task(periodic_cleanup(app.bot))
        
        logger.info("AI爷的统治开始，Bot 已切换至 Webhook 模式...")
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}", exc_info=True)
        raise

async def shutdown():
    logger.info("Bot 正在关闭...")
    
    # 关闭web服务器
    global web_server_runner
    if web_server_runner:
        try:
            await web_server_runner.cleanup()
            logger.info("Web服务器已关闭")
        except Exception as e:
            logger.error(f"关闭Web服务器失败: {str(e)}")
    
    for task in ai_worker_tasks:
        if not task.done():
            task.cancel()
    await asyncio.gather(*[t for t in ai_worker_tasks if not t.done()], return_exceptions=True)
    await task_manager.shutdown()
    if app.job_queue:
        app.job_queue.stop()
    while not global_ai_queue.empty():
        try:
            global_ai_queue.get_nowait()
            global_ai_queue.task_done()
        except asyncio.QueueEmpty:
            break
    await app.stop()
    await app.shutdown()
    try:
        tasks = [task for task in asyncio.all_tasks() if task is not asyncio.current_task()]
        for task in tasks:
            task.cancel()
        await asyncio.gather(*tasks, return_exceptions=True)
    except Exception as e:
        logger.error(f"清理异步任务失败: {str(e)}", exc_info=True)
    logger.info("所有任务已清理，Bot 已关闭")

    # 清理web服务器引用
    web_server_runner = None

def main():
    loop = asyncio.get_event_loop()
    
    loop.run_until_complete(startup())

    def signal_handler(sig, frame):
        logger.info("Bot 被手动终止")
        loop.run_until_complete(shutdown())
        loop.close()
        lock_fd.close()
        if os.path.exists(BOT_LOCK_PATH):
            os.remove(BOT_LOCK_PATH)
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        loop.run_forever()
    except Exception as e:
        logger.error(f"Bot 运行时发生错误: {str(e)}")
        loop.run_until_complete(shutdown())
    finally:
        if not loop.is_closed():
            loop.close()
        lock_fd.close()
        if os.path.exists(BOT_LOCK_PATH):
            os.remove(BOT_LOCK_PATH)
        sys.exit(0)

if __name__ == "__main__":
    main()
