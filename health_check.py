#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bot健康检查和监控脚本
用于检测Bot是否正常运行，AI队列状态，以及各种潜在问题
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime, timedelta
from config import BOT_TOKEN, WEBHOOK_URL, MASTER_ID
from globals import global_ai_queue
from logging_config import logger
import os

# 尝试导入psutil，如果不存在则禁用系统资源检查
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil模块未安装，系统资源检查功能将被禁用。")
    print("安装命令: pip install psutil")

class BotHealthChecker:
    def __init__(self):
        self.bot_token = BOT_TOKEN
        self.webhook_url = WEBHOOK_URL
        self.master_id = MASTER_ID
        self.last_check_time = datetime.now()
        self.check_interval = 60  # 检查间隔（秒）
        
    async def check_bot_status(self):
        """检查Bot是否在线"""
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("ok"):
                            logger.info(f"Bot状态检查 - 在线，Bot用户名: {data['result']['username']}")
                            return True, data['result']['username']
                    logger.error(f"Bot状态检查失败 - HTTP状态: {response.status}")
                    return False, None
        except Exception as e:
            logger.error(f"Bot状态检查异常: {str(e)}")
            return False, None
    
    async def check_webhook_status(self):
        """检查Webhook状态"""
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/getWebhookInfo"
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("ok"):
                            webhook_info = data['result']
                            logger.info(f"Webhook状态 - URL: {webhook_info.get('url', '未设置')}, "
                                      f"待处理更新: {webhook_info.get('pending_update_count', 0)}, "
                                      f"最后错误: {webhook_info.get('last_error_message', '无')}")
                            return webhook_info
                    return {}
        except Exception as e:
            logger.error(f"Webhook状态检查异常: {str(e)}")
            return {}
    
    def check_queue_status(self):
        """检查AI队列状态"""
        try:
            queue_size = global_ai_queue.qsize()
            max_size = global_ai_queue.maxsize
            usage_percent = (queue_size / max_size) * 100 if max_size > 0 else 0
            
            status = "正常"
            if usage_percent > 80:
                status = "高负载"
            elif usage_percent > 95:
                status = "严重拥堵"
            
            logger.info(f"AI队列状态 - 当前: {queue_size}, 最大: {max_size}, "
                       f"使用率: {usage_percent:.1f}%, 状态: {status}")
            
            return {
                "current_size": queue_size,
                "max_size": max_size,
                "usage_percent": usage_percent,
                "status": status
            }
        except Exception as e:
            logger.error(f"队列状态检查异常: {str(e)}")
            return {"error": str(e)}
    
    def check_system_resources(self):
        """检查系统资源使用情况"""
        try:
            if not PSUTIL_AVAILABLE:
                logger.warning("psutil不可用，跳过系统资源检查")
                return {
                    "cpu_percent": 0,
                    "memory_percent": 0,
                    "disk_percent": 0,
                    "process_memory_mb": 0,
                    "process_cpu": 0,
                    "warnings": ["psutil模块未安装，无法获取系统资源信息"]
                }
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 当前进程信息
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            process_cpu = process.cpu_percent()
            
            logger.info(f"系统资源 - CPU: {cpu_percent}%, 内存: {memory_percent}%, "
                       f"磁盘: {disk_percent}%, 进程内存: {process_memory:.1f}MB")
            
            warnings = []
            if cpu_percent > 80:
                warnings.append(f"CPU使用率过高: {cpu_percent}%")
            if memory_percent > 85:
                warnings.append(f"内存使用率过高: {memory_percent}%")
            if disk_percent > 90:
                warnings.append(f"磁盘使用率过高: {disk_percent}%")
            if process_memory > 1000:  # 1GB
                warnings.append(f"Bot进程内存使用过高: {process_memory:.1f}MB")
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent,
                "process_memory_mb": process_memory,
                "process_cpu": process_cpu,
                "warnings": warnings
            }
        except Exception as e:
            logger.error(f"检查系统资源失败: {str(e)}")
            return {
                "cpu_percent": 0,
                "memory_percent": 0,
                "disk_percent": 0,
                "process_memory_mb": 0,
                "process_cpu": 0,
                "warnings": [f"系统资源检查失败: {str(e)}"]
            }
    
    def check_log_file_size(self):
        """检查日志文件大小"""
        try:
            log_file = "master_bot.log"
            if os.path.exists(log_file):
                size_bytes = os.path.getsize(log_file)
                size_mb = size_bytes / 1024 / 1024
                
                # 检查是否需要轮转
                if size_mb > 100:  # 超过100MB
                    logger.warning(f"日志文件过大: {size_mb:.1f}MB，建议轮转")
                
                logger.info(f"日志文件大小: {size_mb:.1f}MB")
                return {"size_mb": size_mb, "needs_rotation": size_mb > 100}
            else:
                logger.warning("日志文件不存在")
                return {"error": "日志文件不存在"}
        except Exception as e:
            logger.error(f"日志文件检查异常: {str(e)}")
            return {"error": str(e)}
    
    async def send_alert_to_master(self, message):
        """向管理员发送警报"""
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                "chat_id": self.master_id,
                "text": f"🚨 Bot健康检查警报 🚨\n\n{message}",
                "parse_mode": "Markdown"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data, timeout=10) as response:
                    if response.status == 200:
                        logger.info("健康检查警报已发送给管理员")
                        return True
                    else:
                        logger.error(f"发送警报失败，HTTP状态: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"发送警报异常: {str(e)}")
            return False
    
    async def comprehensive_health_check(self):
        """综合健康检查"""
        logger.info("开始综合健康检查...")
        
        issues = []
        
        # 1. 检查Bot状态
        bot_online, bot_username = await self.check_bot_status()
        if not bot_online:
            issues.append("❌ Bot离线或无法访问")
        
        # 2. 检查Webhook
        webhook_info = await self.check_webhook_status()
        if webhook_info:
            pending_updates = webhook_info.get('pending_update_count', 0)
            if pending_updates > 10:
                issues.append(f"⚠️ Webhook有 {pending_updates} 个待处理更新")
            
            last_error = webhook_info.get('last_error_message')
            if last_error:
                issues.append(f"⚠️ Webhook最后错误: {last_error}")
        
        # 3. 检查队列状态
        queue_status = self.check_queue_status()
        if not queue_status.get("error"):
            if queue_status["usage_percent"] > 90:
                issues.append(f"🔥 AI队列严重拥堵: {queue_status['usage_percent']:.1f}%")
            elif queue_status["usage_percent"] > 70:
                issues.append(f"⚠️ AI队列高负载: {queue_status['usage_percent']:.1f}%")
        
        # 4. 检查系统资源
        system_resources = self.check_system_resources()
        if not system_resources.get("error"):
            if system_resources["memory_percent"] > 90:
                issues.append(f"💾 内存使用过高: {system_resources['memory_percent']:.1f}%")
            if system_resources["cpu_percent"] > 80:
                issues.append(f"⚡ CPU使用过高: {system_resources['cpu_percent']:.1f}%")
            if system_resources["disk_percent"] > 90:
                issues.append(f"💿 磁盘空间不足: {system_resources['disk_percent']:.1f}%")
        
        # 5. 检查日志文件
        log_status = self.check_log_file_size()
        if not log_status.get("error") and log_status.get("needs_rotation"):
            issues.append(f"📄 日志文件过大: {log_status['size_mb']:.1f}MB")
        
        # 如果有问题，发送警报
        if issues:
            alert_message = "\n".join(issues)
            await self.send_alert_to_master(alert_message)
            logger.warning(f"发现 {len(issues)} 个问题")
        else:
            logger.info("健康检查通过，所有系统正常")
        
        return len(issues) == 0
    
    async def start_monitoring(self):
        """开始监控循环"""
        logger.info("开始Bot健康监控...")
        
        while True:
            try:
                await self.comprehensive_health_check()
                await asyncio.sleep(self.check_interval)
            except KeyboardInterrupt:
                logger.info("健康监控被手动停止")
                break
            except Exception as e:
                logger.error(f"健康监控异常: {str(e)}")
                await asyncio.sleep(30)  # 出错后等待30秒再继续

# 独立运行健康检查
async def run_health_check():
    """运行一次性健康检查"""
    checker = BotHealthChecker()
    await checker.comprehensive_health_check()

if __name__ == "__main__":
    # 可以作为独立脚本运行
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        # 持续监控模式
        checker = BotHealthChecker()
        asyncio.run(checker.start_monitoring())
    else:
        # 单次检查模式
        asyncio.run(run_health_check()) 