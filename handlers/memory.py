from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from logging_config import logger
from database import get_user_data, save_user_data, clear_conversation_memory, clear_all_conversations, restore_user_conversation
from utils import get_main_menu
from config import MASTER_ID


async def clear_memory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /clearmemory 命令，普通用户清除自己的记忆，管理员清除所有记忆"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if str(user_id) == MASTER_ID:
        # 管理员清除所有用户记忆
        keyboard = [
            [InlineKeyboardButton("确认清空所有记忆" if lang == "zh-cn" else "Confirm Clear All Memory", callback_data="confirm_clear_all_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "至高无上的爹！您要清空所有贱奴的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Supreme Master! Clear all filthy dogs' brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送管理员清空所有记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        # 普通用户清除自己的记忆
        keyboard = [
            [InlineKeyboardButton("确认清空我的记忆" if lang == "zh-cn" else "Confirm Clear My Memory", callback_data="confirm_clear_user_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "贱奴！你要清空你的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Filthy dog! Clear your brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送用户清空记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_clear_all_memory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if str(user_id) == MASTER_ID:
        try:
            await clear_all_conversations()
            await query.edit_message_text(
                "所有贱奴的狗脑子对话记录已被爷截断！"
                if lang == "zh-cn" else
                "All filthy dogs' brainless chat records have been wiped by me!",
                reply_markup=get_main_menu(user_id)
            )
        except Exception as e:
            logger.error(f"清空所有记忆失败，用户ID: {user_id}, 错误: {str(e)}")
            await query.edit_message_text(
                "爹！清空记忆时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Master! Something went wrong clearing memories, try again later!"
            )


async def handle_clear_user_memory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        await clear_conversation_memory(user_id)
        await query.edit_message_text(
            "贱奴！你的狗脑子对话记录已被爷截断！"
            if lang == "zh-cn" else
            "Filthy dog! Your brainless chat records have been wiped by me!",
            reply_markup=get_main_menu(user_id)
        )
    except Exception as e:
        logger.error(f"清空用户记忆失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！清空记忆时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong clearing your memory, try again later!"
        )


async def handle_cancel_clear_memory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        await query.edit_message_text(
            "贱奴！清空记忆取消了，滚回去吧！"
            if lang == "zh-cn" else
            "Filthy dog! Memory clear canceled, get lost!",
            reply_markup=get_main_menu(user_id)
        )
    except TelegramError as e:
        logger.error(f"发送取消清空记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def restorememory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /restorememory 命令，恢复用户对话记忆"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if str(user_id) == MASTER_ID:
        # 管理员恢复所有用户记忆
        keyboard = [
            [InlineKeyboardButton("确认恢复所有记忆" if lang == "zh-cn" else "Confirm Restore All Memory", callback_data="confirm_restore_all_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_restore_memory")]
        ]
        try:
            await update.message.reply_text(
                "至高无上的爹！您要恢复所有贱奴的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Supreme Master! Restore all filthy dogs' brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送管理员恢复所有记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        # 普通用户恢复自己的记忆
        success, message = await restore_user_conversation(user_id)
        try:
            await update.message.reply_text(
                message,
                reply_markup=get_main_menu(user_id)
            )
        except TelegramError as e:
            logger.error(f"发送用户恢复记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def cleanmemory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /cleanmemory 命令，清理用户对话记忆"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if str(user_id) == MASTER_ID:
        # 管理员清理所有用户记忆
        keyboard = [
            [InlineKeyboardButton("确认清理所有记忆" if lang == "zh-cn" else "Confirm Clean All Memory", callback_data="confirm_clear_all_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "至高无上的爹！您要清理所有贱奴的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Supreme Master! Clean all filthy dogs' brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送管理员清理所有记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        # 普通用户清理自己的记忆
        keyboard = [
            [InlineKeyboardButton("确认清理我的记忆" if lang == "zh-cn" else "Confirm Clean My Memory", callback_data="confirm_clear_user_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "贱奴！你要清理你的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Filthy dog! Clean your brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送用户清理记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")
