# handlers/drift_bottle.py
import asyncio
import time
import os
import uuid
import re
import json
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from logging_config import logger
from database import (
    get_user_data, save_user_data, save_drift_bottle, update_drift_bottle,
    get_drift_bottle, get_waiting_bottles, get_user_bottles,
    cleanup_expired_bottles, get_bottle_stats, can_user_send_bottle_today,
    increment_user_daily_bottle_count, get_user_daily_bottle_limit,
    can_user_pick_bottle_today, increment_user_daily_pick_count,
    get_user_daily_pick_limit, get_pickable_bottles, get_unviewed_bottles_24h
)
from utils import get_main_menu
from config import DOWNLOAD_DIR

# 用户状态管理
user_states = {}

# 漂流瓶状态常量
BOTTLE_STATE_WAITING = "waiting"  # 等待被捡到
BOTTLE_STATE_DELIVERED = "delivered"  # 已投递给用户
BOTTLE_STATE_VIEWED = "viewed"  # 已被查看
BOTTLE_STATE_REJECTED = "rejected"  # 被拒绝查看
BOTTLE_STATE_EXPIRED = "expired"  # 已过期
BOTTLE_STATE_REPLIED = "replied"  # 已被回复
BOTTLE_STATE_COMPLETED = "completed"  # 流程完成

# 用户状态超时时间（5分钟）
USER_STATE_TIMEOUT = 300


class DriftBottle:
    """漂流瓶类 - 使用数据库存储"""
    def __init__(self, sender_id, voice_message_id, chat_id, created_at=None):
        self.id = str(uuid.uuid4())
        self.sender_id = sender_id
        self.voice_message_id = voice_message_id  # Telegram消息ID
        self.chat_id = chat_id  # 发送者的chat_id
        self.created_at = created_at or datetime.now()
        self.state = BOTTLE_STATE_WAITING
        self.delivery_count = 0  # 投递次数
        self.max_deliveries = 3  # 最大投递次数
        self.current_recipient = None  # 当前接收者
        self.delivery_history = []  # 投递历史
        self.reply_message_id = None  # 回复消息ID
        self.reply_chat_id = None  # 回复者的chat_id
        self.replied_by = None  # 回复者ID
        self.expires_at = self.created_at + timedelta(weeks=1)  # 过期时间
        self.conversation_id = None  # 对话ID，用于多轮对话
        self.is_conversation_active = False  # 是否为活跃对话
        self.last_delivered_at = None  # 最后投递时间

    @classmethod
    def from_dict(cls, data):
        """从字典创建漂流瓶对象"""
        bottle = cls.__new__(cls)
        bottle.id = data['id']
        bottle.sender_id = data['sender_id']
        bottle.voice_message_id = data['voice_message_id']
        bottle.chat_id = data['chat_id']
        bottle.created_at = datetime.fromisoformat(data['created_at'])
        bottle.state = data['state']
        bottle.delivery_count = data['delivery_count']
        bottle.max_deliveries = data['max_deliveries']
        bottle.current_recipient = data['current_recipient']
        bottle.delivery_history = json.loads(data['delivery_history']) if data['delivery_history'] else []
        bottle.reply_message_id = data['reply_message_id']
        bottle.reply_chat_id = data['reply_chat_id']
        bottle.replied_by = data['replied_by']
        bottle.expires_at = datetime.fromisoformat(data['expires_at'])
        bottle.conversation_id = data.get('conversation_id')
        bottle.is_conversation_active = bool(data.get('is_conversation_active', 0))
        bottle.last_delivered_at = datetime.fromisoformat(data['last_delivered_at']) if data.get('last_delivered_at') else None
        return bottle

    def to_dict(self):
        """转换为字典用于数据库存储"""
        return {
            'id': self.id,
            'sender_id': self.sender_id,
            'voice_message_id': self.voice_message_id,
            'chat_id': self.chat_id,
            'created_at': self.created_at.isoformat(),
            'state': self.state,
            'delivery_count': self.delivery_count,
            'max_deliveries': self.max_deliveries,
            'current_recipient': self.current_recipient,
            'delivery_history': json.dumps(self.delivery_history),
            'reply_message_id': self.reply_message_id,
            'reply_chat_id': self.reply_chat_id,
            'replied_by': self.replied_by,
            'expires_at': self.expires_at.isoformat(),
            'conversation_id': self.conversation_id,
            'is_conversation_active': int(self.is_conversation_active),
            'last_delivered_at': self.last_delivered_at.isoformat() if self.last_delivered_at else None
        }
    
    def is_expired(self):
        """检查是否过期（一周）"""
        return datetime.now() > self.expires_at
    
    def can_deliver(self):
        """检查是否可以投递"""
        return (not self.is_expired() and
                self.delivery_count < self.max_deliveries and
                self.state in [BOTTLE_STATE_WAITING, BOTTLE_STATE_REJECTED])
    
    async def deliver_to(self, recipient_id):
        """投递给指定用户"""
        if not self.can_deliver():
            return False

        self.current_recipient = recipient_id
        self.state = BOTTLE_STATE_DELIVERED
        self.delivery_count += 1
        self.last_delivered_at = datetime.now()

        # 记录投递历史
        delivery_record = {
            'recipient': recipient_id,
            'delivered_at': self.last_delivered_at.isoformat(),
            'action': 'delivered'
        }
        self.delivery_history.append(delivery_record)

        # 更新数据库
        await update_drift_bottle(self.id, {
            'current_recipient': self.current_recipient,
            'state': self.state,
            'delivery_count': self.delivery_count,
            'delivery_history': json.dumps(self.delivery_history),
            'last_delivered_at': self.last_delivered_at.isoformat()
        })

        return True
    
    async def mark_viewed(self):
        """标记为已查看"""
        self.state = BOTTLE_STATE_VIEWED
        if self.delivery_history:
            self.delivery_history[-1]['action'] = 'viewed'

        # 更新数据库
        await update_drift_bottle(self.id, {
            'state': self.state,
            'delivery_history': json.dumps(self.delivery_history)
        })

    async def mark_rejected(self):
        """标记为被拒绝"""
        if self.delivery_history:
            self.delivery_history[-1]['action'] = 'rejected'
        # 重置状态以便下次投递
        self.state = BOTTLE_STATE_WAITING
        self.current_recipient = None

        # 更新数据库
        await update_drift_bottle(self.id, {
            'state': self.state,
            'current_recipient': self.current_recipient,
            'delivery_history': json.dumps(self.delivery_history)
        })

    async def mark_replied(self, reply_message_id, reply_chat_id, replied_by):
        """标记为已回复"""
        self.state = BOTTLE_STATE_REPLIED
        self.reply_message_id = reply_message_id
        self.reply_chat_id = reply_chat_id
        self.replied_by = replied_by
        if self.delivery_history:
            self.delivery_history[-1]['action'] = 'replied'

        # 更新数据库
        await update_drift_bottle(self.id, {
            'state': self.state,
            'reply_message_id': self.reply_message_id,
            'reply_chat_id': self.reply_chat_id,
            'replied_by': self.replied_by,
            'delivery_history': json.dumps(self.delivery_history)
        })

    async def mark_completed(self):
        """标记为流程完成"""
        self.state = BOTTLE_STATE_COMPLETED

        # 更新数据库
        await update_drift_bottle(self.id, {
            'state': self.state
        })

    async def save(self):
        """保存到数据库"""
        await save_drift_bottle(self.to_dict())


async def get_drift_bottle_menu(user_id):
    """获取漂流瓶主菜单"""
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 获取每日使用情况
    try:
        # 获取扔瓶子使用情况
        can_send, sent_count, send_limit = await can_user_send_bottle_today(user_id)
        # 获取捞瓶子使用情况
        can_pick, pick_count, pick_limit = await can_user_pick_bottle_today(user_id)

        # 检查会员状态
        is_member = await check_user_membership(user_id)
        user_type = "会员" if is_member else "普通用户"
        user_type_en = "Member" if is_member else "Regular User"

        menu_text = (
            f"🍾 **漂流瓶** 🍾\n"
            f"───────────────────\n"
            f"在这里你可以扔出语音漂流瓶，\n"
            f"也可以捞取其他人的漂流瓶！\n"
            f"───────────────────\n"
            f"👤 用户类型：{user_type}\n"
            f"🚀 今日扔瓶子：{sent_count}/{send_limit} 个\n"
            f"🎣 今日捞瓶子：{pick_count}/{pick_limit} 个\n"
            f"───────────────────\n"
            f"🔥 选择你的操作："
            if lang == "zh-cn" else
            f"🍾 **Drift Bottle** 🍾\n"
            f"───────────────────\n"
            f"Here you can throw voice drift bottles,\n"
            f"and pick up bottles from others!\n"
            f"───────────────────\n"
            f"👤 User Type: {user_type_en}\n"
            f"🚀 Today Thrown: {sent_count}/{send_limit}\n"
            f"🎣 Today Picked: {pick_count}/{pick_limit}\n"
            f"───────────────────\n"
            f"🔥 Choose your action:"
        )
    except Exception as e:
        logger.error(f"获取漂流瓶菜单数据失败，用户ID: {user_id}, 错误: {str(e)}")
        # 降级到简单菜单
        menu_text = (
            "🍾 **漂流瓶** 🍾\n"
            "───────────────────\n"
            "在这里你可以扔出语音漂流瓶，\n"
            "也可以捞取其他人的漂流瓶！\n"
            "───────────────────\n"
            "🔥 选择你的操作："
            if lang == "zh-cn" else
            "🍾 **Drift Bottle** 🍾\n"
            "───────────────────\n"
            "Here you can throw voice drift bottles,\n"
            "and pick up bottles from others!\n"
            "───────────────────\n"
            "🔥 Choose your action:"
        )

    keyboard = [
        [InlineKeyboardButton("🚀 扔瓶子" if lang == "zh-cn" else "🚀 Throw Bottle",
                             callback_data=f"throw_bottle_{user_id}")],
        [InlineKeyboardButton("🎣 捞瓶子" if lang == "zh-cn" else "🎣 Pick Bottle",
                             callback_data=f"pick_bottle_{user_id}")],
        [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                             callback_data="back_to_main")]
    ]

    return menu_text, InlineKeyboardMarkup(keyboard)


async def handle_drift_bottle_menu(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理漂流瓶主菜单"""
    query = update.callback_query
    user_id = query.from_user.id
    
    try:
        menu_text, keyboard = await get_drift_bottle_menu(user_id)
        await query.edit_message_text(
            menu_text,
            reply_markup=keyboard,
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 打开漂流瓶菜单")
    except TelegramError as e:
        logger.error(f"显示漂流瓶菜单失败，用户ID: {user_id}, 错误: {str(e)}")


def check_user_state_timeout(user_id):
    """检查用户状态是否超时"""
    if user_id not in user_states:
        return False

    started_at = user_states[user_id].get('started_at')
    if not started_at:
        return False

    elapsed = (datetime.now() - started_at).total_seconds()
    return elapsed > USER_STATE_TIMEOUT


def cleanup_user_state(user_id):
    """清理用户状态"""
    if user_id in user_states:
        del user_states[user_id]
        logger.info(f"清理用户 {user_id} 的状态")


def contains_numbers(text):
    """检测文本中是否包含数字（阿拉伯数字和中文数字）"""
    if not text:
        return False

    # 阿拉伯数字检测
    if re.search(r'\d', text):
        return True

    # 中文数字检测（包括大写和小写）
    chinese_numbers = [
        '零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
        '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾',
        '百', '千', '万', '亿', '佰', '仟', '萬', '億',
        '两', '兩', '廿', '卅'
    ]

    for num in chinese_numbers:
        if num in text:
            return True

    return False


async def check_user_membership(user_id):
    """检查用户是否为会员"""
    try:
        user_data = await get_user_data(user_id)

        # 首先检查是否为永久会员
        if user_data.get("permanent_member", False):
            return True

        # 检查会员到期时间
        membership_expiry = user_data.get("membership_expiry")
        if membership_expiry:
            try:
                expiry_date = datetime.fromisoformat(membership_expiry)
                if datetime.now() < expiry_date:
                    return True
            except (ValueError, TypeError) as e:
                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {membership_expiry}，错误: {str(e)}")

        return False
    except Exception as e:
        logger.error(f"检查用户会员状态失败，用户ID: {user_id}, 错误: {str(e)}")
        return False


async def transcribe_and_check_voice(voice_path, user_id, lang="zh-cn"):
    """转录语音并检查是否包含数字"""
    try:
        # 导入语音转文字功能
        from utils import transcribe_voice

        # 转录语音
        transcribed_text = await transcribe_voice(voice_path)

        if not transcribed_text:
            logger.warning(f"用户 {user_id} 语音转录失败")
            return None, False, ""

        logger.info(f"用户 {user_id} 语音转录结果: {transcribed_text}")

        # 检查是否包含数字
        has_numbers = contains_numbers(transcribed_text)

        return transcribed_text, has_numbers, transcribed_text

    except Exception as e:
        logger.error(f"语音转录和检查失败，用户ID: {user_id}, 错误: {str(e)}")
        return None, False, ""


async def handle_throw_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理扔瓶子"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 检查每日限制
        can_send, current_count, daily_limit = await can_user_send_bottle_today(user_id)

        if not can_send:
            is_member = await check_user_membership(user_id)

            if is_member:
                # 会员已达上限，使用弹窗提示
                await query.answer(
                    f"🚫 会员每日限制已达上限！今日已发送：{current_count}/{daily_limit} 个漂流瓶，明天再来吧！"
                    if lang == "zh-cn" else
                    f"🚫 Member daily limit reached! Today sent: {current_count}/{daily_limit} bottles, come back tomorrow!",
                    show_alert=True
                )
            else:
                # 普通用户已达上限，发送新消息提示
                await context.bot.send_message(
                    chat_id=user_id,
                    text=(
                        f"🚫 **每日限制已达上限** 🚫\n"
                        f"───────────────────\n"
                        f"今日已发送：{current_count}/{daily_limit} 个漂流瓶\n"
                        f"普通用户每日只能发送1个漂流瓶\n"
                        f"───────────────────\n"
                        f"💎 升级会员可每日发送3个！"
                        if lang == "zh-cn" else
                        f"🚫 **Daily Limit Reached** 🚫\n"
                        f"───────────────────\n"
                        f"Today sent: {current_count}/{daily_limit} bottles\n"
                        f"Regular users can only send 1 bottle daily\n"
                        f"───────────────────\n"
                        f"💎 Upgrade to member for 3 bottles daily!"
                    ),
                    parse_mode="Markdown"
                )

                # 使用弹窗简短提示
                await query.answer(
                    "🚫 每日限制已达上限！详情请查看上方消息。"
                    if lang == "zh-cn" else
                    "🚫 Daily limit reached! Check the message above for details.",
                    show_alert=True
                )
            return

        # 清理可能存在的旧状态
        cleanup_user_state(user_id)

        # 设置用户状态为等待语音消息
        user_states[user_id] = {
            'state': 'waiting_voice',
            'started_at': datetime.now()
        }
        
        text = (
            "🎤 **请发送语音消息** 🎤\n"
            "───────────────────\n"
            "请发送一条语音消息作为你的漂流瓶内容。\n"
            "⚠️ 注意：只支持语音消息！\n"
            "───────────────────\n"
            "发送完成后，我会让你确认是否投放。"
            if lang == "zh-cn" else
            "🎤 **Please send voice message** 🎤\n"
            "───────────────────\n"
            "Please send a voice message as your drift bottle content.\n"
            "⚠️ Note: Only voice messages are supported!\n"
            "───────────────────\n"
            "After sending, I'll ask you to confirm the release."
        )
        
        keyboard = [
            [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel", 
                                 callback_data="cancel_throw_bottle")]
        ]
        
        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )
        
        logger.info(f"用户 {user_id} 开始扔瓶子，等待语音消息")
        
    except TelegramError as e:
        logger.error(f"处理扔瓶子失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_cancel_throw_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理取消扔瓶子"""
    query = update.callback_query
    user_id = query.from_user.id
    
    # 清除用户状态
    if user_id in user_states:
        del user_states[user_id]
    
    # 返回漂流瓶主菜单
    await handle_drift_bottle_menu(update, context)
    logger.info(f"用户 {user_id} 取消扔瓶子")


async def handle_pick_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理捞瓶子"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 检查每日捞瓶子限制
        can_pick, current_count, daily_limit = await can_user_pick_bottle_today(user_id)

        if not can_pick:
            is_member = await check_user_membership(user_id)

            if is_member:
                # 会员已达上限，使用弹窗提示
                await query.answer(
                    f"🚫 会员每日捞瓶子限制已达上限！今日已捞取：{current_count}/{daily_limit} 个漂流瓶，明天再来吧！"
                    if lang == "zh-cn" else
                    f"🚫 Member daily pick limit reached! Today picked: {current_count}/{daily_limit} bottles, come back tomorrow!",
                    show_alert=True
                )
            else:
                # 普通用户已达上限，发送新消息提示
                await context.bot.send_message(
                    chat_id=user_id,
                    text=(
                        f"🚫 **每日捞瓶子限制已达上限** 🚫\n"
                        f"───────────────────\n"
                        f"今日已捞取：{current_count}/{daily_limit} 个漂流瓶\n"
                        f"普通用户每日只能捞取1个漂流瓶\n"
                        f"───────────────────\n"
                        f"💎 升级会员可每日捞取3个！"
                        if lang == "zh-cn" else
                        f"🚫 **Daily Pick Limit Reached** 🚫\n"
                        f"───────────────────\n"
                        f"Today picked: {current_count}/{daily_limit} bottles\n"
                        f"Regular users can only pick 1 bottle daily\n"
                        f"───────────────────\n"
                        f"💎 Upgrade to member for 3 bottles daily!"
                    ),
                    parse_mode="Markdown"
                )

                # 使用弹窗简短提示
                await query.answer(
                    "🚫 每日捞瓶子限制已达上限！详情请查看上方消息。"
                    if lang == "zh-cn" else
                    "🚫 Daily pick limit reached! Check the message above for details.",
                    show_alert=True
                )
            return

        # 获取可捞取的漂流瓶
        pickable_bottles = await get_pickable_bottles(user_id, limit=20)

        if not pickable_bottles:
            text = (
                "🎣 **没有可捞取的漂流瓶** 🎣\n"
                "───────────────────\n"
                "暂时没有找到可捞取的漂流瓶\n"
                "可能的原因：\n"
                "• 7天内没有新的漂流瓶\n"
                "• 所有瓶子都已被回复\n"
                "───────────────────\n"
                "🚀 试试扔个瓶子吧！"
                if lang == "zh-cn" else
                "🎣 **No Bottles Available** 🎣\n"
                "───────────────────\n"
                "No pickable bottles found at the moment\n"
                "Possible reasons:\n"
                "• No new bottles in the past 7 days\n"
                "• All bottles have been replied to\n"
                "───────────────────\n"
                "🚀 Try throwing a bottle!"
            )

            keyboard = [
                [InlineKeyboardButton("🚀 扔瓶子" if lang == "zh-cn" else "🚀 Throw Bottle",
                                     callback_data=f"throw_bottle_{user_id}")],
                [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                     callback_data="back_to_main")]
            ]

            await query.edit_message_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode="Markdown"
            )
            return

        # 随机选择一个漂流瓶
        import random
        selected_bottle_data = random.choice(pickable_bottles)
        bottle = DriftBottle.from_dict(selected_bottle_data)

        # 增加每日捞瓶子计数
        await increment_user_daily_pick_count(user_id)

        # 标记漂流瓶为已投递给当前用户
        await bottle.deliver_to(user_id)

        # 发送漂流瓶给用户
        success = await deliver_picked_bottle_to_user(bottle, user_id, context.bot)

        if success:
            logger.info(f"用户 {user_id} 成功捞取漂流瓶 {bottle.id}")
        else:
            logger.warning(f"用户 {user_id} 捞取漂流瓶 {bottle.id} 失败")

    except Exception as e:
        logger.error(f"处理捞瓶子失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("捞瓶子时出错！" if lang == "zh-cn" else "Error picking bottle!")


async def deliver_picked_bottle_to_user(bottle, user_id, bot):
    """投递捞取的漂流瓶给用户"""
    try:
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")

        text = (
            f"🎣 **你捞到了一个漂流瓶！** 🎣\n"
            f"───────────────────\n"
            f"这是一个来自远方的语音消息\n"
            f"点击下方按钮查看或跳过\n"
            f"───────────────────\n"
            f"🌊 每个漂流瓶都承载着一份心意"
            if lang == "zh-cn" else
            f"🎣 **You caught a drift bottle!** 🎣\n"
            f"───────────────────\n"
            f"This is a voice message from afar\n"
            f"Click the buttons below to view or skip\n"
            f"───────────────────\n"
            f"🌊 Every bottle carries a piece of heart"
        )

        keyboard = [
            [InlineKeyboardButton("👀 查看" if lang == "zh-cn" else "👀 View",
                                 callback_data=f"view_bottle_{bottle.id}"),
             InlineKeyboardButton("❌ 跳过" if lang == "zh-cn" else "❌ Skip",
                                 callback_data=f"skip_bottle_{bottle.id}")]
        ]

        await bot.send_message(
            chat_id=user_id,
            text=text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"成功投递捞取的漂流瓶 {bottle.id} 给用户 {user_id}")
        return True

    except TelegramError as e:
        logger.error(f"投递捞取的漂流瓶失败，瓶子ID: {bottle.id}, 接收者: {user_id}, 错误: {str(e)}")
        return False


def get_user_priority_score(user_data):
    """计算用户优先级分数"""
    now = datetime.now()
    last_interaction = user_data.get("last_interaction")
    
    if not last_interaction:
        return 0  # 从未交互的用户优先级最低
    
    try:
        if isinstance(last_interaction, str):
            last_interaction = datetime.fromisoformat(last_interaction)
        
        time_diff = now - last_interaction
        
        if time_diff <= timedelta(minutes=10):
            return 4  # 10分钟内交互
        elif time_diff <= timedelta(days=1):
            return 3  # 一天内交互
        elif time_diff <= timedelta(weeks=1):
            return 2  # 一周内交互
        else:
            return 1  # 其他用户
    except Exception as e:
        logger.error(f"计算用户优先级失败: {str(e)}")
        return 0


async def find_bottle_recipient(sender_id, exclude_users=None):
    """根据优先级找到漂流瓶接收者（已弃用，使用try_deliver_bottle中的逻辑）"""
    try:
        from database import get_active_users_for_bottle

        # 获取活跃用户列表
        active_users = await get_active_users_for_bottle(sender_id, limit=20)

        if not active_users:
            logger.warning(f"没有找到可用的漂流瓶接收者，发送者: {sender_id}")
            return None

        # 排除已尝试过的用户
        if exclude_users:
            active_users = [user for user in active_users if user[0] not in exclude_users]

        if not active_users:
            logger.warning(f"排除已尝试用户后，没有可用的漂流瓶接收者，发送者: {sender_id}")
            return None

        # 随机选择一个用户（已经按优先级排序）
        import random
        selected_user = random.choice(active_users[:min(5, len(active_users))])  # 从前5个高优先级用户中随机选择

        logger.info(f"为发送者 {sender_id} 找到漂流瓶接收者: {selected_user[0]}")
        return selected_user[0]

    except Exception as e:
        logger.error(f"查找漂流瓶接收者失败，发送者: {sender_id}, 错误: {str(e)}")
        return None


async def deliver_bottle_to_user(bottle, recipient_id, bot):
    """将漂流瓶投递给用户"""
    try:
        user_data = await get_user_data(recipient_id)
        lang = user_data.get("preferred_language", "zh-cn")

        text = (
            "🍾 **你收到了一个漂流瓶！** 🍾\n"
            "───────────────────\n"
            "有人向大海投放了一个语音漂流瓶，\n"
            "现在漂到了你这里！\n"
            "───────────────────\n"
            "是否要查看这个漂流瓶？"
            if lang == "zh-cn" else
            "🍾 **You received a drift bottle!** 🍾\n"
            "───────────────────\n"
            "Someone threw a voice drift bottle into the sea,\n"
            "and it drifted to you!\n"
            "───────────────────\n"
            "Do you want to check this drift bottle?"
        )

        keyboard = [
            [InlineKeyboardButton("👀 查看" if lang == "zh-cn" else "👀 View",
                                 callback_data=f"view_bottle_{bottle.id}")],
            [InlineKeyboardButton("❌ 不看" if lang == "zh-cn" else "❌ Skip",
                                 callback_data=f"skip_bottle_{bottle.id}")]
        ]

        await bot.send_message(
            chat_id=recipient_id,
            text=text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"漂流瓶 {bottle.id} 已投递给用户 {recipient_id}")
        return True

    except TelegramError as e:
        error_msg = str(e)

        # 根据错误类型记录不同级别的日志
        if "Chat not found" in error_msg or "Forbidden" in error_msg:
            # 用户不存在或已屏蔽bot，这是常见情况，使用warning级别
            logger.warning(f"投递漂流瓶失败，瓶子ID: {bottle.id}, 接收者: {recipient_id}, 原因: {error_msg} (用户不可达)")

            # 标记用户为已屏蔽（如果是Forbidden错误）
            if "Forbidden" in error_msg:
                try:
                    user_data = await get_user_data(recipient_id)
                    user_data["is_blocked"] = 1
                    await save_user_data(recipient_id, user_data)
                    logger.info(f"用户 {recipient_id} 已标记为屏蔽状态")
                except Exception as db_error:
                    logger.error(f"更新用户屏蔽状态失败: {str(db_error)}")
        else:
            # 其他错误使用error级别
            logger.error(f"投递漂流瓶失败，瓶子ID: {bottle.id}, 接收者: {recipient_id}, 错误: {error_msg}")

        return False
    except Exception as e:
        logger.error(f"投递漂流瓶时发生未知错误，瓶子ID: {bottle.id}, 接收者: {recipient_id}, 错误: {str(e)}")
        return False


async def handle_voice_for_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理用户发送的语音消息（用于漂流瓶）"""
    user_id = update.effective_user.id

    # 检查用户是否在扔瓶子或回复瓶子状态
    if user_id not in user_states:
        return False  # 不是在等待状态，返回False让其他处理器处理

    user_state = user_states[user_id].get('state')
    if user_state not in ['waiting_voice', 'waiting_reply_voice']:
        return False  # 不是在等待语音状态，返回False让其他处理器处理

    # 检查是否超时
    if check_user_state_timeout(user_id):
        await handle_state_timeout(update, context, user_id)
        return True  # 已处理超时，返回True

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 获取语音消息信息
        voice = update.message.voice
        voice_message_id = update.message.message_id
        chat_id = update.message.chat_id

        # 检查用户是否为会员
        is_member = await check_user_membership(user_id)

        # 如果不是会员，需要检查语音内容
        if not is_member:
            # 临时下载文件进行转录检查
            voice_file = await context.bot.get_file(voice.file_id)
            temp_filename = f"temp_check_{user_id}_{int(time.time())}.ogg"
            temp_path = os.path.join(DOWNLOAD_DIR, temp_filename)

            try:
                # 确保目录存在
                os.makedirs(DOWNLOAD_DIR, exist_ok=True)

                # 下载临时文件
                await voice_file.download_to_drive(temp_path)

                transcribed_text, has_numbers, full_text = await transcribe_and_check_voice(temp_path, user_id, lang)

                if transcribed_text is None:
                    # 转录失败
                    await update.message.reply_text(
                        "语音转录失败，请重新发送语音消息！" if lang == "zh-cn" else "Voice transcription failed, please send voice message again!",
                        parse_mode="Markdown"
                    )
                    return True

                if has_numbers:
                    # 包含数字，拒绝发送
                    text = (
                        "贱狗！非会员不能发带数字的语音消息！"
                        if lang == "zh-cn" else
                        "Damn dog! Non-members cannot send voice messages with numbers!"
                    )

                    keyboard = [
                        [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel",
                                             callback_data="cancel_throw_bottle" if user_state == 'waiting_voice' else "cancel_reply_bottle")]
                    ]

                    await update.message.reply_text(
                        text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode="Markdown"
                    )

                    logger.info(f"用户 {user_id} 发送的语音包含数字，已拒绝: {transcribed_text}")
                    return True

            finally:
                # 清理临时文件
                try:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                except Exception:
                    pass

        # 根据当前状态更新用户状态
        if user_state == 'waiting_voice':
            # 扔瓶子状态
            user_states[user_id] = {
                'state': 'confirming_send',
                'voice_message_id': voice_message_id,
                'chat_id': str(chat_id),
                'started_at': datetime.now()
            }

            # 发送确认消息
            text = (
                "🎤 **语音消息已收到！** 🎤\n"
                "───────────────────\n"
                "你的语音消息已经准备好了！\n"
                "确认要将这个语音漂流瓶投放到大海中吗？\n"
                "───────────────────\n"
                "⚠️ 投放后无法撤回！"
                if lang == "zh-cn" else
                "🎤 **Voice message received!** 🎤\n"
                "───────────────────\n"
                "Your voice message is ready!\n"
                "Confirm to throw this voice drift bottle into the sea?\n"
                "───────────────────\n"
                "⚠️ Cannot be withdrawn after release!"
            )

            keyboard = [
                [InlineKeyboardButton("🚀 确认投放" if lang == "zh-cn" else "🚀 Confirm Release",
                                     callback_data="confirm_throw_bottle")],
                [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel",
                                     callback_data="cancel_throw_bottle")]
            ]

        elif user_state == 'waiting_reply_voice':
            # 回复瓶子状态
            bottle_id = user_states[user_id].get('bottle_id')
            user_states[user_id] = {
                'state': 'confirming_reply',
                'voice_message_id': voice_message_id,
                'chat_id': str(chat_id),
                'bottle_id': bottle_id,
                'started_at': datetime.now()
            }

            # 发送确认消息
            text = (
                "🎤 **回复语音已收到！** 🎤\n"
                "───────────────────\n"
                "你的回复语音已经准备好了！\n"
                "确认要发送这个回复给漂流瓶的主人吗？\n"
                "───────────────────\n"
                "⚠️ 发送后无法撤回！"
                if lang == "zh-cn" else
                "🎤 **Reply voice received!** 🎤\n"
                "───────────────────\n"
                "Your reply voice is ready!\n"
                "Confirm to send this reply to the bottle owner?\n"
                "───────────────────\n"
                "⚠️ Cannot be withdrawn after sending!"
            )

            keyboard = [
                [InlineKeyboardButton("🚀 确认发送" if lang == "zh-cn" else "🚀 Confirm Send",
                                     callback_data="confirm_reply_bottle")],
                [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel",
                                     callback_data="cancel_reply_bottle")]
            ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 发送了语音消息，等待确认投放")
        return True  # 表示已处理

    except Exception as e:
        logger.error(f"处理语音消息失败，用户ID: {user_id}, 错误: {str(e)}")

        # 清理状态
        if user_id in user_states:
            del user_states[user_id]

        await update.message.reply_text(
            "处理语音消息时出错，请重试！" if lang == "zh-cn" else "Error processing voice message, please try again!"
        )
        return True


async def handle_non_voice_for_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理用户在漂流瓶状态下发送的非语音消息"""
    user_id = update.effective_user.id

    # 检查用户是否在等待语音状态
    if user_id not in user_states:
        return False

    user_state = user_states[user_id].get('state')
    if user_state not in ['waiting_voice', 'waiting_reply_voice']:
        return False

    # 检查是否超时
    if check_user_state_timeout(user_id):
        await handle_state_timeout(update, context, user_id)
        return True

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        if user_state == 'waiting_voice':
            text = (
                "⚠️ **请发送语音消息** ⚠️\n"
                "───────────────────\n"
                "漂流瓶只支持语音消息！\n"
                "请录制一段语音消息作为你的漂流瓶内容。\n"
                "───────────────────\n"
                "🎤 点击麦克风图标录制语音"
                if lang == "zh-cn" else
                "⚠️ **Please send voice message** ⚠️\n"
                "───────────────────\n"
                "Drift bottles only support voice messages!\n"
                "Please record a voice message as your bottle content.\n"
                "───────────────────\n"
                "🎤 Click the microphone icon to record"
            )
        else:  # waiting_reply_voice
            text = (
                "⚠️ **请发送语音回复** ⚠️\n"
                "───────────────────\n"
                "回复漂流瓶只支持语音消息！\n"
                "请录制一段语音消息作为你的回复。\n"
                "───────────────────\n"
                "🎤 点击麦克风图标录制语音"
                if lang == "zh-cn" else
                "⚠️ **Please send voice reply** ⚠️\n"
                "───────────────────\n"
                "Bottle replies only support voice messages!\n"
                "Please record a voice message as your reply.\n"
                "───────────────────\n"
                "🎤 Click the microphone icon to record"
            )

        keyboard = [
            [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel",
                                 callback_data="cancel_throw_bottle" if user_state == 'waiting_voice' else "cancel_reply_bottle")]
        ]

        await update.message.reply_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 在 {user_state} 状态下发送了非语音消息，已提示")
        return True

    except Exception as e:
        logger.error(f"处理非语音消息失败，用户ID: {user_id}, 错误: {str(e)}")
        return True


async def handle_state_timeout(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: int):
    """处理用户状态超时"""
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        text = (
            "⏰ **操作超时** ⏰\n"
            "───────────────────\n"
            "你已经5分钟没有操作了，\n"
            "为了不影响与AI的正常互动，\n"
            "漂流瓶操作已自动取消。\n"
            "───────────────────\n"
            "💡 如需继续，请重新开始操作"
            if lang == "zh-cn" else
            "⏰ **Operation Timeout** ⏰\n"
            "───────────────────\n"
            "You haven't operated for 5 minutes.\n"
            "To avoid affecting normal AI interaction,\n"
            "the bottle operation has been cancelled.\n"
            "───────────────────\n"
            "💡 Please restart if you want to continue"
        )

        keyboard = [
            [InlineKeyboardButton("🍾 重新开始" if lang == "zh-cn" else "🍾 Start Again",
                                 callback_data=f"drift_bottle_{user_id}")],
            [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                 callback_data="back_to_main")]
        ]

        # 清理用户状态
        cleanup_user_state(user_id)

        if update.message:
            await update.message.reply_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode="Markdown"
            )
        elif update.callback_query:
            await update.callback_query.edit_message_text(
                text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode="Markdown"
            )

        logger.info(f"用户 {user_id} 操作超时，已清理状态")

    except Exception as e:
        logger.error(f"处理超时失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_confirm_throw_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理确认投放漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查用户状态
    if user_id not in user_states or user_states[user_id].get('state') != 'confirming_send':
        await query.answer("操作已过期，请重新开始！" if lang == "zh-cn" else "Operation expired, please start again!")
        return

    try:
        voice_message_id = user_states[user_id]['voice_message_id']
        chat_id = user_states[user_id]['chat_id']

        # 创建漂流瓶对象
        bottle = DriftBottle(user_id, voice_message_id, chat_id)

        # 保存到数据库
        await bottle.save()

        # 增加每日计数
        await increment_user_daily_bottle_count(user_id)

        # 清理用户状态
        del user_states[user_id]

        # 检查是否还能再发送
        can_send_more, current_count, daily_limit = await can_user_send_bottle_today(user_id)

        # 发送成功消息
        text = (
            f"🍾 **漂流瓶投放成功！** 🍾\n"
            f"───────────────────\n"
            f"你的语音漂流瓶已经投放到大海中！\n"
            f"它会随机漂到其他用户那里。\n"
            f"───────────────────\n"
            f"📊 今日已发送：{current_count}/{daily_limit} 个\n"
            f"🌊 愿你的声音传达到远方！"
            if lang == "zh-cn" else
            f"🍾 **Drift bottle released successfully!** 🍾\n"
            f"───────────────────\n"
            f"Your voice drift bottle has been thrown into the sea!\n"
            f"It will randomly drift to other users.\n"
            f"───────────────────\n"
            f"📊 Today sent: {current_count}/{daily_limit}\n"
            f"🌊 May your voice reach far away!"
        )

        keyboard = [
            [InlineKeyboardButton("🚀 再扔一个" if lang == "zh-cn" else "🚀 Throw Another",
                                 callback_data=f"throw_another_bottle_{user_id}")],
            [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                 callback_data="back_to_main")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 成功投放漂流瓶 {bottle.id}")

        # 异步尝试投递给其他用户
        asyncio.create_task(try_deliver_waiting_bottles(context.bot))

    except Exception as e:
        logger.error(f"确认投放漂流瓶失败，用户ID: {user_id}, 错误: {str(e)}")

        # 清理状态和文件
        if user_id in user_states:
            voice_path = user_states[user_id].get('voice_path')
            if voice_path and os.path.exists(voice_path):
                try:
                    os.remove(voice_path)
                except Exception:
                    pass
            del user_states[user_id]

        await query.edit_message_text(
            "投放漂流瓶失败，请重试！" if lang == "zh-cn" else "Failed to release drift bottle, please try again!"
        )


async def handle_throw_another_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理再扔一个漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 检查每日限制
        can_send, current_count, daily_limit = await can_user_send_bottle_today(user_id)

        if not can_send:
            # 检查是否为会员
            is_member = await check_user_membership(user_id)

            if is_member:
                # 会员已达上限，使用弹窗提示
                await query.answer(
                    f"🚫 会员每日限制已达上限！今日已发送：{current_count}/{daily_limit} 个漂流瓶，明天再来吧！"
                    if lang == "zh-cn" else
                    f"🚫 Member daily limit reached! Today sent: {current_count}/{daily_limit} bottles, come back tomorrow!",
                    show_alert=True
                )
            else:
                # 普通用户需要升级会员，发送新消息提示
                await context.bot.send_message(
                    chat_id=user_id,
                    text=(
                        f"💎 **仅会员可再次发送** 💎\n"
                        f"───────────────────\n"
                        f"普通用户每日只能发送1个漂流瓶\n"
                        f"今日已发送：{current_count}/{daily_limit} 个\n"
                        f"───────────────────\n"
                        f"💎 升级会员可每日发送3个漂流瓶！\n"
                        f"🎯 享受更多互动乐趣！"
                        if lang == "zh-cn" else
                        f"💎 **Members Only Feature** 💎\n"
                        f"───────────────────\n"
                        f"Regular users can only send 1 bottle daily\n"
                        f"Today sent: {current_count}/{daily_limit}\n"
                        f"───────────────────\n"
                        f"💎 Upgrade to member for 3 bottles daily!\n"
                        f"🎯 Enjoy more interactive fun!"
                    ),
                    parse_mode="Markdown"
                )

                # 使用弹窗简短提示
                await query.answer(
                    "💎 仅会员可再次发送！详情请查看上方消息。"
                    if lang == "zh-cn" else
                    "💎 Members only! Check the message above for details.",
                    show_alert=True
                )
            return

        # 可以继续发送，调用扔瓶子函数
        await handle_throw_bottle(update, context)

    except Exception as e:
        logger.error(f"处理再扔一个漂流瓶失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("操作失败，请重试！" if lang == "zh-cn" else "Operation failed, please try again!")


async def try_deliver_waiting_bottles(bot):
    """尝试投递等待中的漂流瓶"""
    try:
        waiting_bottles = await get_waiting_bottles(limit=10)

        for bottle_data in waiting_bottles:
            bottle = DriftBottle.from_dict(bottle_data)
            await try_deliver_bottle(bottle, bot)

    except Exception as e:
        logger.error(f"投递等待中的漂流瓶失败: {str(e)}")


async def redeliver_unviewed_bottles(bot):
    """重新投递24小时未查看的漂流瓶"""
    try:
        unviewed_bottles = await get_unviewed_bottles_24h()

        if not unviewed_bottles:
            return

        logger.info(f"发现 {len(unviewed_bottles)} 个24小时未查看的漂流瓶，开始重新投递")

        for bottle_data in unviewed_bottles:
            bottle = DriftBottle.from_dict(bottle_data)

            # 重置状态为等待，清除当前接收者
            await bottle.mark_rejected()
            logger.info(f"漂流瓶 {bottle.id} 24小时未查看，已重置状态等待重新投递")

            # 尝试重新投递
            await try_deliver_bottle(bottle, bot)

    except Exception as e:
        logger.error(f"重新投递24小时未查看漂流瓶失败: {str(e)}")


async def try_deliver_bottle(bottle, bot, max_attempts=5):
    """尝试投递漂流瓶给合适的用户，失败时自动重试"""
    try:
        logger.info(f"开始尝试投递漂流瓶 {bottle.id}")

        # 获取活跃用户列表
        from database import get_active_users_for_bottle
        active_users = await get_active_users_for_bottle(bottle.sender_id, limit=50)

        if not active_users:
            logger.warning(f"漂流瓶 {bottle.id} 没有找到任何可用的接收者")
            return

        # 记录已尝试过的用户，避免重复投递
        attempted_users = set()

        # 尝试投递给多个用户，直到成功或达到最大尝试次数
        for attempt in range(max_attempts):
            if not bottle.can_deliver():
                logger.warning(f"漂流瓶 {bottle.id} 无法继续投递（已达到最大投递次数或已过期）")
                break

            # 从活跃用户中选择一个未尝试过的用户
            available_users = [user for user in active_users if user[0] not in attempted_users]

            if not available_users:
                logger.warning(f"漂流瓶 {bottle.id} 已尝试所有可用用户，投递失败")
                break

            # 随机选择一个用户
            import random
            selected_user = random.choice(available_users[:min(10, len(available_users))])  # 从前10个高优先级用户中随机选择
            recipient_id = selected_user[0]

            # 记录已尝试的用户
            attempted_users.add(recipient_id)

            logger.info(f"漂流瓶 {bottle.id} 第 {attempt + 1} 次尝试投递给用户 {recipient_id}")

            # 尝试投递
            success = await bottle.deliver_to(recipient_id)
            if success:
                # 发送漂流瓶给接收者
                delivery_success = await deliver_bottle_to_user(bottle, recipient_id, bot)
                if delivery_success:
                    logger.info(f"漂流瓶 {bottle.id} 成功投递给用户 {recipient_id} (第 {attempt + 1} 次尝试)")
                    return  # 投递成功，退出函数
                else:
                    logger.warning(f"漂流瓶 {bottle.id} 投递给用户 {recipient_id} 失败，尝试下一个用户")
                    await bottle.mark_rejected()  # 重置状态以便下次投递
            else:
                logger.warning(f"漂流瓶 {bottle.id} 无法投递给用户 {recipient_id}")
                break

        logger.warning(f"漂流瓶 {bottle.id} 经过 {max_attempts} 次尝试后仍未成功投递，已尝试用户: {list(attempted_users)}")

    except Exception as e:
        logger.error(f"投递漂流瓶失败，瓶子ID: {bottle.id}, 错误: {str(e)}")


async def schedule_bottle_retry(bottle, bot, delay_seconds=30):
    """安排漂流瓶重试投递"""
    try:
        logger.info(f"安排漂流瓶 {bottle.id} 在 {delay_seconds} 秒后重试投递")
        await asyncio.sleep(delay_seconds)

        if bottle.can_deliver():
            await try_deliver_bottle(bottle, bot)
        else:
            logger.info(f"漂流瓶 {bottle.id} 重试时已无法投递（可能已过期或达到最大投递次数）")
    except Exception as e:
        logger.error(f"安排漂流瓶重试失败，瓶子ID: {bottle.id}, 错误: {str(e)}")


async def handle_view_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理查看漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 从数据库查找漂流瓶
        bottle_data = await get_drift_bottle(bottle_id)
        if not bottle_data:
            logger.warning(f"用户 {user_id} 尝试查看不存在的漂流瓶: {bottle_id}")
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            return

        # 检查漂流瓶是否已过期
        bottle = DriftBottle.from_dict(bottle_data)
        if bottle.is_expired():
            logger.warning(f"用户 {user_id} 尝试查看已过期的漂流瓶: {bottle_id}")
            await query.answer("漂流瓶已过期！" if lang == "zh-cn" else "Bottle has expired!")
            return

        # 检查用户权限：要么是当前接收者，要么是通过捞瓶子获得的（状态允许查看）
        is_current_recipient = bottle_data['current_recipient'] == str(user_id)
        is_pickable_state = bottle_data['state'] in ['waiting', 'delivered', 'viewed', 'rejected'] and bottle_data['replied_by'] is None

        if not (is_current_recipient or is_pickable_state):
            logger.warning(f"用户 {user_id} 无权查看漂流瓶: {bottle_id}, 当前接收者: {bottle_data['current_recipient']}, 状态: {bottle_data['state']}")
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            return

        # 如果不是当前接收者，更新接收者信息（用于捞瓶子的情况）
        if not is_current_recipient:
            await bottle.deliver_to(user_id)

        # 标记为已查看
        await bottle.mark_viewed()

        try:
            # 复制语音消息（不显示发送者信息）
            await context.bot.copy_message(
                chat_id=user_id,
                from_chat_id=bottle.chat_id,
                message_id=bottle.voice_message_id
            )

        except TelegramError as e:
            logger.error(f"复制漂流瓶消息失败，瓶子ID: {bottle_id}, 错误: {str(e)}")
            await query.answer("语音消息已失效！" if lang == "zh-cn" else "Voice message is no longer available!")
            await bottle.mark_rejected()  # 标记为拒绝，让它重新投递
            return

        # 编辑原消息，添加回复按钮
        text = (
            "🍾 **漂流瓶已查看** 🍾\n"
            "───────────────────\n"
            "你已经查看了这个漂流瓶！\n"
            "希望这个声音能带给你快乐！\n"
            "───────────────────\n"
            "💬 你可以选择回复这个漂流瓶"
            if lang == "zh-cn" else
            "🍾 **Bottle Viewed** 🍾\n"
            "───────────────────\n"
            "You have viewed this drift bottle!\n"
            "Hope this voice brings you joy!\n"
            "───────────────────\n"
            "💬 You can choose to reply to this bottle"
        )

        keyboard = [
            [InlineKeyboardButton("💬 回复漂流瓶" if lang == "zh-cn" else "💬 Reply to Bottle",
                                 callback_data=f"reply_bottle_{bottle_id}")],
            [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                 callback_data="back_to_main")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 查看了漂流瓶 {bottle_id}")

    except Exception as e:
        logger.error(f"处理查看漂流瓶失败，用户ID: {user_id}, 瓶子ID: {bottle_id}, 错误: {str(e)}")
        await query.answer("查看漂流瓶时出错！" if lang == "zh-cn" else "Error viewing bottle!")


async def handle_skip_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理跳过漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 从数据库查找漂流瓶
        bottle_data = await get_drift_bottle(bottle_id)
        if not bottle_data or bottle_data['current_recipient'] != str(user_id):
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            return

        bottle = DriftBottle.from_dict(bottle_data)

        # 标记为被拒绝（重置状态以便投递给下一个用户）
        await bottle.mark_rejected()

        # 编辑原消息
        await query.edit_message_text(
            "🍾 **漂流瓶已跳过** 🍾\n"
            "───────────────────\n"
            "你选择跳过了这个漂流瓶。\n"
            "它会继续漂流到其他人那里。\n"
            "───────────────────\n"
            "🌊 也许下次会有更适合你的漂流瓶！"
            if lang == "zh-cn" else
            "🍾 **Bottle Skipped** 🍾\n"
            "───────────────────\n"
            "You chose to skip this drift bottle.\n"
            "It will continue drifting to others.\n"
            "───────────────────\n"
            "🌊 Maybe next time there will be a better one!",
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 跳过了漂流瓶 {bottle_id}")

        # 异步尝试投递给下一个用户
        if bottle.can_deliver():
            asyncio.create_task(try_deliver_bottle(bottle, context.bot))

    except Exception as e:
        logger.error(f"处理跳过漂流瓶失败，用户ID: {user_id}, 瓶子ID: {bottle_id}, 错误: {str(e)}")
        await query.answer("跳过漂流瓶时出错！" if lang == "zh-cn" else "Error skipping bottle!")


async def handle_reply_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理回复漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 从数据库查找漂流瓶
        bottle_data = await get_drift_bottle(bottle_id)
        if not bottle_data:
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            return

        # 清理可能存在的旧状态
        cleanup_user_state(user_id)

        # 设置用户状态为等待回复语音
        user_states[user_id] = {
            'state': 'waiting_reply_voice',
            'bottle_id': bottle_id,
            'started_at': datetime.now()
        }

        text = (
            "🎤 **请发送回复语音** 🎤\n"
            "───────────────────\n"
            "请发送一条语音消息作为你对这个漂流瓶的回复。\n"
            "⚠️ 注意：只支持语音消息！\n"
            "───────────────────\n"
            "发送完成后，我会让你确认是否发送给漂流瓶的主人。"
            if lang == "zh-cn" else
            "🎤 **Please send reply voice** 🎤\n"
            "───────────────────\n"
            "Please send a voice message as your reply to this bottle.\n"
            "⚠️ Note: Only voice messages are supported!\n"
            "───────────────────\n"
            "After sending, I'll ask you to confirm sending to the bottle owner."
        )

        keyboard = [
            [InlineKeyboardButton("❌ 取消回复" if lang == "zh-cn" else "❌ Cancel Reply",
                                 callback_data="cancel_reply_bottle")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 开始回复漂流瓶 {bottle_id}")

    except Exception as e:
        logger.error(f"处理回复漂流瓶失败，用户ID: {user_id}, 瓶子ID: {bottle_id}, 错误: {str(e)}")
        await query.answer("回复漂流瓶时出错！" if lang == "zh-cn" else "Error replying to bottle!")


async def handle_cancel_reply_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理取消回复漂流瓶"""
    query = update.callback_query
    user_id = query.from_user.id

    # 清除用户状态
    cleanup_user_state(user_id)

    # 返回漂流瓶主菜单
    await handle_drift_bottle_menu(update, context)
    logger.info(f"用户 {user_id} 取消回复漂流瓶")


async def handle_confirm_reply_bottle(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理确认发送回复"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查用户状态
    if user_id not in user_states or user_states[user_id].get('state') != 'confirming_reply':
        await query.answer("操作已过期，请重新开始！" if lang == "zh-cn" else "Operation expired, please start again!")
        return

    try:
        voice_message_id = user_states[user_id]['voice_message_id']
        chat_id = user_states[user_id]['chat_id']
        bottle_id = user_states[user_id]['bottle_id']

        # 从数据库查找漂流瓶
        bottle_data = await get_drift_bottle(bottle_id)
        if not bottle_data:
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            cleanup_user_state(user_id)
            return

        bottle = DriftBottle.from_dict(bottle_data)

        # 标记漂流瓶为已回复
        await bottle.mark_replied(voice_message_id, chat_id, user_id)

        # 清理用户状态
        cleanup_user_state(user_id)

        # 发送成功消息给回复者
        text = (
            "💬 **回复发送成功！** 💬\n"
            "───────────────────\n"
            "你的语音回复已经发送给漂流瓶的主人！\n"
            "相信你的声音会带给对方温暖。\n"
            "───────────────────\n"
            "🌊 感谢你参与漂流瓶互动！"
            if lang == "zh-cn" else
            "💬 **Reply sent successfully!** 💬\n"
            "───────────────────\n"
            "Your voice reply has been sent to the bottle owner!\n"
            "Your voice will surely bring warmth to them.\n"
            "───────────────────\n"
            "🌊 Thank you for participating in bottle interaction!"
        )

        keyboard = [
            [InlineKeyboardButton("🍾 再扔一个" if lang == "zh-cn" else "🍾 Throw Another",
                                 callback_data=f"throw_bottle_{user_id}")],
            [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                 callback_data="back_to_main")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        # 异步发送回复给原漂流瓶发送者
        asyncio.create_task(send_reply_to_sender(bottle, context.bot))

        logger.info(f"用户 {user_id} 成功回复漂流瓶 {bottle_id}")

    except Exception as e:
        logger.error(f"确认回复漂流瓶失败，用户ID: {user_id}, 错误: {str(e)}")

        # 清理状态和文件
        cleanup_user_state(user_id)

        await query.edit_message_text(
            "发送回复失败，请重试！" if lang == "zh-cn" else "Failed to send reply, please try again!"
        )


async def send_reply_to_sender(bottle, bot):
    """发送回复给原漂流瓶发送者"""
    try:
        sender_id = bottle.sender_id
        reply_message_id = bottle.reply_message_id
        reply_chat_id = bottle.reply_chat_id

        if not reply_message_id or not reply_chat_id:
            logger.error(f"回复消息信息不完整: message_id={reply_message_id}, chat_id={reply_chat_id}")
            return

        user_data = await get_user_data(sender_id)
        lang = user_data.get("preferred_language", "zh-cn")

        try:
            # 复制回复语音消息（不显示发送者信息）
            await bot.copy_message(
                chat_id=sender_id,
                from_chat_id=reply_chat_id,
                message_id=reply_message_id
            )

            # 检查原发送者是否为会员，决定是否可以继续回复
            is_sender_member = await check_user_membership(sender_id)

            if is_sender_member:
                # 会员可以继续回复，发送带回复按钮的消息
                text = (
                    "💬 **你的漂流瓶收到了回复！** 💬\n"
                    "───────────────────\n"
                    "🎯 作为会员，你可以继续回复进行对话！"
                    if lang == "zh-cn" else
                    "💬 **Your bottle got a reply!** 💬\n"
                    "───────────────────\n"
                    "🎯 As a member, you can continue replying for conversation!"
                )

                keyboard = [
                    [InlineKeyboardButton("💬 继续回复" if lang == "zh-cn" else "💬 Continue Reply",
                                         callback_data=f"continue_reply_{bottle.id}")],
                    [InlineKeyboardButton("✅ 结束对话" if lang == "zh-cn" else "✅ End Conversation",
                                         callback_data=f"end_conversation_{bottle.id}")]
                ]

                await bot.send_message(
                    chat_id=sender_id,
                    text=text,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode="Markdown"
                )

                # 不标记为完成，保持对话活跃状态
                await update_drift_bottle(bottle.id, {
                    'is_conversation_active': 1,
                    'conversation_id': f"{bottle.sender_id}_{bottle.replied_by}"
                })
            else:
                # 普通用户，发送带升级提示的回复按钮
                text = (
                    "💬 **你的漂流瓶收到了回复！** 💬\n"
                    "───────────────────\n"
                    "想要继续对话吗？"
                    if lang == "zh-cn" else
                    "💬 **Your bottle got a reply!** 💬\n"
                    "───────────────────\n"
                    "Want to continue the conversation?"
                )

                keyboard = [
                    [InlineKeyboardButton("💬 继续回复" if lang == "zh-cn" else "💬 Continue Reply",
                                         callback_data=f"try_continue_reply_{bottle.id}")]
                ]

                await bot.send_message(
                    chat_id=sender_id,
                    text=text,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode="Markdown"
                )

                # 标记漂流瓶流程完成（普通用户不能继续对话）
                await bottle.mark_completed()

        except TelegramError as e:
            logger.error(f"复制回复消息失败: {str(e)}")
            # 如果复制失败，发送提示消息
            await bot.send_message(
                chat_id=sender_id,
                text=(
                    "💬 你的漂流瓶收到了回复，但回复消息已失效。"
                    if lang == "zh-cn" else
                    "💬 Your bottle got a reply, but the reply message is no longer available."
                ),
                parse_mode="Markdown"
            )

            # 标记漂流瓶流程完成
            await bottle.mark_completed()

        logger.info(f"回复已发送给漂流瓶发送者 {sender_id}，瓶子 {bottle.id}")

    except Exception as e:
        logger.error(f"发送回复给发送者失败，瓶子ID: {bottle.id}, 错误: {str(e)}")


async def handle_try_continue_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理普通用户尝试继续回复（会员检查）"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 检查是否为会员
        is_member = await check_user_membership(user_id)

        if is_member:
            # 是会员，可以继续回复
            await handle_continue_reply(update, context)
        else:
            # 不是会员，发送新消息提示
            await context.bot.send_message(
                chat_id=user_id,
                text=(
                    f"💎 **仅会员可继续对话** 💎\n"
                    f"───────────────────\n"
                    f"普通用户收到回复后对话即结束\n"
                    f"只有会员才能进行多轮对话\n"
                    f"───────────────────\n"
                    f"💎 升级会员享受无限对话！\n"
                    f"🎯 与更多有趣的人深入交流！"
                    if lang == "zh-cn" else
                    f"💎 **Members Only Feature** 💎\n"
                    f"───────────────────\n"
                    f"Regular users' conversations end after one reply\n"
                    f"Only members can have multi-turn conversations\n"
                    f"───────────────────\n"
                    f"💎 Upgrade to member for unlimited conversations!\n"
                    f"🎯 Deep chat with more interesting people!"
                ),
                parse_mode="Markdown"
            )

            # 使用弹窗简短提示
            await query.answer(
                "💎 仅会员可继续对话！详情请查看上方消息。"
                if lang == "zh-cn" else
                "💎 Members only! Check the message above for details.",
                show_alert=True
            )

    except Exception as e:
        logger.error(f"处理继续回复失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("操作失败，请重试！" if lang == "zh-cn" else "Operation failed, please try again!")


async def handle_continue_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理继续回复（会员功能）"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 检查漂流瓶是否存在
        bottle_data = await get_drift_bottle(bottle_id)
        if not bottle_data:
            await query.answer("漂流瓶不存在或已过期！" if lang == "zh-cn" else "Bottle not found or expired!")
            return

        # 设置用户状态为等待回复语音
        user_states[user_id] = {
            'state': 'waiting_reply_voice',
            'bottle_id': bottle_id,
            'started_at': datetime.now()
        }

        text = (
            "🎤 **请发送语音回复** 🎤\n"
            "───────────────────\n"
            "请发送你的语音消息来继续对话\n"
            "───────────────────\n"
            "💡 只接受语音消息哦！"
            if lang == "zh-cn" else
            "🎤 **Please Send Voice Reply** 🎤\n"
            "───────────────────\n"
            "Please send your voice message to continue the conversation\n"
            "───────────────────\n"
            "💡 Voice messages only!"
        )

        keyboard = [
            [InlineKeyboardButton("❌ 取消" if lang == "zh-cn" else "❌ Cancel",
                                 callback_data="cancel_reply_bottle")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"处理继续回复失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("操作失败，请重试！" if lang == "zh-cn" else "Operation failed, please try again!")


async def handle_end_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理结束对话"""
    query = update.callback_query
    user_id = query.from_user.id
    bottle_id = query.data.split("_")[-1]

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        # 标记对话结束
        await update_drift_bottle(bottle_id, {
            'state': 'completed',
            'is_conversation_active': 0
        })

        text = (
            "✅ **对话已结束** ✅\n"
            "───────────────────\n"
            "感谢你的参与！\n"
            "希望这次漂流瓶之旅给你带来了快乐！"
            if lang == "zh-cn" else
            "✅ **Conversation Ended** ✅\n"
            "───────────────────\n"
            "Thank you for participating!\n"
            "Hope this bottle journey brought you joy!"
        )

        keyboard = [
            [InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Back to Main Menu",
                                 callback_data="back_to_main")]
        ]

        await query.edit_message_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )

        logger.info(f"用户 {user_id} 结束了漂流瓶 {bottle_id} 的对话")

    except Exception as e:
        logger.error(f"处理结束对话失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("操作失败，请重试！" if lang == "zh-cn" else "Operation failed, please try again!")


async def cleanup_expired_bottles_db():
    """清理过期的漂流瓶（数据库版本）"""
    try:
        cleaned_count = await cleanup_expired_bottles()
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个过期或已完成的漂流瓶")
    except Exception as e:
        logger.error(f"清理过期漂流瓶失败: {str(e)}")


def cleanup_expired_user_states():
    """清理过期的用户状态"""
    expired_users = []
    for user_id, state_info in user_states.items():
        started_at = state_info.get('started_at')
        if started_at and (datetime.now() - started_at).total_seconds() > USER_STATE_TIMEOUT:
            expired_users.append(user_id)

    for user_id in expired_users:
        cleanup_user_state(user_id)
        logger.info(f"清理过期用户状态: {user_id}")


async def get_bottle_pool_stats_db():
    """获取漂流瓶池统计信息（数据库版本）"""
    try:
        return await get_bottle_stats()
    except Exception as e:
        logger.error(f"获取漂流瓶统计失败: {str(e)}")
        return {
            'total': 0,
            'waiting': 0,
            'delivered': 0,
            'viewed': 0,
            'replied': 0,
            'completed': 0,
            'expired': 0
        }


# 定期清理任务（应该在主程序中调用）
async def periodic_cleanup(bot=None):
    """定期清理过期漂流瓶和用户状态"""
    cleanup_count = 0
    while True:
        try:
            await cleanup_expired_bottles_db()
            cleanup_expired_user_states()

            # 尝试投递等待中的漂流瓶
            if bot:
                asyncio.create_task(try_deliver_waiting_bottles(bot))

                # 每小时检查一次24小时未查看的漂流瓶
                if cleanup_count % 12 == 0:  # 每12次清理（1小时）检查一次
                    asyncio.create_task(redeliver_unviewed_bottles(bot))

            # 每小时记录一次统计信息
            cleanup_count += 1
            if cleanup_count % 12 == 0:  # 每12次清理（1小时）记录一次统计
                stats = await get_bottle_pool_stats_db()
                logger.info(f"漂流瓶池统计: 总数={stats['total']}, 等待={stats['waiting']}, "
                           f"已投递={stats['delivered']}, 已查看={stats['viewed']}, "
                           f"已回复={stats['replied']}, 已完成={stats['completed']}, 已过期={stats['expired']}")

            await asyncio.sleep(300)  # 每5分钟清理一次
        except Exception as e:
            logger.error(f"定期清理失败: {str(e)}")
            await asyncio.sleep(300)
