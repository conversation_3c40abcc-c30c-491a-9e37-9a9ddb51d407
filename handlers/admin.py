from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from datetime import datetime, timedelta
from logging_config import logger
from database import get_user_data, save_user_data
from config import MASTER_ID, DOWNLOAD_DIR
from utils import get_main_menu
from ai_processing_status import ai_processing_status
import os

# 用于存储管理员正在查看的用户信息的临时状态
admin_user_sessions = {}


async def ban_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    if not context.args:
        try:
            await update.message.reply_text(
                "爹！用法：/ban <用户ID> <小时数>\n例如：/ban 123456789 24"
                if lang == "zh-cn" else
                "Master! Usage: /ban <user_id> <hours>\nExample: /ban 123456789 24"
            )
        except TelegramError as e:
            logger.error(f"发送用法提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    try:
        target_user_id = context.args[0]
        hours = float(context.args[1])
        ban_until = (datetime.now() + timedelta(hours=hours)).isoformat()
        target_user_data = await get_user_data(target_user_id)
        target_user_data["ban_until"] = ban_until
        await save_user_data(target_user_id, target_user_data)
        try:
            await context.bot.send_message(
                chat_id=target_user_id,
                text=f"贱奴！你被爷禁言 {hours} 小时，滚去反省！"
                if target_user_data.get("preferred_language", "zh-cn") == "zh-cn" else
                f"Filthy dog! You're muted for {hours} hours, go reflect!"
            )
            await update.message.reply_text(
                f"爹！用户 {target_user_id} 已被禁言 {hours} 小时！"
                if lang == "zh-cn" else
                f"Master! User {target_user_id} has been muted for {hours} hours!"
            )
            logger.info(f"用户 {target_user_id} 被管理员 {user_id} 禁言 {hours} 小时")
        except TelegramError as e:
            logger.error(f"发送禁言通知失败，用户ID: {target_user_id}, 错误: {str(e)}")
            await update.message.reply_text(
                f"爹！用户 {target_user_id} 禁言成功，但通知发送失败！"
                if lang == "zh-cn" else
                f"Master! User {target_user_id} muted successfully, but notification failed!"
            )
    except (IndexError, ValueError) as e:
        logger.error(f"禁言命令参数错误，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await update.message.reply_text(
                "爹！用法：/ban <用户ID> <小时数>\n例如：/ban 123456789 24"
                if lang == "zh-cn" else
                "Master! Usage: /ban <user_id> <hours>\nExample: /ban 123456789 24"
            )
        except TelegramError as e:
            logger.error(f"发送用法提示失败，用户ID: {user_id}, 错误: {str(e)}")
    except Exception as e:
        logger.error(f"禁言用户失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await update.message.reply_text(
                "爹！禁言时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Master! Something went wrong banning the user, try again later!"
            )
        except TelegramError as e:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_task_center(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理任务中心快捷回复"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    task_center_message = """任务Bot开放测试，暂时没几个任务，懒得想，以后任务多少看大家投稿了。
点击打开：@bdsm_taskbot

🎯 任务系统
- 📤 投稿任务：提交任务通过审核奖励3天会员。
- 🎲 每日抽取：普通用户每日1次抽取机会，会员每日3次抽取机会。
- ✅ 完成奖励：提交证明通过AI审批获得1天会员。

🤖 任务审核
- 🧠 智能识别：先由AI审核图片/视频内容是否符合任务要求
- ⚡ 高效处理：AI识别出错的，可提交管理员或者自己绑定的主人进行审核。

👑 主奴互动
- 🔗 绑定关系：奴可以输入主人的ID，申请绑定主人。
- 📋 专属任务：主人发布，奴完成任务

💎 会员特权
- 🎯 更多机会：每日3次抽取VS普通1次
- 🎁 获取渠道：任务投稿、任务完成、购买兑换码、提建议、反馈BUG

注意保护个人隐私！
注意保护个人隐私！！
注意保护个人隐私！！！
傻逼除外！"""
    
    back_button = [
        [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
    ]
    
    try:
        await query.edit_message_text(
            task_center_message,
            reply_markup=InlineKeyboardMarkup(back_button)
        )
        logger.info(f"用户 {user_id} 查看任务中心信息")
    except TelegramError as e:
        logger.error(f"发送任务中心信息失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！任务中心信息显示失败，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Task center info display failed, try again later!",
            reply_markup=InlineKeyboardMarkup(back_button)
        )


async def handle_task_center_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的任务中心 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    task_center_message = """任务Bot开放测试，暂时没几个任务，懒得想，以后任务多少看大家投稿了。
点击打开：@bdsm_taskbot

🎯 任务系统
- 📤 投稿任务：提交任务通过审核奖励3天会员。
- 🎲 每日抽取：普通用户每日1次抽取机会，会员每日3次抽取机会。
- ✅ 完成奖励：提交证明通过AI审批获得1天会员。

🤖 任务审核
- 🧠 智能识别：先由AI审核图片/视频内容是否符合任务要求
- ⚡ 高效处理：AI识别出错的，可提交管理员或者自己绑定的主人进行审核。

👑 主奴互动
- 🔗 绑定关系：奴可以输入主人的ID，申请绑定主人。
- 📋 专属任务：主人发布，奴完成任务

💎 会员特权
- 🎯 更多机会：每日3次抽取VS普通1次
- 🎁 获取渠道：任务投稿、任务完成、购买兑换码、提建议、反馈BUG

注意保护个人隐私！
注意保护个人隐私！！
注意保护个人隐私！！！
傻逼除外！"""
    
    back_button = [
        [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
    ]
    
    try:
        await update.message.reply_text(
            task_center_message,
            reply_markup=InlineKeyboardMarkup(back_button)
        )
        logger.info(f"用户 {user_id} 通过快捷回复查看任务中心信息")
    except TelegramError as e:
        logger.error(f"发送任务中心信息失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！任务中心信息显示失败，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Task center info display failed, try again later!",
            reply_markup=InlineKeyboardMarkup(back_button)
        )


async def api_status(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看API保活状态的管理员命令"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查是否为管理员
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return


async def api_health(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看API健康状态的管理员命令"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查是否为管理员
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    try:
        from ai_dialogue.api_manager import format_api_health_report

        # 获取API健康状态报告
        health_report = format_api_health_report()

        # 发送报告
        await update.message.reply_text(
            health_report,
            parse_mode='Markdown'
        )

        logger.info(f"管理员 {user_id} 查看了API健康状态")

    except Exception as e:
        logger.error(f"获取API健康状态失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await update.message.reply_text(
                "爹！获取API健康状态时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Master! Something went wrong getting API health status, try again later!"
            )
        except TelegramError as e:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(e)}")
    
    try:
        # 导入API状态函数
        from ai_dialogue import get_api_status, get_api_keepalive_status
        
        # 获取API状态信息
        api_status_info = get_api_status()
        keepalive_status = get_api_keepalive_status()
        
        # 构建状态消息
        status_message = "🔥 **API状态报告** 🔥\n\n"
        
        # API保活状态
        status_message += "📡 **API保活状态**\n"
        status_message += f"• 状态：{'✅ 已启用' if keepalive_status['enabled'] else '❌ 已禁用'}\n"
        status_message += f"• 上次调用：{keepalive_status['last_api_call_time']}\n"
        status_message += f"• 距离上次：{keepalive_status['time_since_last_call_formatted']}\n"
        status_message += f"• 保活间隔：{keepalive_status['keepalive_interval_seconds']}秒\n"
        status_message += f"• 检查间隔：{keepalive_status['check_interval_seconds']}秒\n"
        
        if keepalive_status['needs_keepalive']:
            status_message += "• ⚠️ 需要保活\n"
        else:
            next_keepalive = keepalive_status['next_keepalive_in_seconds']
            status_message += f"• ⏱️ 下次保活：{int(next_keepalive // 60)}分{int(next_keepalive % 60)}秒后\n"
        
        status_message += f"\n🎯 **目标保活API信息**\n"
        status_message += f"• 配置名称：{keepalive_status['target_api_name']}\n"
        status_message += f"• API名称：{keepalive_status['target_api']['name']}\n"
        status_message += f"• 模型：{keepalive_status['target_api']['model']}\n"
        status_message += f"• URL：{keepalive_status['target_api']['url'][:50]}...\n"
        
        # API使用统计
        status_message += f"\n📊 **API使用统计**\n"
        for api_name, api_info in api_status_info.items():
            status_message += f"• {api_name}：{api_info['success_calls']}次成功，{api_info['failed_calls']}次失败，成功率{api_info['success_rate']}\n"
        
        await update.message.reply_text(
            status_message,
            parse_mode="Markdown"
        )
        
        logger.info(f"管理员 {user_id} 查看API状态")
        
    except Exception as e:
        logger.error(f"获取API状态失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await update.message.reply_text(
                "爹！获取API状态时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Master! Something went wrong getting API status, try again later!"
            )
        except TelegramError as e:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def ai_processing_stats(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """查看AI处理状态统计信息（管理员命令）"""
    user_id = update.effective_user.id
    
    # 检查是否为管理员
    if str(user_id) != MASTER_ID:
        await update.message.reply_text("贱奴！这个命令只有爷才能使用！")
        return
    
    try:
        # 获取统计信息
        stats = await ai_processing_status.get_processing_stats()
        
        # 构建统计报告
        report = "🤖 AI处理状态统计报告 🤖\n"
        report += "=" * 30 + "\n"
        report += f"📊 当前正在处理的用户数: {stats['processing_users_count']}\n"
        
        if stats['processing_users']:
            report += f"👥 正在处理的用户ID: {', '.join(stats['processing_users'])}\n"
        
        report += f"🚫 总计忽略消息数: {stats['total_ignored_messages']}\n"
        
        if stats['users_with_ignored_messages']:
            report += "\n📝 各用户忽略消息统计:\n"
            for user_id, count in stats['users_with_ignored_messages'].items():
                report += f"  用户 {user_id}: {count} 条\n"
        
        report += "\n💡 此优化功能防止用户在AI回复过程中发送重复消息，\n"
        report += "有效减少资源浪费并提高回复质量。"
        
        await update.message.reply_text(report)
        logger.info(f"管理员 {user_id} 查看了AI处理状态统计")
        
    except Exception as e:
        logger.error(f"获取AI处理状态统计失败: {str(e)}")
        await update.message.reply_text(f"获取统计信息失败: {str(e)}")


async def user_info(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """管理员查看用户信息的命令"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查是否为管理员
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    try:
        await update.message.reply_text(
            "爹！请输入要查看的用户ID："
            if lang == "zh-cn" else
            "Master! Please enter the user ID to view:"
        )

        # 设置管理员状态为等待用户ID输入
        admin_user_sessions[str(user_id)] = {"status": "waiting_for_user_id"}
        logger.info(f"管理员 {user_id} 开始查看用户信息流程")

    except TelegramError as e:
        logger.error(f"发送用户信息查询提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_admin_user_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理管理员在用户信息查看流程中的输入"""
    admin_id = str(update.effective_user.id)

    # 检查是否为管理员且在用户信息查看流程中
    if admin_id != MASTER_ID or admin_id not in admin_user_sessions:
        return False  # 不是管理员或不在流程中，返回False让其他处理器处理

    session = admin_user_sessions[admin_id]
    message_text = update.message.text.strip()

    try:
        if session["status"] == "waiting_for_user_id":
            # 处理用户ID输入
            target_user_id = message_text

            try:
                # 获取目标用户数据
                target_user_data = await get_user_data(target_user_id)

                # 格式化用户信息
                info_text = await format_user_info(target_user_id, target_user_data)

                await update.message.reply_text(info_text)

                # 询问是否修改
                await update.message.reply_text(
                    "爹！是否要修改此用户的数据？\n"
                    "回复 '是' 进入修改模式，回复 '否' 或其他内容退出。"
                )

                # 更新会话状态
                session["status"] = "waiting_for_modify_choice"
                session["target_user_id"] = target_user_id
                session["target_user_data"] = target_user_data

            except Exception as e:
                logger.error(f"获取用户 {target_user_id} 数据失败: {str(e)}")
                await update.message.reply_text(f"爹！获取用户 {target_user_id} 数据失败：{str(e)}")
                # 清除会话
                del admin_user_sessions[admin_id]

        elif session["status"] == "waiting_for_modify_choice":
            # 处理是否修改的选择
            if message_text.lower() in ["是", "yes", "y", "1"]:
                await update.message.reply_text(
                    "爹！请按以下格式输入修改内容：\n"
                    "会员状态:值 狗粮:值\n\n"
                    "会员状态可选值：\n"
                    "- 永久 (设为永久会员)\n"
                    "- 2024-12-31 (设置到期日期)\n"
                    "- 无 (取消会员)\n\n"
                    "狗粮：数字\n\n"
                    "示例：会员状态:永久 狗粮:100\n"
                    "示例：会员状态:2024-12-31 狗粮:50\n"
                    "示例：会员状态:无 狗粮:25"
                )
                session["status"] = "waiting_for_modification"
            else:
                await update.message.reply_text("爹！已退出用户信息查看。")
                del admin_user_sessions[admin_id]

        elif session["status"] == "waiting_for_modification":
            # 处理修改输入
            success = await process_user_modification(admin_id, message_text, session)
            if success:
                await update.message.reply_text("爹！用户数据修改成功！")
            else:
                await update.message.reply_text("爹！修改格式错误，请重新输入或回复 '退出' 结束。")
                return True  # 继续等待正确输入

            # 清除会话
            del admin_user_sessions[admin_id]

        return True  # 表示消息已被处理

    except Exception as e:
        logger.error(f"处理管理员用户信息输入失败: {str(e)}")
        await update.message.reply_text(f"爹！处理失败：{str(e)}")
        # 清除会话
        if admin_id in admin_user_sessions:
            del admin_user_sessions[admin_id]
        return True


async def format_user_info(user_id: str, user_data: dict) -> str:
    """格式化用户信息显示"""
    try:
        # 会员状态
        membership_status = "普通用户"
        if user_data.get("permanent_member", False):
            membership_status = "永久会员"
        elif user_data.get("membership_expiry"):
            try:
                expiry_date = datetime.fromisoformat(user_data["membership_expiry"])
                if datetime.now() < expiry_date:
                    membership_status = f"会员（到期：{expiry_date.strftime('%Y-%m-%d')}）"
                else:
                    membership_status = f"会员已过期（过期：{expiry_date.strftime('%Y-%m-%d')}）"
            except (ValueError, TypeError):
                membership_status = "会员状态异常"

        # 狗粮余额
        dog_food = user_data.get("dog_food", 0)
        daily_free_dog_food = user_data.get("daily_free_dog_food", 0)

        info_text = f"""📊 用户信息查询结果

👤 用户ID: {user_id}
💎 会员状态: {membership_status}
🦴 狗粮余额: {dog_food}
🎁 每日免费狗粮: {daily_free_dog_food}
📅 注册顺序: {user_data.get('join_order', '未知')}
🔊 语音功能: {'已开启' if user_data.get('voice_enabled', False) else '未开启'}
🚫 封禁状态: {'已封禁' if user_data.get('ban_until') else '正常'}"""

        return info_text

    except Exception as e:
        logger.error(f"格式化用户信息失败: {str(e)}")
        return f"格式化用户信息失败: {str(e)}"


async def process_user_modification(admin_id: str, input_text: str, session: dict) -> bool:
    """处理用户数据修改"""
    try:
        if input_text.strip().lower() in ["退出", "exit", "quit"]:
            return True

        target_user_id = session["target_user_id"]
        target_user_data = session["target_user_data"]

        # 解析输入格式：会员状态:值 狗粮:值
        parts = input_text.split()
        modifications = {}

        for part in parts:
            if ":" in part:
                key, value = part.split(":", 1)
                modifications[key.strip()] = value.strip()

        # 处理会员状态修改
        if "会员状态" in modifications:
            membership_value = modifications["会员状态"]
            if membership_value == "永久":
                target_user_data["permanent_member"] = 1  # 使用整数1而不是True
                target_user_data["membership_expiry"] = None
                logger.info(f"设置用户 {session['target_user_id']} 为永久会员")
            elif membership_value == "无":
                target_user_data["permanent_member"] = 0  # 使用整数0而不是False
                target_user_data["membership_expiry"] = None
                logger.info(f"取消用户 {session['target_user_id']} 的会员身份")
            else:
                # 尝试解析为日期
                try:
                    expiry_date = datetime.strptime(membership_value, "%Y-%m-%d")
                    target_user_data["permanent_member"] = 0  # 使用整数0而不是False
                    target_user_data["membership_expiry"] = expiry_date.isoformat()
                    logger.info(f"设置用户 {session['target_user_id']} 会员到期时间为: {expiry_date.isoformat()}")
                except ValueError:
                    logger.error(f"无效的日期格式: {membership_value}")
                    return False

        # 处理狗粮修改
        if "狗粮" in modifications:
            try:
                dog_food_value = int(modifications["狗粮"])
                target_user_data["dog_food"] = dog_food_value
            except ValueError:
                logger.error(f"无效的狗粮数值: {modifications['狗粮']}")
                return False

        # 保存修改后的用户数据
        await save_user_data(target_user_id, target_user_data)

        logger.info(f"管理员 {admin_id} 修改了用户 {target_user_id} 的数据: {modifications}")
        return True

    except Exception as e:
        logger.error(f"处理用户数据修改失败: {str(e)}")
        return False


async def clean_temp_files_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """手动清理临时文件的管理员命令"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查是否为管理员
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    try:
        current_time = datetime.now().timestamp()
        deleted_count = 0
        total_size = 0

        # 检查下载目录是否存在
        if not os.path.exists(DOWNLOAD_DIR):
            await update.message.reply_text(
                f"爹！下载目录不存在: {DOWNLOAD_DIR}"
                if lang == "zh-cn" else
                f"Master! Download directory doesn't exist: {DOWNLOAD_DIR}"
            )
            return

        # 获取清理前的文件统计
        all_files = os.listdir(DOWNLOAD_DIR)
        temp_files = []

        for filename in all_files:
            should_delete = False

            # 匹配各种临时文件模式
            # 音频文件
            if filename.endswith((".ogg", ".mp3", ".wav")):
                if (filename.startswith("temp_") or
                    filename.startswith("dice_proof_") or
                    filename.startswith("temp_voice_") or
                    filename.startswith("temp_segment_") or
                    "_compressed" in filename):
                    # 排除漂流瓶相关文件
                    if not filename.startswith("bottle_voice_"):
                        should_delete = True

            # 图片文件
            elif filename.endswith((".jpg", ".jpeg", ".png")):
                if (filename.startswith("dice_proof_") or
                    filename.startswith("temp_") or
                    "_compressed" in filename or
                    filename.startswith("dice_proof_frame_")):
                    should_delete = True

            # 视频文件
            elif filename.endswith((".mp4", ".avi", ".mov")):
                if (filename.startswith("dice_proof_video_") or
                    filename.startswith("temp_") or
                    "_" in filename and any(char.isdigit() for char in filename)):
                    should_delete = True

            if should_delete:
                file_path = os.path.join(DOWNLOAD_DIR, filename)
                try:
                    file_size = os.path.getsize(file_path)
                    file_mtime = os.path.getmtime(file_path)
                    age_hours = (current_time - file_mtime) / 3600
                    temp_files.append({
                        'path': file_path,
                        'name': filename,
                        'size': file_size,
                        'age_hours': age_hours
                    })
                except Exception as e:
                    logger.error(f"获取文件信息失败 {file_path}: {str(e)}")

        # 发送清理前的统计信息
        if not temp_files:
            await update.message.reply_text(
                "爹！没有找到需要清理的临时文件。"
                if lang == "zh-cn" else
                "Master! No temporary files found to clean."
            )
            return

        # 执行清理
        for file_info in temp_files:
            try:
                os.remove(file_info['path'])
                deleted_count += 1
                total_size += file_info['size']
                logger.info(f"手动清理临时文件: {file_info['path']}")
            except Exception as e:
                logger.error(f"删除临时文件失败 {file_info['path']}: {str(e)}")

        # 格式化文件大小
        def format_size(size_bytes):
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} MB"

        # 发送清理结果
        result_message = (
            f"爹！临时文件清理完成：\n"
            f"• 总文件数: {len(temp_files)}\n"
            f"• 成功删除: {deleted_count}\n"
            f"• 释放空间: {format_size(total_size)}\n"
            f"• 清理目录: {DOWNLOAD_DIR}"
            if lang == "zh-cn" else
            f"Master! Temporary file cleanup completed:\n"
            f"• Total files: {len(temp_files)}\n"
            f"• Successfully deleted: {deleted_count}\n"
            f"• Space freed: {format_size(total_size)}\n"
            f"• Cleanup directory: {DOWNLOAD_DIR}"
        )

        await update.message.reply_text(result_message)
        logger.info(f"管理员 {user_id} 手动清理了 {deleted_count} 个临时文件，释放 {format_size(total_size)} 空间")

    except Exception as e:
        logger.error(f"手动清理临时文件失败: {str(e)}", exc_info=True)
        await update.message.reply_text(
            f"爹！清理临时文件时出错: {str(e)}"
            if lang == "zh-cn" else
            f"Master! Error cleaning temporary files: {str(e)}"
        )
