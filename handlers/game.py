from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from logging_config import logger
from database import get_user_data, save_user_data
from dice_game import end_game as end_game_func


async def handle_dice_game(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    rules = (
        "🎲 **贱奴！骰子羞辱游戏规则** 🎲\n"
        "───────────────────\n"
        "1️⃣ 你这贱狗最多穿6件破布，爷要剥光你！\n"
        "2️⃣ 8轮调教，跟爷比骰子点数！\n"
        "3️⃣ 点数一样，你赢，自扇一巴掌给爷听响！\n"
        "4️⃣ 点数不同，爷赢，脱一件，拍裸照验货！\n"
        "5️⃣ 8轮没脱光，拍视频磕头10次谢爷！\n"
        "6️⃣ 全脱光，左右各扇5巴掌求饶！\n"
        "───────────────────\n"
        "🔥 跪好，点下面开始，别让爷等！"
        if lang == "zh-cn" else
        "🎲 **Filthy dog! Dice Humiliation Game Rules** 🎲\n"
        "───────────────────\n"
        "1️⃣ You filthy dog can wear up to 6 rags, I'll strip you bare!\n"
        "2️⃣ 8 rounds of training, compete with my dice rolls!\n"
        "3️⃣ Same roll, you win, slap yourself for me to hear!\n"
        "4️⃣ Different roll, I win, strip one piece, send a nude for inspection!\n"
        "5️⃣ Not naked after 8 rounds, send a video kowtowing 10 times to thank me!\n"
        "6️⃣ Fully naked, slap yourself 5 times on each side and beg!\n"
        "───────────────────\n"
        "🔥 Kneel, click below to start, don't keep me waiting!"
    )
    keyboard = [
        [InlineKeyboardButton("🔴 跪下开始" if lang == "zh-cn" else "🔴 Kneel and Start", callback_data="start_dice")],
        [InlineKeyboardButton("🏃 滚回去" if lang == "zh-cn" else "🏃 Get Lost", callback_data="back_to_main")]
    ]
    try:
        await query.edit_message_text(rules, reply_markup=InlineKeyboardMarkup(keyboard), parse_mode="Markdown")
        logger.info(f"用户 {user_id} 进入骰子游戏规则页面")
    except TelegramError as e:
        logger.error(f"发送骰子游戏规则失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！骰子游戏规则加载失败，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Dice game rules failed to load, try again later!"
        )


async def handle_start_dice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    user_data["dice_round"] = 1
    user_data["clothes"] = 6
    user_data["waiting_for_proof"] = False
    user_data["previous_clothes"] = 6  # 添加 previous_clothes 字段
    await save_user_data(user_id, user_data)
    try:
        await query.edit_message_text(
            f"第1轮，贱奴！你还剩{user_data['clothes']}件破布！快掷骰子（发送 /dice），别让爷等你这废物！"
            if lang == "zh-cn" else
            f"Round 1, filthy dog! You have {user_data['clothes']} rags left! Roll the dice (/dice) now, don't keep me waiting, you scum!"
        )
    except TelegramError as e:
        logger.error(f"发送开始骰子游戏提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_next_round(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    round_num = user_data["dice_round"]
    if user_data["waiting_for_proof"]:
        try:
            await query.edit_message_text(
                "狗东西！你输了还没献上证明！快拍照片或视频给爷检查，不然滚！"
                if lang == "zh-cn" else
                "You filthy thing! You lost and haven't sent proof! Send a photo or video for inspection, or get lost!"
            )
        except TelegramError as e:
            logger.info(f"群聊 {query.message.chat_id} 中发送等待证明提示失败，用户ID: {user_id}, 错误: {str(e)}，可能是 Bot 被禁言")
        return
    if round_num <= 8:
        try:
            await query.edit_message_text(
                f"第{round_num}轮，贱奴！你还剩{user_data['clothes']}件破布！快掷骰子（发送 /dice），别磨蹭！"
                if lang == "zh-cn" else
                f"Round {round_num}, filthy dog! You have {user_data['clothes']} rags left! Roll the dice (/dice) now, don't dawdle!"
            )
        except TelegramError as e:
            logger.info(f"群聊 {query.message.chat_id} 中发送下一轮提示失败，用户ID: {user_id}, 错误: {str(e)}，可能是 Bot 被禁言")
    else:
        await end_game_func(update, context, query.message.chat_id)


async def handle_end_game(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_data = await get_user_data(query.from_user.id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        await end_game_func(update, context, query.message.chat_id)
        await query.answer()
    except TelegramError as e:
        logger.error(f"结束游戏失败，用户ID: {query.from_user.id}, 错误: {str(e)}")
        await query.message.reply_text(
            "贱奴！结束游戏时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong ending the game, try again later!"
        )
