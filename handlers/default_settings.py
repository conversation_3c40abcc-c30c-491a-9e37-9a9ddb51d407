# -*- coding: utf-8 -*-
"""
默认设定配置文件
包含调教场景设定、主人设定和调教喜好的默认值
"""

# 调教场景设定及默认值
DEFAULT_SCENE_SETTINGS = {
    "location": "",
    "environment": "",
    "people": "主人，自己",
    "others": ""
}

# 男主人默认设定
DEFAULT_MALE_MASTER = {
    "name": "",
    "age": "",
    "occupation": "", 
    "clothing": "",
    "height": "",
    "weight": "",
    "hobbies": "",
    "penis_erect_length": "22",
    "penis_soft_length": "15", 
    "penis_thickness": "5",
    "shoe_size": "46"
}

# 女主人默认设定
DEFAULT_FEMALE_MASTER = {
    "name": "",
    "age": "", 
    "occupation": "",
    "clothing": "",
    "height": "",
    "weight": "",
    "hobbies": "",
    "bust": "91",
    "waist": "64",
    "hip": "93",
    "shoe_size": "37"
}

# 调教喜好默认设定
DEFAULT_TRAINING_PREFERENCES = {
    "liked_practices": [],  # 默认为空（保留兼容性）
    "acceptable_practices": [],  # 默认为空（保留兼容性）
    "extra_practices": "",  # 默认为空（保留兼容性）
    "slave_type_preferences": {},  # 新的奴种喜好数据结构
    "dog_name": "",  # 默认为空，避免重复称呼
    "gender": "男",  # 用户性别
    "age": "19",
    "relationship": "同学"
}

def normalize_master_gender(master_gender):
    """
    标准化主人性别值，确保兼容性
    
    Args:
        master_gender: 原始性别值
    
    Returns:
        str: 标准化后的性别值 ('male' 或 'female')
    """
    if master_gender == "female":
        return "female"
    else:
        # 对于None、空字符串、"male"或任何其他值，都默认为"male"
        # 这样确保对老用户的兼容性（原系统只有男主人）
        return "male"

def get_default_settings(setting_type, master_gender=None):
    """
    获取指定类型的默认设定
    
    Args:
        setting_type: 设定类型 ('scene_settings', 'master_settings', 'training_preferences')
        master_gender: 主人性别 ('male', 'female')，仅在获取master_settings时需要
    
    Returns:
        dict: 默认设定字典
    """
    if setting_type == "scene_settings":
        return DEFAULT_SCENE_SETTINGS.copy()
    elif setting_type == "master_settings":
        # 使用标准化的性别值
        normalized_gender = normalize_master_gender(master_gender)
        if normalized_gender == "female":
            return DEFAULT_FEMALE_MASTER.copy()
        else:  # 默认为男性（兼容老用户）
            return DEFAULT_MALE_MASTER.copy()
    elif setting_type == "training_preferences":
        return DEFAULT_TRAINING_PREFERENCES.copy()
    elif setting_type == "basic_info":
        # basic_info是首次设定时的基本信息，返回空字典
        return {
            "dog_name": "",
            "gender": "",
            "age": "",
            "relationship": ""
        }
    else:
        return {}

def merge_with_defaults(user_settings, setting_type, master_gender=None):
    """
    将用户设定与默认值合并，确保所有字段都有值
    
    Args:
        user_settings: 用户当前设定
        setting_type: 设定类型
        master_gender: 主人性别
    
    Returns:
        dict: 合并后的完整设定
    """
    defaults = get_default_settings(setting_type, master_gender)
    
    # 确保user_settings是字典
    if not isinstance(user_settings, dict):
        user_settings = {}
    
    # 合并设定，优先使用用户设定，缺失的字段使用默认值
    merged_settings = defaults.copy()
    for key, value in user_settings.items():
        if value is not None and value != "":
            merged_settings[key] = value
    
    return merged_settings

def validate_and_complete_user_data(user_data):
    """
    验证并完善用户数据，确保所有设定字段都有默认值
    
    Args:
        user_data: 用户数据字典
    
    Returns:
        dict: 完善后的用户数据
    """
    # 确保master_gender字段存在且有效（优先兼容老用户）
    if "master_gender" not in user_data or not user_data["master_gender"] or user_data["master_gender"] not in ["male", "female"]:
        user_data["master_gender"] = "male"  # 默认为男性，兼容原系统设计
    
    if "scene_settings" not in user_data:
        user_data["scene_settings"] = {}
    
    if "master_settings" not in user_data:
        user_data["master_settings"] = {}
    
    if "training_preferences" not in user_data:
        user_data["training_preferences"] = {}
    
    # 合并默认值
    user_data["scene_settings"] = merge_with_defaults(
        user_data["scene_settings"], "scene_settings"
    )
    
    user_data["master_settings"] = merge_with_defaults(
        user_data["master_settings"], "master_settings", user_data["master_gender"]
    )
    
    user_data["training_preferences"] = merge_with_defaults(
        user_data["training_preferences"], "training_preferences"
    )
    
    return user_data

# 字段标签映射（用于显示和解析）
FIELD_LABELS = {
    "scene_settings": {
        "zh-cn": {
            "location": "调教地点",
            "environment": "环境描述", 
            "people": "相关人员",
            "others": "其他"
        },
        "en": {
            "location": "Training Location",
            "environment": "Environment Description",
            "people": "Related People", 
            "others": "Others"
        }
    },
    "master_settings": {
        "male": {
            "zh-cn": {
                "name": "姓名",
                "occupation": "职业",
                "clothing": "穿着",
                "height": "身高（厘米）",
                "weight": "体重（千克）",
                "hobbies": "爱好",
                "penis_erect_length": "鸡巴勃起长（厘米）",
                "penis_soft_length": "鸡巴软长度（厘米）",
                "penis_thickness": "鸡巴勃起粗（厘米）",
                "shoe_size": "脚码"
            },
            "en": {
                "name": "Name",
                "occupation": "Occupation",
                "clothing": "Clothing",
                "height": "Height (cm)",
                "weight": "Weight (kg)",
                "hobbies": "Hobbies",
                "penis_erect_length": "Penis Erect Length (cm)",
                "penis_soft_length": "Penis Soft Length (cm)",
                "penis_thickness": "Penis Thickness (cm)",
                "shoe_size": "Shoe Size"
            }
        },
        "female": {
            "zh-cn": {
                "name": "姓名",
                "occupation": "职业",
                "clothing": "穿着",
                "height": "身高（厘米）",
                "weight": "体重（千克）",
                "hobbies": "爱好",
                "bust": "胸围（厘米）",
                "waist": "腰围（厘米）",
                "hip": "臀围（厘米）",
                "shoe_size": "脚码"
            },
            "en": {
                "name": "Name",
                "occupation": "Occupation",
                "clothing": "Clothing",
                "height": "Height (cm)",
                "weight": "Weight (kg)",
                "hobbies": "Hobbies",
                "bust": "Bust (cm)",
                "waist": "Waist (cm)",
                "hip": "Hip (cm)",
                "shoe_size": "Shoe Size"
            }
        }
    },
    "training_preferences": {
        "zh-cn": {
            "liked_practices": "喜欢的玩法",
            "acceptable_practices": "可接受的玩法",
            "extra_practices": "其他特殊喜好",
            "dog_name": "犬名",
            "gender": "性别",
            "age": "年龄", 
            "relationship": "与主人的关系"
        },
        "en": {
            "liked_practices": "Liked Practices", 
            "acceptable_practices": "Acceptable Practices",
            "extra_practices": "Other Special Preferences",
            "dog_name": "Dog Name",
            "gender": "Gender",
            "age": "Age",
            "relationship": "Relationship with Master"
        }
    },
    "basic_info": {
        "zh-cn": {
            "dog_name": "犬名",
            "gender": "性别",
            "age": "年龄", 
            "relationship": "与主人的关系"
        },
        "en": {
            "dog_name": "Dog Name",
            "gender": "Gender",
            "age": "Age",
            "relationship": "Relationship with Master"
        }
    }
}

def get_field_labels(setting_type, lang="zh-cn", master_gender="male"):
    """
    获取字段标签
    
    Args:
        setting_type: 设定类型
        lang: 语言 ('zh-cn', 'en')
        master_gender: 主人性别 (仅在master_settings时需要)
    
    Returns:
        dict: 字段标签字典
    """
    if setting_type == "master_settings":
        # 使用标准化的性别值，确保兼容性
        normalized_gender = normalize_master_gender(master_gender)
        return FIELD_LABELS[setting_type][normalized_gender][lang]
    else:
        return FIELD_LABELS[setting_type][lang]

def format_settings_template(setting_type, lang="zh-cn", master_gender="male"):
    """
    生成设定模板文本（填充默认值）
    
    Args:
        setting_type: 设定类型
        lang: 语言
        master_gender: 主人性别
    
    Returns:
        str: 模板文本
    """
    labels = get_field_labels(setting_type, lang, master_gender)
    defaults = get_default_settings(setting_type, master_gender)
    
    template_lines = []
    for key, label in labels.items():
        default_value = defaults.get(key, "")
        if isinstance(default_value, list):
            default_value = ", ".join(default_value) if default_value else ""
        template_lines.append(f"{label}：{default_value}")
    
    return "\n".join(template_lines)

def format_empty_template(setting_type, lang="zh-cn", master_gender="male"):
    """
    生成空模板文本（首次设置使用）
    
    Args:
        setting_type: 设定类型
        lang: 语言
        master_gender: 主人性别
    
    Returns:
        str: 空模板文本
    """
    labels = get_field_labels(setting_type, lang, master_gender)
    
    template_lines = []
    for key, label in labels.items():
        template_lines.append(f"{label}：")
    
    return "\n".join(template_lines)

def format_user_settings_template(setting_type, user_settings, lang="zh-cn", master_gender="male"):
    """
    生成用户设定模板文本（编辑时使用，填充用户当前数据）
    
    Args:
        setting_type: 设定类型
        user_settings: 用户当前设定
        lang: 语言
        master_gender: 主人性别
    
    Returns:
        str: 填充用户数据的模板文本
    """
    labels = get_field_labels(setting_type, lang, master_gender)
    
    template_lines = []
    for key, label in labels.items():
        user_value = user_settings.get(key, "")
        if isinstance(user_value, list):
            user_value = ", ".join(user_value) if user_value else ""
        template_lines.append(f"{label}：{user_value}")
    
    return "\n".join(template_lines) 