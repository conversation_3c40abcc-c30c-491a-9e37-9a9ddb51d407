import time
import asyncio
import re
from collections import defaultdict
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from datetime import datetime
from logging_config import logger
from database import get_user_data, save_user_data
from utils import get_main_menu, get_redeem_code_menu, get_quick_reply_menu
from config import (
    MASTER_ID, BOT_USERNAME, SEARCH_GROUP_ID
)
from globals import global_ai_queue
from ai_dialogue import (
    download_and_process_image, download_and_process_video, handle_broadcast_message,
    private_chat_with_user
)


message_buffer = defaultdict(list)
message_tasks = {}


async def process_buffered_messages(user_id, chat_id, context):
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if not message_buffer[user_id]:
        return
    combined_message = "\n".join(message_buffer[user_id])
    message_buffer[user_id].clear()
    if user_id in message_tasks:
        message_tasks.pop(user_id)
    try:
        await global_ai_queue.put((user_id, combined_message, chat_id, False))
        logger.info(f"用户 {user_id} 缓冲消息已处理并加入队列：{combined_message}")
    except asyncio.QueueFull:
        logger.warning(f"队列已满，用户 {user_id} 消息被丢弃")
        try:
            await context.bot.send_message(
                chat_id=chat_id,
                text="贱奴！爷忙得要死，稍后再来跪舔！"
                if lang == "zh-cn" else
                "Filthy dog! I'm swamped, come back later to grovel!"
            )
        except TelegramError as e:
            logger.error(f"发送队列满提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def buffer_message(user_id, message, chat_id, context):
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    message_history = user_data.get("message_history", [])
    
    if message_history:
        last_message = message_history[-1]
        last_message_time = user_data.get(f"last_message_time_{user_id}", 0)
        current_time = time.time()
        time_diff = current_time - last_message_time
        if message == last_message and time_diff < 5:
            logger.debug(f"用户 {user_id} 短时间重复消息: {message}，忽略")
            try:
                await context.bot.send_message(
                    chat_id=chat_id,
                    text="贱奴！别重复废话，爷烦了！换点新鲜的！"
                    if lang == "zh-cn" else
                    "Filthy dog! Stop repeating nonsense, I'm annoyed! Say something new!"
                )
            except TelegramError as e:
                logger.error(f"发送重复消息提示失败，用户ID: {user_id}, 错误: {str(e)}")
            return

    if len(message_buffer[user_id]) >= 10:
        logger.warning(f"用户 {user_id} 缓冲区已满，忽略新消息")
        try:
            await context.bot.send_message(
                chat_id=chat_id,
                text="贱奴！你发太快了，爷忙不过来，慢点来！"
                if lang == "zh-cn" else
                "Filthy dog! You're sending too fast, I can't keep up, slow down!"
            )
        except TelegramError as e:
            logger.error(f"发送缓冲区满提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    message_buffer[user_id].append(message)
    
    if user_id in message_tasks:
        message_tasks[user_id].cancel()
    
    last_message_time = context.user_data.get(f"last_message_time_{user_id}", 0)
    current_time = time.time()
    time_diff = current_time - last_message_time
    delay = 0.2 if time_diff < 1 else 0.5
    context.user_data[f"last_message_time_{user_id}"] = current_time
    
    message_tasks[user_id] = asyncio.create_task(delayed_process(user_id, chat_id, context, delay))
    logger.info(f"用户 {user_id} 消息已缓冲，预计延迟 {delay} 秒处理")


async def delayed_process(user_id, chat_id, context, delay):
    try:
        await asyncio.sleep(delay)
        await process_buffered_messages(user_id, chat_id, context)
    except asyncio.CancelledError:
        logger.debug(f"用户 {user_id} 的延迟处理任务被取消")
    except Exception as e:
        logger.error(f"延迟处理消息失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_share_link(user_id: str, shared_by: str, context: ContextTypes.DEFAULT_TYPE):
    sharer_id = shared_by.split("_")[-1]
    user_data = await get_user_data(user_id)
    if user_data["shared_by"]:
        logger.info(f"用户 {user_id} 已通过其他分享链接注册，忽略新链接: {shared_by}")
        return

    # 设置新用户的分享者信息
    user_data["shared_by"] = sharer_id
    await save_user_data(user_id, user_data)

    # 更新分享者的分享统计
    try:
        sharer_data = await get_user_data(sharer_id)

        # 检查新用户是否为新用户（根据 has_used_bot 字段判断）
        if not user_data.get("has_used_bot", False):
            # 新用户，增加 daily_new_shares
            sharer_data["daily_new_shares"] = sharer_data.get("daily_new_shares", 0) + 1
            # 给分享者奖励3个狗粮
            sharer_data["dog_food"] = sharer_data.get("dog_food", 0) + 3
            reward_message = "获得3个狗粮🦴奖励！"
        else:
            # 老用户，增加 daily_old_shares
            sharer_data["daily_old_shares"] = sharer_data.get("daily_old_shares", 0) + 1
            reward_message = "但这是老用户，无奖励。"

        await save_user_data(sharer_id, sharer_data)

        logger.info(f"用户 {user_id} 通过分享链接注册，分享者: {sharer_id}，新用户: {not user_data.get('has_used_bot', False)}")

        # 发送通知给分享者
        await context.bot.send_message(
            chat_id=sharer_id,
            text=f"贱奴！有新狗通过你的链接注册，ID: {user_id}！{reward_message}"
            if sharer_data.get("preferred_language", "zh-cn") == "zh-cn" else
            f"Filthy dog! A new dog registered via your link, ID: {user_id}! {reward_message}"
        )
    except Exception as e:
        logger.error(f"处理分享奖励失败，分享者ID: {sharer_id}, 新用户ID: {user_id}, 错误: {str(e)}")
        # 即使奖励处理失败，也要发送基本通知
        try:
            await context.bot.send_message(
                chat_id=sharer_id,
                text=f"贱奴！有新狗通过你的链接注册，ID: {user_id}！"
                if user_data.get("preferred_language", "zh-cn") == "zh-cn" else
                f"Filthy dog! A new dog registered via your link, ID: {user_id}!"
            )
        except TelegramError as e:
            logger.error(f"发送分享通知失败，分享者ID: {sharer_id}, 新用户ID: {user_id}, 错误: {str(e)}")


async def handle_back_to_main(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    reply_markup = get_main_menu(user_id)
    quick_reply_menu = get_quick_reply_menu(user_id)
    
    try:
        await query.edit_message_text(
            "🔥 贱奴！跪在爷面前，准备接受调教吧！🔥\n"
            "直接打字说话，别浪费爷的时间！\n"
            "───────────────────"
            if lang == "zh-cn" else
            "🔥 Filthy dog! Kneel before me, ready for training! 🔥\n"
            "Type directly, don't waste my time!\n"
            "───────────────────",
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
        
        # 发送快捷回复菜单
        await query.message.reply_text(
            "快捷菜单已启用",  # 使用明确的提示文字
            reply_markup=quick_reply_menu
        )
        
        logger.info(f"用户 {user_id} 返回主菜单")
        # 清理上下文
        context.user_data.pop("setting_type", None)
        context.user_data.pop("is_edit", None)
        context.user_data.pop("setting_step", None)
        context.user_data.pop("setting_delay", None)
        context.user_data.pop("setting_sentences", None)
        context.user_data.pop("liked_practices", None)
        context.user_data.pop("acceptable_practices", None)
        context.user_data.pop("liked_practices_edit", None)
        context.user_data.pop("acceptable_practices_edit", None)
        context.user_data.pop("setting_step_edit", None)
        if "remind_task" in context.user_data:
            context.user_data["remind_task"].cancel()
            context.user_data.pop("remind_task", None)
    except TelegramError as e:
        logger.error(f"发送主菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.message.reply_text(
            "贱奴！返回主菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Failed to return to main menu, try again later!",
            reply_markup=reply_markup
        )


async def handle_register(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        await query.edit_message_text(
            "爷还在考虑怎么测试你的奴性，滚回去跪着等！"
            if lang == "zh-cn" else
            "I'm still thinking how to test your slavishness, go kneel and wait!",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
        )
    except TelegramError as e:
        logger.error(f"发送注册提示失败，用户ID: {user_id}, 错误: {str(e)}")
