# handlers/settings.py
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError
import asyncio
from config import TTS_CONFIG
from utils import get_main_menu, send_with_retry, PRACTICES, SLAVE_TYPES, SLAVE_TYPE_ORDER
from logging_config import logger
from database import get_user_data, save_user_data
from .default_settings import (
    get_default_settings, 
    merge_with_defaults, 
    get_field_labels, 
    format_settings_template,
    FIELD_LABELS,
    format_empty_template,
    format_user_settings_template
)
from prompt_configs import check_membership_status, has_custom_mode, get_custom_mode_content
import asyncio
import re

def _parse_scene_free_text(user_text, lang="zh-cn"):
    """
    简单处理用户输入的场景自由文本
    直接保存为场景描述

    Args:
        user_text: 用户输入的文本
        lang: 语言

    Returns:
        dict: 解析后的场景设定
    """
    scene_settings = {}
    text = user_text.strip()

    # 直接保存为场景描述（others字段）
    scene_settings["others"] = text

    return scene_settings

async def handle_confirm_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    logger.info(f"用户 {user_id} 确认开始设置")
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 清空旧的设定数据，允许覆盖
    user_data["training_preferences"] = {}
    user_data["master_settings"] = {}
    user_data["scene_settings"] = {}
    await save_user_data(user_id, user_data)
    logger.info(f"用户 {user_id} 旧设定数据已清空，准备重新设置")
    
    # 修改为从主人设定开始
    context.user_data["setting_type"] = "master_settings"
    context.user_data["setting_step"] = 1
    
    # 开始主人性别选择
    keyboard = [
        [InlineKeyboardButton("男性主人" if lang == "zh-cn" else "Male Master", callback_data="select_master_gender_male")],
        [InlineKeyboardButton("女性主人" if lang == "zh-cn" else "Female Master", callback_data="select_master_gender_female")],
        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
    ]
    
    try:
        await query.edit_message_text(
            text=(
                "🔥 第一部分：定义你的主人！🔥\n"
                "首先选择主人的性别："
                if lang == "zh-cn" else
                "🔥 Part 1: Define Your Master! 🔥\n"
                "First, choose your master's gender:"
            ),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except TelegramError as e:
        logger.error(f"发送主人性别选择菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                text=(
                    "🔥 第一部分：定义你的主人！🔥\n"
                    "首先选择主人的性别："
                    if lang == "zh-cn" else
                    "🔥 Part 1: Define Your Master! 🔥\n"
                    "First, choose your master's gender:"
                ),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_select_liked(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    selected_practice = query.data.split("_")[2]
    logger.info(f"用户 {user_id} 选择了喜欢的玩法: {selected_practice}")

    if "liked_practices" not in context.user_data:
        context.user_data["liked_practices"] = []
    if selected_practice not in context.user_data["liked_practices"]:
        context.user_data["liked_practices"].append(selected_practice)

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    keyboard = []
    available_practices = [p for p in PRACTICES if p not in context.user_data["liked_practices"]]
    for i in range(0, len(available_practices), 5):
        row = [InlineKeyboardButton(available_practices[j], callback_data=f"select_liked_{available_practices[j]}")
               for j in range(i, min(i + 5, len(available_practices)))]
        keyboard.append(row)
    keyboard.append([InlineKeyboardButton("完成选择" if lang == "zh-cn" else "Finish Selection", callback_data="finish_liked_practices")])
    keyboard.append([InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_1")])
    keyboard.append([InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")])

    try:
        await query.edit_message_text(
            text=(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "1. 已选择喜欢的玩法：" + ', '.join(context.user_data['liked_practices']) + "\n"
                "继续选择你喜欢的玩法（可多选，选完点击'完成选择'）："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "1. Selected favorite practices: " + ', '.join(context.user_data['liked_practices']) + "\n"
                "Continue selecting your favorite practices (multiple choices, click 'Finish Selection' when done):"
            ),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except TelegramError as e:
        logger.error(f"更新喜欢的玩法选择菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                text=(
                    "🔥 第二部分：正视你内心深处的自己！🔥\n"
                    "1. 已选择喜欢的玩法：" + ', '.join(context.user_data['liked_practices']) + "\n"
                    "继续选择你喜欢的玩法（可多选，选完点击'完成选择'）："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Face Your Inner Self! 🔥\n"
                    "1. Selected favorite practices: " + ', '.join(context.user_data['liked_practices']) + "\n"
                    "Continue selecting your favorite practices (multiple choices, click 'Finish Selection' when done):"
                ),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_finish_liked_practices(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    logger.info(f"用户 {user_id} 完成喜欢的玩法选择")

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    user_data["training_preferences"]["liked_practices"] = context.user_data.get("liked_practices", [])
    await save_user_data(user_id, user_data)
    context.user_data["setting_step"] = 2
    context.user_data["acceptable_practices"] = []

    available_practices = [p for p in PRACTICES if p not in context.user_data["liked_practices"]]
    keyboard = []
    for i in range(0, len(available_practices), 5):
        row = [InlineKeyboardButton(available_practices[j], callback_data=f"select_acceptable_{available_practices[j]}")
               for j in range(i, min(i + 5, len(available_practices)))]
        keyboard.append(row)
    keyboard.append([InlineKeyboardButton("完成选择" if lang == "zh-cn" else "Finish Selection", callback_data="finish_acceptable_practices")])
    keyboard.append([InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_2")])
    keyboard.append([InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")])

    try:
        await query.edit_message_text(
            text=(
                f"🔥 第二部分：正视你内心深处的自己！🔥\n"
                f"2. 选择一些你不太喜欢，但主人要求你会做的玩法（可多选，选完点击'完成选择'）："
                if lang == "zh-cn" else
                f"🔥 Part 2: Face Your Inner Self! 🔥\n"
                f"2. Select practices you don't love but would do if Master demands (multiple choices, click 'Finish Selection' when done):"
            ),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except TelegramError as e:
        logger.error(f"发送可接受的玩法选择菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                text=(
                    f"🔥 第二部分：正视你内心深处的自己！🔥\n"
                    f"2. 选择一些你不太喜欢，但主人要求你会做的玩法（可多选，选完点击'完成选择'）："
                    if lang == "zh-cn" else
                    f"🔥 Part 2: Face Your Inner Self! 🔥\n"
                    f"2. Select practices you don't love but would do if Master demands (multiple choices, click 'Finish Selection' when done):"
                ),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_select_acceptable(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    selected_practice = query.data.split("_")[2]
    logger.info(f"用户 {user_id} 选择了可接受的玩法: {selected_practice}")

    if "acceptable_practices" not in context.user_data:
        context.user_data["acceptable_practices"] = []
    if selected_practice not in context.user_data["acceptable_practices"]:
        context.user_data["acceptable_practices"].append(selected_practice)

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    available_practices = [p for p in PRACTICES if p not in context.user_data["liked_practices"] and p not in context.user_data["acceptable_practices"]]
    keyboard = []
    for i in range(0, len(available_practices), 5):
        row = [InlineKeyboardButton(available_practices[j], callback_data=f"select_acceptable_{available_practices[j]}")
               for j in range(i, min(i + 5, len(available_practices)))]
        keyboard.append(row)
    keyboard.append([InlineKeyboardButton("完成选择" if lang == "zh-cn" else "Finish Selection", callback_data="finish_acceptable_practices")])
    keyboard.append([InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_2")])
    keyboard.append([InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")])

    try:
        await query.edit_message_text(
            text=(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "2. 已选择可接受的玩法：{}\n"
                "继续选择你不太喜欢，但主人要求会做的玩法（可多选，选完点击'完成选择'）：".format(', '.join(context.user_data['acceptable_practices']))
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "2. Selected acceptable practices: {}\n"
                "Continue selecting practices you don't love but would do if Master demands (multiple choices, click 'Finish Selection' when done):".format(', '.join(context.user_data['acceptable_practices']))
            ),
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except TelegramError as e:
        logger.error(f"更新可接受的玩法选择菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                text=(
                    "🔥 第二部分：正视你内心深处的自己！🔥\n"
                    "2. 已选择可接受的玩法：{}\n"
                    "继续选择你不太喜欢，但主人要求会做的玩法（可多选，选完点击'完成选择'）：".format(', '.join(context.user_data['acceptable_practices']))
                    if lang == "zh-cn" else
                    "🔥 Part 2: Face Your Inner Self! 🔥\n"
                    "2. Selected acceptable practices: {}\n"
                    "Continue selecting practices you don't love but would do if Master demands (multiple choices, click 'Finish Selection' when done):".format(', '.join(context.user_data['acceptable_practices']))
                ),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_finish_acceptable_practices(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    logger.info(f"用户 {user_id} 完成可接受的玩法选择")

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    user_data["training_preferences"]["acceptable_practices"] = context.user_data.get("acceptable_practices", [])
    await save_user_data(user_id, user_data)
    context.user_data["setting_step"] = 3

    try:
        await query.edit_message_text(
            text=(
                "🔥 第一部分：正视你内心深处的自己！🔥\n"
                "3. 列出一些上述列表中不存在但是你想尝试的玩法（没有请填'无'或点击'无需补充'）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 1: Face Your Inner Self! 🔥\n"
                "3. List any practices not in the above list that you'd like to try (enter 'None' or click 'No Extra Practices'):\n"
                "Enter in the chat:"
            ),
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("无需补充" if lang == "zh-cn" else "No Extra Practices", callback_data="no_extra_practices")],
                [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_3")],
                [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
            ])
        )
    except TelegramError as e:
        logger.error(f"发送额外玩法输入提示失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                text=(
                    "🔥 第二部分：正视你内心深处的自己！🔥\n"
                    "3. 列出一些上述列表中不存在但是你想尝试的玩法（没有请填'无'或点击'无需补充'）：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Face Your Inner Self! 🔥\n"
                    "3. List any practices not in the above list that you'd like to try (enter 'None' or click 'No Extra Practices'):\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("无需补充" if lang == "zh-cn" else "No Extra Practices", callback_data="no_extra_practices")],
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_3")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_no_extra_practices(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    logger.info(f"用户 {user_id} 选择无需补充额外玩法")

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    user_data["training_preferences"]["extra_practices"] = "无" if lang == "zh-cn" else "None"
    await save_user_data(user_id, user_data)

    # 修改：在选择"无需补充"后，将设定步骤设置为 4，进入犬名输入步骤
    context.user_data["setting_step"] = 4

    try:
        await query.edit_message_text(
            text=(
                "🔥 第一部分：正视你内心深处的自己！🔥\n"
                "4. 给自己起一个犬名（不能为空）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 1: Face Your Inner Self! 🔥\n"
                "4. Choose a dog name for yourself (cannot be empty):\n"
                "Enter in the chat:"
            ),
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_4")],
                [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
            ])
        )
        logger.info(f"用户 {user_id} 选择无需补充额外玩法，进入犬名输入步骤")
    except TelegramError as e:
        logger.error(f"发送犬名输入提示失败，用户ID: {user_id}, 错误: {str(e)}")
        # 回退：发送新消息
        try:
            await query.message.reply_text(
                text=(
                    "🔥 第一部分：正视你内心深处的自己！🔥\n"
                    "4. 给自己起一个犬名（不能为空）：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 1: Face Your Inner Self! 🔥\n"
                    "4. Choose a dog name for yourself (cannot be empty):\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_4")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        except TelegramError as e2:
            logger.error(f"发送新消息失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    setting_type = query.data
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    settings = user_data[setting_type]
    
    # 导入会员检查函数
    from prompt_configs import check_membership_status
    
    # 检查会员状态
    is_member = check_membership_status(user_data)

    try:
        settings_text = ""
        if setting_type == "training_preferences":
            settings_text = (
                f"喜欢的玩法：{', '.join(settings.get('liked_practices', [])) or ('无' if lang == 'zh-cn' else 'None')}\n"
                f"可接受的玩法：{', '.join(settings.get('acceptable_practices', [])) or ('无' if lang == 'zh-cn' else 'None')}\n"
                f"额外玩法：{settings.get('extra_practices', '无' if lang == 'zh-cn' else 'None')}\n"
                f"犬名：{settings.get('dog_name', '未设置' if lang == 'zh-cn' else 'Not set')}\n"
                f"年龄：{settings.get('age', '未设置' if lang == 'zh-cn' else 'Not set')}\n"
                f"关系：{settings.get('relationship', '未设置' if lang == 'zh-cn' else 'Not set')}"
            ) if lang == "zh-cn" else (
                f"Favorite practices: {', '.join(settings.get('liked_practices', [])) or 'None'}\n"
                f"Acceptable practices: {', '.join(settings.get('acceptable_practices', [])) or 'None'}\n"
                f"Extra practices: {settings.get('extra_practices', 'None')}\n"
                f"Dog name: {settings.get('dog_name', 'Not set')}\n"
                f"Age: {settings.get('age', 'Not set')}\n"
                f"Relationship: {settings.get('relationship', 'Not set')}"
            )
        else:
            settings_text = "\n".join([f"{k}：{v}" for k, v in settings.items() if v]) or ("未设置" if lang == "zh-cn" else "Not set")

        keyboard = [
            [InlineKeyboardButton("修改" if lang == "zh-cn" else "Edit", callback_data=f"edit_{setting_type}")],
            [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
        ]
        
        # 显示🦴消耗信息或会员免费修改提示
        cost_info = ""
        if is_member:
            cost_info = "会员免费修改" if lang == "zh-cn" else "Free edit for members"
        else:
            cost_info = "每次修改需要15🦴" if lang == "zh-cn" else "15🦴 per edit"
        
        new_message = (
            f"🔥 贱狗！你当前的{setting_type.replace('_', ' ')}：🔥\n{settings_text}\n"
            f"{cost_info}"
            if lang == "zh-cn" else
            f"🔥 Filthy dog! Your current {setting_type.replace('_', ' ')}: 🔥\n{settings_text}\n"
            f"{cost_info}"
        )

        if query.message.text != new_message or query.message.reply_markup != InlineKeyboardMarkup(keyboard):
            await query.edit_message_text(
                new_message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        else:
            await query.answer("贱奴！当前设置未变更，别重复点！" if lang == "zh-cn" else "Filthy dog! No changes, stop clicking!")
    except TelegramError as e:
        logger.error(f"发送调教设置菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text("贱奴！爷弄设置菜单时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! My system choked on the settings menu, try again later!")
        except TelegramError as e2:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_edit_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    setting_type = query.data[5:]
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 导入会员检查函数
    from prompt_configs import check_membership_status
    
    # 检查会员状态
    is_member = check_membership_status(user_data)
    
    # 检查🦴余额（非会员需要15🦴）
    dog_food_total = user_data.get("daily_free_dog_food", 0) + user_data.get("dog_food", 0)
    can_edit = is_member or dog_food_total >= 15

    try:
        if can_edit:
            if setting_type == "training_preferences":
                # 使用新的奴种分类系统进行编辑
                context.user_data["setting_type"] = "slave_type_preferences"
                context.user_data["is_edit"] = True  # 明确设置为编辑模式
                context.user_data["current_slave_type_index"] = 0  # 从第一个奴种开始
                
                # 初始化奴种喜好数据结构
                if "slave_type_preferences" not in user_data["training_preferences"]:
                    user_data["training_preferences"]["slave_type_preferences"] = {}

                cost_reminder = ""
                if not is_member:
                    cost_reminder = "\n⚠️ 修改将消耗15🦴" if lang == "zh-cn" else "\n⚠️ Editing will cost 15🦴"

                # 显示编辑说明
                await query.edit_message_text(
                    text=(
                        f"🔥 修改调教喜好：奴种分类设定！🔥\n\n"
                        f"将按9个奴种分类逐一设定你的玩法喜好。\n"
                        f"每个玩法可选择：💖超级喜欢 / ✅可以被要求 / ❌明确拒绝\n\n"
                        f"即将开始第1个奴种设定...{cost_reminder}"
                        if lang == "zh-cn" else
                        f"🔥 Edit Training Preferences: Slave Type Categories! 🔥\n\n"
                        f"Will set your play preferences across 9 slave type categories.\n"
                        f"Each practice can be: 💖Super Love / ✅Acceptable / ❌Refuse\n\n"
                        f"Starting with the 1st slave type...{cost_reminder}"
                    ),
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("开始设定" if lang == "zh-cn" else "Start Setup", callback_data="start_slave_edit_0")],
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
            else:
                # 对于主人设定，先让用户选择性别
                if setting_type == "master_settings":
                    keyboard = [
                        [InlineKeyboardButton("男性主人", callback_data="select_master_gender_male_edit")],
                        [InlineKeyboardButton("女性主人", callback_data="select_master_gender_female_edit")],
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ]
                    
                    cost_reminder = ""
                    if not is_member:
                        cost_reminder = f"\n⚠️ 修改将消耗15🦴" if lang == "zh-cn" else f"\n⚠️ Editing will cost 15🦴"
                    
                    await query.edit_message_text(
                        text=(
                            f"🔥 贱狗！修改你的主人设定！🔥\n"
                            "首先选择主人的性别：{}"
                            .format(cost_reminder)
                            if lang == "zh-cn" else
                            f"🔥 Filthy dog! Edit your master settings! 🔥\n"
                            "First, choose your master's gender:{}"
                            .format(cost_reminder)
                        ),
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                    context.user_data["setting_type"] = "master_gender_edit"
                    context.user_data["is_edit"] = True
                    return
                
                # 添加🦴消耗提示
                cost_reminder = ""
                if not is_member:
                    cost_reminder = f"\n⚠️ 修改将消耗15🦴" if lang == "zh-cn" else f"\n⚠️ Editing will cost 15🦴"
                
                # 第一条消息：描述性语言
                if setting_type == "scene_settings":
                    description_text = (
                        f"🔥 贱狗！修改你的场景设定！🔥\n"
                        "你可以：\n"
                        "1. 按模板格式填写（复制下一条消息的模板）\n"
                        "2. 直接输入任何场景描述文字\n"
                        "不按模板也没关系，输入什么都会保存为场景设定！{}"
                        .format(cost_reminder)
                        if lang == "zh-cn" else
                        f"🔥 Filthy dog! Edit your scene settings! 🔥\n"
                        "You can:\n"
                        "1. Fill in the template format (copy the template from the next message)\n"
                        "2. Simply enter any scene description text\n"
                        "It's okay not to follow the template, whatever you enter will be saved as scene settings!{}"
                        .format(cost_reminder)
                    )
                else:
                    description_text = (
                        f"🔥 贱狗！修改你的{setting_type.replace('_', ' ')}！🔥\n"
                        "请按以下格式填写信息（每项一行，空行表示跳过）。\n"
                        "可直接复制下一条消息的模板进行填写：{}"
                        .format(cost_reminder)
                        if lang == "zh-cn" else
                        f"🔥 Filthy dog! Edit your {setting_type.replace('_', ' ')}! 🔥\n"
                        "Please fill in the information in the format below (one per line, leave blank to skip).\n"
                        "You can copy the template from the next message:{}"
                        .format(cost_reminder)
                    )
                
                # 第二条消息：模板（使用用户当前数据填充）
                if setting_type == "master_settings":
                    master_gender = user_data.get("master_gender", "male")
                    template_text = format_user_settings_template("master_settings", user_data.get("master_settings", {}), lang, master_gender)
                else:  # scene_settings
                    template_text = format_user_settings_template("scene_settings", user_data.get("scene_settings", {}), lang)
                
                # 发送描述性消息
                await query.edit_message_text(
                    description_text,
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
                )
                # 发送模板消息
                await query.message.reply_text(
                    template_text
                )
                context.user_data["setting_type"] = setting_type
                context.user_data["is_edit"] = True
        else:
            # 🦴不足，显示充值提示
            keyboard = [
                [InlineKeyboardButton("充值🦴" if lang == "zh-cn" else "Recharge🦴", callback_data="pay_membership")],
                [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
            ]
            await query.edit_message_text(
                f"🔥 贱狗！你的🦴不足15个，无法修改设定！去充值吧！🔥\n当前🦴余额：{dog_food_total}个"
                if lang == "zh-cn" else
                f"🔥 Filthy dog! You don't have enough🦴 (need 15), can't edit settings! Go recharge! 🔥\nCurrent🦴 balance: {dog_food_total}",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
    except TelegramError as e:
        logger.error(f"发送编辑设置菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text("贱奴！爷弄编辑菜单时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! My system choked on the edit menu, try again later!")
        except TelegramError as e2:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_setting_input(update: Update, context: ContextTypes.DEFAULT_TYPE, user_data):
    user_id = update.effective_user.id
    message = update.message.text.strip()
    setting_type = context.user_data.get("setting_type")
    is_edit = context.user_data.get("is_edit", False)  # 获取是否为编辑模式
    logger.info(f"用户 {user_id} 输入设定，类型: {setting_type}, 编辑模式: {is_edit}, 内容: {message}")

    lang = user_data.get("preferred_language", "zh-cn")
    lines = message.split('\n')

    try:
        if setting_type == "training_preferences":
            # 首次设定流程
            if not is_edit:
                if context.user_data.get("setting_step") == 3:
                    user_data["training_preferences"]["extra_practices"] = message if message != "无" else "无" if lang == "zh-cn" else "None"
                    context.user_data["setting_step"] = 4
                    await save_user_data(user_id, user_data)
                    
                    # 返回主菜单
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text="贱奴！你的调教偏好已经设置完成，现在可以开始骚骚了！"
                             if lang == "zh-cn" else
                             "Filthy dog! Your training preferences have been set. You can start your slutty behavior now!"
                    )
                    # 用户数据中标记设定阶段完成
                    if "instruction_states" not in user_data:
                        user_data["instruction_states"] = {}
                    user_data["instruction_states"]["setting_stage"] = "complete"
                    await save_user_data(user_id, user_data)
                    
                    # 返回主菜单并提示用户可以开始对话
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text="🔥 贱奴！跪在爷面前，准备接受调教吧！🔥\n直接打字说话，别浪费爷的时间！"
                             if lang == "zh-cn" else
                             "🔥 Filthy dog! Kneel before me, ready for training! 🔥\nType directly, don't waste my time!",
                        reply_markup=get_main_menu(user_id)
                    )

                    # 首次填写完所有调教设定后，自动触发AI的第一条回复
                    # 构建初始问候消息
                    initial_message = "你好主人，我已完成调教设定，请开始调教我吧" if lang == "zh-cn" else "Hello Master, I've completed my training settings, please start training me"
                    logger.info(f"用户 {user_id} 首次完成所有调教设定，自动触发AI回复")
                    
                    # 获取用户的语音设置
                    from ai_dialogue.user_manager import determine_voice_usage
                    use_voice, deduct_type = await determine_voice_usage(user_id, user_data, update.message._bot, update.effective_chat.id)
                    
                    # 将初始消息添加到AI队列
                    from globals import global_ai_queue
                    username = update.effective_user.username or update.effective_user.first_name
                    try:
                        await global_ai_queue.put((user_id, initial_message, update.effective_chat.id, use_voice, username))
                        logger.info(f"用户 {user_id} 自动触发的初始消息已加入AI处理队列")
                    except asyncio.QueueFull:
                        logger.warning(f"队列已满，用户 {user_id} 自动触发的初始消息被丢弃")
                    
                    return
                elif context.user_data.get("setting_step") == 2:
                    user_data["training_preferences"]["acceptable_practices"] = context.user_data.get("acceptable_practices", [])
                    context.user_data["setting_step"] = 3
                    await save_user_data(user_id, user_data)
                    
                    keyboard = [
                        [InlineKeyboardButton("跳过" if lang == "zh-cn" else "Skip", callback_data="no_extra_practices")],
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ]
                    
                    # 根据语言选择文本内容
                    if lang == "zh-cn":
                        text_content = """🔥 第二部分：完善你的调教偏好！🔥
3. 输入额外玩法或特殊要求（如果有）：
（输入"无"或点击下方"跳过"按钮可跳过）"""
                    else:
                        text_content = """🔥 Part 2: Complete Your Training Preferences! 🔥
3. Enter any additional practices or special requests (if any):
(Enter 'None' or click 'Skip' button below to skip)"""
                    
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text=text_content,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                    return
            else:  # 编辑模式
                # 编辑模式的处理...
                pass
                
        elif setting_type == "master_settings":
            # 主人设定处理
            if not message.strip():
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text="贱奴！主人信息不能为空，重新输入！"
                         if lang == "zh-cn" else
                         "Filthy dog! Master information cannot be empty, try again!"
                )
                return
            
            # 解析用户输入的主人设定格式
            master_settings = {}
            master_gender = user_data.get("master_gender", "male")
            field_labels = get_field_labels("master_settings", lang, master_gender)
            
            # 首先保留之前设置的年龄
            if "master_settings" in user_data and "age" in user_data["master_settings"]:
                master_settings["age"] = user_data["master_settings"]["age"]
            
            # 检查是否为标准模板格式输入
            has_template_format = any("：" in line for line in lines)
            
            if has_template_format:
                # 解析每一行的设定
                for line in lines:
                    if "：" in line:
                        label, value = line.split("：", 1)
                        label = label.strip()
                        value = value.strip()
                        
                        # 找到对应的字段key
                        for field_key, field_label in field_labels.items():
                            if field_label == label:
                                if value:  # 只保存非空值
                                    master_settings[field_key] = value
                                break
            else:
                # 用户只输入了名字，直接作为主人名字处理
                master_settings["name"] = message.strip()
            
            # 检查是否至少填写了姓名
            if not master_settings.get("name"):
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text="贱奴！主人姓名不能为空，重新输入！"
                         if lang == "zh-cn" else
                         "Filthy dog! Master name cannot be empty, try again!"
                )
                return
            
            # 验证主人年龄（如果有提供）
            if "age" in master_settings:
                try:
                    age = int(master_settings["age"])
                    if age < 18:
                        await send_with_retry(
                            bot=update.message._bot,
                            method=update.message.reply_text,
                            user_id=user_id,
                            text="贱奴！主人年龄必须大于等于18岁，重新输入！"
                                 if lang == "zh-cn" else
                                 "Filthy dog! Master's age must be 18 or older, try again!"
                        )
                        return
                except ValueError:
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text="贱奴！主人年龄必须是有效数字，重新输入！"
                             if lang == "zh-cn" else
                             "Filthy dog! Master's age must be a valid number, try again!"
                    )
                    return
            
            # 保存主人设定
            user_data["master_settings"] = master_settings
            await save_user_data(user_id, user_data)
            
            # 根据是否为编辑模式决定下一步
            if is_edit:
                # 编辑模式：扣除🦴并返回主菜单
                from prompt_configs import check_membership_status
                is_member = check_membership_status(user_data)
                        
                if not is_member:
                    # 扣除15🦴
                    if user_data.get("daily_free_dog_food", 0) >= 15:
                        user_data["daily_free_dog_food"] -= 15
                    else:
                        remaining = 15 - user_data.get("daily_free_dog_food", 0)
                        user_data["daily_free_dog_food"] = 0
                        user_data["dog_food"] = user_data.get("dog_food", 0) - remaining
                    
                    await save_user_data(user_id, user_data)
                    
                    cost_text = "（已扣除15🦴）" if lang == "zh-cn" else " (15🦴 deducted)"
                else:
                    cost_text = "（会员免费）" if lang == "zh-cn" else " (Free for members)"
                
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=(
                        f"🔥 贱狗！主人设定已更新！🔥{cost_text}\n"
                        "直接打字或发送语音与爷互动吧！"
                        if lang == "zh-cn" else
                        f"🔥 Filthy dog! Master settings updated! 🔥{cost_text}\n"
                        "Type or send voice to interact with me!"
                    ),
                    reply_markup=get_main_menu(user_id)
                )
                
                # 清理编辑状态
                context.user_data.pop("setting_type", None)
                context.user_data.pop("is_edit", None)
                
                logger.info(f"用户 {user_id} 完成主人设定编辑")
                return
            else:
                # 首次设定模式：进入奴种设定
                context.user_data["setting_type"] = "slave_type_preferences"
                context.user_data["current_slave_type_index"] = 0
                
                # 初始化奴种喜好数据结构
                if "slave_type_preferences" not in user_data["training_preferences"]:
                    user_data["training_preferences"]["slave_type_preferences"] = {}
                
                await start_slave_type_settings(update, context, user_data, 0, 0)
                logger.info(f"用户 {user_id} 完成主人设定，进入奴种喜好设定流程")
                return
                
        elif setting_type == "master_age":
            # 主人年龄设定处理
            try:
                age = int(message)
                if age < 18:
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text="贱奴！主人年龄必须大于等于18岁，重新输入！"
                             if lang == "zh-cn" else
                             "Filthy dog! Master's age must be 18 or older, try again!"
                    )
                    return
                
                # 保存主人年龄并进入主人其他信息设定
                if "master_settings" not in user_data:
                    user_data["master_settings"] = {}
                user_data["master_settings"]["age"] = message
                await save_user_data(user_id, user_data)
                
                # 切换到主人设定模式
                context.user_data["setting_type"] = "master_settings"
                context.user_data.pop("setting_step", None)
                
                # 根据性别获取对应的模板
                master_gender = user_data.get("master_gender", "male")
                
                if is_edit:
                    # 编辑模式：显示填充了用户数据的模板
                    description_text = (
                        "🔥 修改主人设定！🔥\n"
                        "请按以下格式填写主人信息（每项一行，空行表示跳过，姓名不能为空）。\n"
                        "可直接复制下一条消息的模板进行填写："
                        if lang == "zh-cn" else
                        "🔥 Edit Master Settings! 🔥\n"
                        "Please fill in the master's information in the format below (one per line, leave blank to skip, name cannot be empty).\n"
                        "You can copy the template from the next message:"
                    )
                    
                    # 使用填充了用户数据的模板，包含刚设定的年龄
                    current_settings = user_data.get("master_settings", {})
                    template_text = format_user_settings_template("master_settings", current_settings, lang, master_gender)
                else:
                    # 首次设定模式：显示空模板
                    description_text = (
                        "🔥 第一部分：定义你的主人！🔥\n"
                        "请按以下格式填写主人信息（每项一行，空行表示跳过，姓名不能为空）。\n"
                        "可直接复制下一条消息的模板进行填写："
                        if lang == "zh-cn" else
                        "🔥 Part 1: Define Your Master! 🔥\n"
                        "Please fill in the master's information in the format below (one per line, leave blank to skip, name cannot be empty).\n"
                        "You can copy the template from the next message:"
                    )
                    
                    # 生成空模板 - 不再需要手动添加年龄字段，因为模板中已经不包含这个字段
                    template_text = format_empty_template("master_settings", lang, master_gender)
                
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=description_text,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
                
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=template_text
                )
                
                mode_text = "编辑模式" if is_edit else "首次设定"
                logger.info(f"用户 {user_id} 设定主人年龄: {message}，进入主人信息{mode_text}填写阶段")
                return
                
            except ValueError:
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text="贱奴！请输入有效的年龄数字，重新输入！"
                         if lang == "zh-cn" else
                         "Filthy dog! Enter a valid age number, try again!"
                )
                return
                
        elif setting_type == "basic_info":
            # 基本信息设定步骤
            step = context.user_data.get("setting_step", 1)
            if step == 1:
                # 犬名设定完成，询问性别
                user_data["training_preferences"]["dog_name"] = message
                context.user_data["setting_step"] = 2
                
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=(
                        "🔥 第三部分：完善基本信息！🔥\n"
                        "2. 填写你的性别（如：男、女、其他）：\n"
                        "在对话框中输入："
                        if lang == "zh-cn" else
                        "🔥 Part 3: Complete Basic Information! 🔥\n"
                        "2. Enter your gender (e.g., male, female, other):\n"
                        "Enter in the chat:"
                    ),
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_2")],
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
                logger.info(f"用户 {user_id} 设定犬名: {message}")
            elif step == 2:
                # 性别设定完成，询问年龄
                user_data["training_preferences"]["gender"] = message
                context.user_data["setting_step"] = 3

                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=(
                        "🔥 第三部分：完善基本信息！🔥\n"
                        "3. 填写你的年龄：\n"
                        "在对话框中输入："
                        if lang == "zh-cn" else
                        "🔥 Part 3: Complete Basic Information! 🔥\n"
                        "3. Enter your age:\n"
                        "Enter in the chat:"
                    ),
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_3")],
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
                logger.info(f"用户 {user_id} 设定性别: {message}")
            elif step == 3:
                # 年龄设定完成，询问关系
                try:
                    age = int(message)
                    if age < 18:
                        await send_with_retry(
                            bot=update.message._bot,
                            method=update.message.reply_text,
                            user_id=user_id,
                            text="贱奴！年龄必须大于等于18岁，重新输入！"
                                 if lang == "zh-cn" else
                                 "Filthy dog! Age must be 18 or older, try again!"
                        )
                        return
                    user_data["training_preferences"]["age"] = message
                    context.user_data["setting_step"] = 4
                    
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text=(
                            "🔥 第三部分：完善基本信息！🔥\n"
                            "4. 填写你与主人的关系（如：同学、邻居等）：\n"
                            "在对话框中输入："
                            if lang == "zh-cn" else
                            "🔥 Part 3: Complete Basic Information! 🔥\n"
                            "4. Enter your relationship with master (e.g., classmate, neighbor, etc.):\n"
                            "Enter in the chat:"
                        ),
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_4")],
                            [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                        ])
                    )
                    logger.info(f"用户 {user_id} 设定年龄: {message}")
                except ValueError:
                    await send_with_retry(
                        bot=update.message._bot,
                        method=update.message.reply_text,
                        user_id=user_id,
                        text="贱奴！请输入有效的年龄数字，重新输入！"
                             if lang == "zh-cn" else
                             "Filthy dog! Enter a valid age number, try again!"
                    )
                    return
            elif step == 4:
                # 关系设定完成，跳转到场景设定
                user_data["training_preferences"]["relationship"] = message
                context.user_data["setting_type"] = "scene_settings"
                context.user_data["setting_step"] = 1

                # 使用空模板进行首次设定
                description_text = (
                    "🔥 第四部分：设定你的调教场景！🔥\n"
                    "你可以：\n"
                    "1. 按模板格式填写（复制下一条消息的模板）\n"
                    "2. 直接输入任何场景描述文字\n"
                    "不按模板也没关系，输入什么都会保存为场景设定！"
                    if lang == "zh-cn" else
                    "🔥 Part 4: Set Your Training Scene! 🔥\n"
                    "You can:\n"
                    "1. Fill in the template format (copy the template from the next message)\n"
                    "2. Simply enter any scene description text\n"
                    "It's okay not to follow the template, whatever you enter will be saved as scene settings!"
                )
                template_text = format_empty_template("scene_settings", lang)

                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=description_text,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=template_text
                )
                logger.info(f"用户 {user_id} 完成基本信息设定，进入场景设定流程")
                
        elif setting_type == "scene_settings":
            # 解析场景设定
            scene_settings = {}
            field_labels = get_field_labels("scene_settings", lang)

            # 检查是否为标准模板格式输入
            has_template_format = any("：" in line for line in lines)

            if has_template_format:
                # 解析每一行的设定
                for line in lines:
                    if "：" in line:
                        label, value = line.split("：", 1)
                        label = label.strip()
                        value = value.strip()

                        # 找到对应的字段key
                        for field_key, field_label in field_labels.items():
                            if field_label == label:
                                if value:  # 只保存非空值
                                    scene_settings[field_key] = value
                                break
            else:
                # 用户输入了自由文本，直接保存为场景描述
                user_text = message.strip()
                if user_text:
                    scene_settings = _parse_scene_free_text(user_text, lang)

            # 确保至少有一些场景信息
            if not any(scene_settings.get(key) for key in ["location", "environment", "people", "others"]):
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text="贱奴！场景信息不能为空，重新输入！"
                         if lang == "zh-cn" else
                         "Filthy dog! Scene information cannot be empty, try again!"
                )
                return

            # 保存场景设定
            user_data["scene_settings"] = scene_settings
            await save_user_data(user_id, user_data)
            
            # 根据是否为编辑模式决定下一步
            if is_edit:
                # 编辑模式：扣除🦴并返回主菜单
                from prompt_configs import check_membership_status
                is_member = check_membership_status(user_data)
                    
                if not is_member:
                    # 扣除15🦴
                    if user_data.get("daily_free_dog_food", 0) >= 15:
                        user_data["daily_free_dog_food"] -= 15
                    else:
                        remaining = 15 - user_data.get("daily_free_dog_food", 0)
                        user_data["daily_free_dog_food"] = 0
                        user_data["dog_food"] = user_data.get("dog_food", 0) - remaining
                    
                    await save_user_data(user_id, user_data)
                    
                    cost_text = "（已扣除15🦴）" if lang == "zh-cn" else " (15🦴 deducted)"
                else:
                    cost_text = "（会员免费）" if lang == "zh-cn" else " (Free for members)"
                    
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=(
                        f"🔥 贱狗！场景设定已更新！🔥{cost_text}\n"
                        "直接打字或发送语音与爷互动吧！"
                        if lang == "zh-cn" else
                        f"🔥 Filthy dog! Scene settings updated! 🔥{cost_text}\n"
                        "Type or send voice to interact with me!"
                    ),
                    reply_markup=get_main_menu(user_id)
                )
                
                # 清理编辑状态
                context.user_data.pop("setting_type", None)
                context.user_data.pop("is_edit", None)
                
                logger.info(f"用户 {user_id} 完成场景设定编辑")
                return
            else:
                # 修改：不是直接完成，而是引导用户选择调教风格
                from prompt_configs import check_membership_status, has_custom_mode
                
                # 获取当前选择的模式和会员状态
                current_mode = user_data.get("prompt_mode", "bdsm_dom_mild")
                is_member = check_membership_status(user_data)
                has_custom = has_custom_mode(user_data)
                
                # 构建当前模式显示文本
                if current_mode == "bdsm_dom_mild":
                    current_mode_text = "默认模式" if lang == "zh-cn" else "Default Mode"
                elif current_mode == "bdsm_dom_extreme":
                    current_mode_text = "羞辱模式" if lang == "zh-cn" else "Humiliation Mode"
                elif current_mode == "custom_mode":
                    current_mode_text = "自定义模式" if lang == "zh-cn" else "Custom Mode"
                else:
                    current_mode_text = "未知模式" if lang == "zh-cn" else "Unknown Mode"
                
                message = (
                    "🔥 恭喜你完成基本设定！最后一步：选择调教风格 🔥\n"
                    "───────────────────\n"
                    "选择你想要的AI调教风格：\n\n"
                    "🔸 **默认模式**：优化的调教风格，减少强烈羞辱性言语，更注重征服感和心理支配\n\n"
                    "🔸 **羞辱模式**：极端羞辱风格，包含强烈的语言攻击和心理摧毁\n\n"
                    "🔸 **自定义模式**：🔥仅限会员🔥\n"
                    "   • 只能自定义主人的语言风格（说话方式、称呼习惯等）\n"
                    "   • 不会改变已设定的调教规则和角色关系\n"
                    "   • 你的调教设定始终优先于自定义风格\n\n"
                    f"当前模式：{current_mode_text}\n"
                    "───────────────────\n"
                    "🔥 选择你的命运，别让爷等太久！"
                    if lang == "zh-cn" else
                    "🔥 Congratulations on completing the basic settings! Last step: Choose a training style 🔥\n"
                    "───────────────────\n"
                    "Choose your preferred AI training style:\n\n"
                    "🔸 **Default Mode**: Optimized training style with less extreme humiliation, focusing on conquest and psychological dominance\n\n"
                    "🔸 **Humiliation Mode**: Extreme humiliation style with intense verbal abuse and psychological destruction\n\n"
                    "🔸 **Custom Mode**: 🔥Members Only🔥\n"
                    "   • Only customize master's language style (speaking style, address habits, etc.)\n"
                    "   • Won't change established training rules and role relationships\n"
                    "   • Your training settings always take priority over custom style\n\n"
                    f"Current mode: {current_mode_text}\n"
                    "───────────────────\n"
                    "🔥 Choose your fate, don't keep me waiting!"
                )
                
                keyboard = []
                
                # 默认模式按钮
                default_check = "✅" if current_mode == "bdsm_dom_mild" else "⬜"
                keyboard.append([InlineKeyboardButton(
                    f"🔸 默认模式 {default_check}" if lang == "zh-cn" else f"🔸 Default Mode {default_check}",
                    callback_data=f"set_prompt_mode_bdsm_dom_mild_{user_id}"
                )])
                
                # 羞辱模式按钮
                extreme_check = "✅" if current_mode == "bdsm_dom_extreme" else "⬜"
                keyboard.append([InlineKeyboardButton(
                    f"🔸 羞辱模式 {extreme_check}" if lang == "zh-cn" else f"🔸 Humiliation Mode {extreme_check}",
                    callback_data=f"set_prompt_mode_bdsm_dom_extreme_{user_id}"
                )])
                
                # 自定义模式按钮
                if has_custom:
                    # 用户已设置自定义模式，显示选择按钮
                    custom_check = "✅" if current_mode == "custom_mode" else "⬜"
                    keyboard.append([InlineKeyboardButton(
                        f"🔸 自定义模式 {custom_check}" if lang == "zh-cn" else f"🔸 Custom Mode {custom_check}",
                        callback_data=f"set_prompt_mode_custom_mode_{user_id}"
                    )])
                elif is_member:
                    # 会员但未设置自定义模式
                    keyboard.append([InlineKeyboardButton(
                        "✨ 自定义模式" if lang == "zh-cn" else "✨ Custom Mode",
                        callback_data=f"create_custom_mode_{user_id}"
                    )])
                else:
                    # 非会员
                    keyboard.append([InlineKeyboardButton(
                        "🔒 自定义模式 (会员功能)" if lang == "zh-cn" else "🔒 Custom Mode (Members Only)",
                        callback_data=f"vip_info_{user_id}"
                    )])
                
                # 清除临时状态
                context.user_data.pop("setting_type", None)
                context.user_data.pop("setting_step", None)
                context.user_data.pop("current_slave_type_index", None)
                
                # 标记设定完成
                if "instruction_states" not in user_data:
                    user_data["instruction_states"] = {}
                user_data["instruction_states"]["setting_stage"] = "complete"
                await save_user_data(user_id, user_data)

                # 发送风格选择菜单
                await send_with_retry(
                    bot=update.message._bot,
                    method=update.message.reply_text,
                    user_id=user_id,
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode="Markdown"
                )
                logger.info(f"用户 {user_id} 完成场景设定，进入调教风格选择")
                
                # 如果这是首次完成所有设定，自动触发AI的第一条回复
                # 检查所有必要的设定是否已经完成
                training_complete = bool(user_data["training_preferences"].get("dog_name") and user_data["training_preferences"].get("age"))
                master_complete = bool(user_data["master_settings"].get("name"))
                scene_complete = bool(user_data["scene_settings"].get("location"))
                
                if training_complete and master_complete and scene_complete:
                    # 构建初始问候消息
                    initial_message = "你好主人，我已完成调教设定，请开始调教我吧" if lang == "zh-cn" else "Hello Master, I've completed my training settings, please start training me"
                    logger.info(f"用户 {user_id} 首次完成所有调教设定，自动触发AI回复")
                    
                    # 获取用户的语音设置
                    from ai_dialogue.user_manager import determine_voice_usage
                    use_voice, deduct_type = await determine_voice_usage(user_id, user_data, update.message._bot, update.effective_chat.id)
                    
                    # 将初始消息添加到AI队列
                    from globals import global_ai_queue
                    username = update.effective_user.username or update.effective_user.first_name
                    try:
                        await global_ai_queue.put((user_id, initial_message, update.effective_chat.id, use_voice, username))
                        logger.info(f"用户 {user_id} 自动触发的初始消息已加入AI处理队列")
                    except asyncio.QueueFull:
                        logger.warning(f"队列已满，用户 {user_id} 自动触发的初始消息被丢弃")
                
        else:
            # 如果 setting_type 不明确，则退出设定流程
            logger.info(f"用户 {user_id} 在设定过程中输入内容，但 setting_type 不明确: {setting_type}，退出设定流程")
            context.user_data.pop("setting_type", None)
            context.user_data.pop("is_edit", None)
            context.user_data.pop("setting_step", None)
            context.user_data.pop("setting_step_edit", None)
            context.user_data.pop("liked_practices", None)
            context.user_data.pop("acceptable_practices", None)
            context.user_data.pop("liked_practices_edit", None)
            context.user_data.pop("acceptable_practices_edit", None)
            await send_with_retry(
                bot=update.message._bot,
                method=update.message.reply_text,
                user_id=user_id,
                text=(
                    "🔥 贱奴！调教设定已取消，爷现在可以调教你了！🔥\n"
                    "直接打字或发送语音与爷互动吧！"
                    if lang == "zh-cn" else
                    "🔥 Filthy dog! Training setup canceled, I'm ready to train you now! 🔥\n"
                    "Type or send voice to interact with me!"
                ),
                reply_markup=get_main_menu(user_id)
            )
            
    except TelegramError as e:
        logger.error(f"发送设定完成提示失败，用户ID: {user_id}, 输入: {message}, 错误: {str(e)}")
        # 清理 context 状态并返回主菜单
        context.user_data.pop("setting_type", None)
        context.user_data.pop("is_edit", None)
        context.user_data.pop("setting_step", None)
        context.user_data.pop("setting_step_edit", None)
        from .base import send_message_and_clear_context
        await send_message_and_clear_context(
            chat_id=update.effective_chat.id,
            text=(
                "贱奴！处理设定输入时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Something went wrong processing settings input, try again later!"
            ),
            context=context,  # 传递 context 以便清除 remind_task
            reply_markup=get_main_menu(user_id)
        )

async def handle_training_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理调教设定菜单，确保所有设定可进入"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    instruction_states = user_data.get("instruction_states", {})
    setting_stage = instruction_states.get("setting_stage", "start")

    # 初始化 text 和 keyboard 变量，确保它们总是有值
    text = ""
    keyboard = []

    if lang == "zh-cn":
        if setting_stage == "start":
            text = (
                "🔥 贱奴！现在开始设定你的调教规则！🔥\n"
                "爷需要知道你的喜好、场景和主人设定，选一个开始吧！\n"
                "───────────────────\n"
                "1. 调教喜好：选择你喜欢的玩法\n"
                "2. 场景设定：设定调教环境\n"
                "3. 主人设定：定义主人的属性"
            )
            keyboard = [
                [InlineKeyboardButton("调教喜好", callback_data="training_preferences")],
                [InlineKeyboardButton("场景设定", callback_data="scene_settings")],
                [InlineKeyboardButton("主人设定", callback_data="master_settings")],
                [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]
            ]
        elif setting_stage == "training_preferences":
            text = "贱奴！继续设定你的调教喜好，别浪费爷的时间！"
            # 这里需要根据当前的步骤（例如选择喜欢、可接受玩法等）构建键盘
            # 为了简化，这里先给一个返回主菜单的选项，实际应根据具体步骤构建
            keyboard = [[InlineKeyboardButton("继续设定", callback_data="continue_training_settings")], # 假设有一个continue_training_settings处理函数
                        [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]]
            # TODO: 根据 setting_step 构建详细的键盘
        elif setting_stage == "scene_settings":
            text = "贱奴！设定调教场景，告诉爷你想在哪被调教！"
            keyboard = [[InlineKeyboardButton("继续设定", callback_data="edit_scene_settings")],
                        [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]]
        elif setting_stage == "master_settings":
            text = "贱奴！定义你的主人，爷要知道你想伺候谁！"
            keyboard = [[InlineKeyboardButton("继续设定", callback_data="edit_master_settings")],
                        [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]]
        elif setting_stage == "complete": # 如果设定已完成
             text = (
                "🔥 贱奴！你的调教设定已完成！🔥\n"
                "随时可以在这里修改你的设定。\n"
                "───────────────────\n"
                "1. 调教喜好：选择你喜欢的玩法\n"
                "2. 场景设定：设定调教环境\n"
                "3. 主人设定：定义主人的属性\n"
                "4. 调教风格：选择调教风格"
            )
             keyboard = [
                [InlineKeyboardButton("修改调教喜好", callback_data="edit_training_preferences")],
                [InlineKeyboardButton("修改场景设定", callback_data="edit_scene_settings")],
                [InlineKeyboardButton("修改主人设定", callback_data="edit_master_settings")],
                [InlineKeyboardButton("调教风格", callback_data=f"training_mode_{user_id}")],
                [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]
            ]
        else: # 未知设定阶段，回退到开始阶段菜单
            text = "贱奴！爷不知道你在哪个设定阶段，滚回开始菜单！"
            keyboard = [[InlineKeyboardButton("开始设定", callback_data="training_settings_menu")],
                        [InlineKeyboardButton("返回主菜单", callback_data="back_to_main")]]
    else:  # English language
        if setting_stage == "start":
            text = (
                "🔥 Filthy slave! Let's set your training rules! 🔥\n"
                "Master needs to know your preferences, scene, and master settings. Choose one to start!\n"
                "───────────────────\n"
                "1. Training Preferences: Select practices you like\n"
                "2. Scene Settings: Set training environment\n"
                "3. Master Settings: Define master's attributes"
            )
            keyboard = [
                [InlineKeyboardButton("Training Preferences", callback_data="training_preferences")],
                [InlineKeyboardButton("Scene Settings", callback_data="scene_settings")],
                [InlineKeyboardButton("Master Settings", callback_data="master_settings")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]
            ]
        elif setting_stage == "training_preferences":
            text = "Filthy slave! Continue setting your training preferences, don't waste Master's time!"
            keyboard = [[InlineKeyboardButton("Continue Settings", callback_data="continue_training_settings")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]]
        elif setting_stage == "scene_settings":
            text = "Filthy slave! Set your training scene, tell Master where you want to be trained!"
            keyboard = [[InlineKeyboardButton("Continue Settings", callback_data="edit_scene_settings")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]]
        elif setting_stage == "master_settings":
            text = "Filthy slave! Define your master, Master wants to know who you want to serve!"
            keyboard = [[InlineKeyboardButton("Continue Settings", callback_data="edit_master_settings")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]]
        elif setting_stage == "complete": # If settings are complete
             text = (
                "🔥 Filthy slave! Your training settings are complete! 🔥\n"
                "You can modify your settings anytime here.\n"
                "───────────────────\n"
                "1. Training Preferences: Select practices you like\n"
                "2. Scene Settings: Set training environment\n"
                "3. Master Settings: Define master's attributes\n"
                "4. Training Style: Choose training style"
            )
             keyboard = [
                [InlineKeyboardButton("Edit Training Preferences", callback_data="edit_training_preferences")],
                [InlineKeyboardButton("Edit Scene Settings", callback_data="edit_scene_settings")],
                [InlineKeyboardButton("Edit Master Settings", callback_data="edit_master_settings")],
                [InlineKeyboardButton("Training Style", callback_data=f"training_mode_{user_id}")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]
            ]
        else: # Unknown setting stage, fall back to start stage menu
            text = "Filthy slave! Master doesn't know which setting stage you're at, go back to start menu!"
            keyboard = [[InlineKeyboardButton("Start Settings", callback_data="training_settings_menu")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")]]

    try:
        await query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))
        logger.info(f"用户 {user_id} 进入调教设定菜单，当前阶段: {setting_stage}")
    except TelegramError as e:
        logger.error(f"发送调教设定菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        # 如果编辑消息失败，尝试发送新消息
        try:
            await query.message.reply_text(text, reply_markup=InlineKeyboardMarkup(keyboard))
            logger.info(f"发送调教设定菜单（新消息）成功，用户ID: {user_id}")
        except TelegramError as e2:
            logger.error(f"发送调教设定菜单（新消息）失败，用户ID: {user_id}, 错误: {str(e2)}")

async def handle_training_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理调教风格选择菜单"""
    from prompt_configs import check_membership_status, has_custom_mode, get_custom_mode_content
    
    query = update.callback_query
    user_id = query.from_user.id
    
    # 强制重新获取最新用户数据，确保数据同步
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查会员状态
    is_member = check_membership_status(user_data)
    has_custom = has_custom_mode(user_data)
    
    # 添加调试日志
    logger.info(f"用户 {user_id} 会员状态：{is_member}，已设置自定义模式：{has_custom}，自定义内容：{user_data.get('custom_training_mode', '无')}")
    
    # 获取当前选择的模式
    current_mode = user_data.get("prompt_mode", "bdsm_dom_mild")  # 默认为优化的默认模式
    
    # 添加调试日志
    logger.info(f"用户 {user_id} 当前调教风格：{current_mode}，用户数据中的prompt_mode：{user_data.get('prompt_mode', 'None')}")
    
    # 构建当前模式显示文本
    if current_mode == "bdsm_dom_mild":
        current_mode_text = "默认模式" if lang == "zh-cn" else "Default Mode"
    elif current_mode == "bdsm_dom_extreme":
        current_mode_text = "羞辱模式" if lang == "zh-cn" else "Humiliation Mode"
    elif current_mode == "custom_mode":
        current_mode_text = "自定义模式" if lang == "zh-cn" else "Custom Mode"
    else:
        current_mode_text = "未知模式" if lang == "zh-cn" else "Unknown Mode"
    
    message = (
        "🧠 **调教风格选择** 🧠\n"
        "───────────────────\n"
        "选择你想要的AI调教风格：\n\n"
        "🔸 **默认模式**：优化的调教风格，减少强烈羞辱性言语，更注重征服感和心理支配\n\n"
        "🔸 **羞辱模式**：极端羞辱风格，包含强烈的语言攻击和心理摧毁\n\n"
        "🔸 **自定义模式**：🔥仅限会员🔥\n"
        "   • 只能自定义主人的语言风格（说话方式、称呼习惯等）\n"
        "   • 不会改变已设定的调教规则和角色关系\n"
        "   • 你的调教设定始终优先于自定义风格\n\n"
        f"当前模式：{current_mode_text}\n"
        "───────────────────\n"
        "🔥 选择你的命运，别让爷等太久！"
        if lang == "zh-cn" else
        "🧠 **Training Style Selection** 🧠\n"
        "───────────────────\n"
        "Choose your preferred AI training style:\n\n"
        "🔸 **Default Mode**: Optimized training style with less extreme humiliation, focusing on conquest and psychological dominance\n\n"
        "🔸 **Humiliation Mode**: Extreme humiliation style with intense verbal abuse and psychological destruction\n\n"
        "🔸 **Custom Mode**: 🔥Members Only🔥\n"
        "   • Only customize master's language style (speaking style, address habits, etc.)\n"
        "   • Won't change established training rules and role relationships\n"
        "   • Your training settings always take priority over custom style\n\n"
        f"Current mode: {current_mode_text}\n"
        "───────────────────\n"
        "🔥 Choose your fate, don't keep me waiting!"
    )
    
    keyboard = []
    
    # 默认模式按钮
    default_check = "✅" if current_mode == "bdsm_dom_mild" else "⬜"
    keyboard.append([InlineKeyboardButton(
        f"🔸 默认模式 {default_check}" if lang == "zh-cn" else f"🔸 Default Mode {default_check}",
        callback_data=f"set_prompt_mode_bdsm_dom_mild_{user_id}"
    )])
    
    # 羞辱模式按钮
    extreme_check = "✅" if current_mode == "bdsm_dom_extreme" else "⬜"
    keyboard.append([InlineKeyboardButton(
        f"🔸 羞辱模式 {extreme_check}" if lang == "zh-cn" else f"🔸 Humiliation Mode {extreme_check}",
        callback_data=f"set_prompt_mode_bdsm_dom_extreme_{user_id}"
    )])
    
    # 自定义模式按钮
    if has_custom:
        # 用户已设置自定义模式，显示选择按钮
        custom_check = "✅" if current_mode == "custom_mode" else "⬜"
        keyboard.append([InlineKeyboardButton(
            f"🔸 自定义模式 {custom_check}" if lang == "zh-cn" else f"🔸 Custom Mode {custom_check}",
            callback_data=f"set_prompt_mode_custom_mode_{user_id}"
        )])
        # 添加管理自定义模式的按钮
        keyboard.append([InlineKeyboardButton(
            "✏️ 管理自定义模式" if lang == "zh-cn" else "✏️ Manage Custom Mode",
            callback_data=f"manage_custom_mode_{user_id}"
        )])
    else:
        # 用户尚未设置自定义模式，显示创建按钮
        keyboard.append([InlineKeyboardButton(
            "✨ 自定义模式" if lang == "zh-cn" else "✨ Custom Mode",
            callback_data=f"create_custom_mode_{user_id}"
        )])
    
    # 返回按钮
    keyboard.append([InlineKeyboardButton(
        "🏃 返回主菜单" if lang == "zh-cn" else "🏃 Back to Main Menu",
        callback_data="back_to_main"
    )])
    
    try:
        await query.edit_message_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 打开调教风格选择菜单")
    except TelegramError as e:
        logger.error(f"发送调教风格选择菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_set_prompt_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        # 获取选择的模式 - 修复：正确解析回调数据格式 set_prompt_mode_{mode}_{user_id}
        callback_parts = query.data.split("_")
        if len(callback_parts) >= 4:
            # 对于格式 set_prompt_mode_bdsm_dom_mild_{user_id} 或 set_prompt_mode_bdsm_dom_extreme_{user_id}
            # 或 set_prompt_mode_custom_mode_{user_id}
            # 取出 bdsm_dom_mild 或 bdsm_dom_extreme 或 custom_mode 部分
            mode = "_".join(callback_parts[3:-1])  # 去掉前3个部分和最后的user_id
        else:
            # 兼容性处理，如果格式不对则使用默认模式
            mode = "bdsm_dom_mild"
            
        user_data["prompt_mode"] = mode
        
        # 切换模式时清理对话历史，避免不同模式的上下文混合
        old_conversation_count = len(user_data.get("conversation", []))
        user_data["conversation"] = []
        
        # 保存用户数据
        await save_user_data(user_id, user_data)
        
        logger.info(f"用户 {user_id} 设置调教风格为: {mode}，已清理 {old_conversation_count} 条对话历史")
        
        mode_names = {
            "bdsm_dom_mild": "默认模式" if lang == "zh-cn" else "Default Mode",
            "bdsm_dom_extreme": "羞辱模式" if lang == "zh-cn" else "Humiliation Mode",
            "custom_mode": "自定义模式" if lang == "zh-cn" else "Custom Mode"
        }
        
        await query.edit_message_text(
            f"🔥 贱狗！调教风格已设置为：{mode_names.get(mode, mode)} 🔥\n"
            "直接打字或发送语音与爷互动吧！"
            if lang == "zh-cn" else
            f"🔥 Filthy dog! Training style set to: {mode_names.get(mode, mode)} 🔥\n"
            "Type or send voice to interact with me!",
            reply_markup=get_main_menu(user_id)
        )
        logger.info(f"用户 {user_id} 设置调教风格为: {mode}")
    except TelegramError as e:
        logger.error(f"设置调教风格失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("贱奴！设置调教风格时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong setting training style, try again later!")

async def handle_select_master_gender(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理主人性别选择"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        # 获取选择的性别
        gender = query.data.split("_")[-1]  # male 或 female
        user_data["master_gender"] = gender
        await save_user_data(user_id, user_data)
        
        # 设置为主人年龄输入状态
        context.user_data["setting_type"] = "master_age"
        context.user_data["setting_step"] = 1
        
        await query.edit_message_text(
            text=(
            "🔥 第一部分：定义你的主人！🔥\n"
                "请输入主人的年龄（必须大于等于18岁）：\n"
                "在对话框中输入："
            if lang == "zh-cn" else
            "🔥 Part 1: Define Your Master! 🔥\n"
                "Please enter master's age (must be 18 or older):\n"
                "Enter in the chat:"
            ),
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
            ])
        )
        
        logger.info(f"用户 {user_id} 选择主人性别为: {gender}，进入主人年龄输入步骤")
        
    except TelegramError as e:
        logger.error(f"处理主人性别选择失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("贱奴！选择主人性别时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong selecting master gender, try again later!")

async def handle_select_master_gender_edit(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理编辑模式的主人性别选择"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        # 获取选择的性别
        gender = query.data.split("_")[-2]  # male_edit 或 female_edit，取倒数第二个
        user_data["master_gender"] = gender
        await save_user_data(user_id, user_data)
        
        # 设置为主人年龄输入状态（编辑模式）
        context.user_data["setting_type"] = "master_age"
        context.user_data["setting_step"] = 1
        context.user_data["is_edit"] = True
        
        await query.edit_message_text(
            text=(
                "🔥 修改主人设定！🔥\n"
                "请输入主人的年龄（必须大于等于18岁）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Edit Master Settings! 🔥\n"
                "Please enter master's age (must be 18 or older):\n"
                "Enter in the chat:"
            ),
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
            ])
        )
        
        logger.info(f"用户 {user_id} 编辑模式选择主人性别为: {gender}，进入主人年龄输入")
        
    except TelegramError as e:
        logger.error(f"处理编辑模式主人性别选择失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("贱奴！选择主人性别时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong selecting master gender, try again later!")

async def handle_training_settings_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的调教设定 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 获取调教设定菜单的内容
    setting_data = user_data.get("setting_data", {})
    preferences = setting_data.get("preferences", [])
    acceptable = setting_data.get("acceptable", [])
    
    status_text = (
        "🔥 **贱奴！调教设定菜单** 🔥\n"
        "───────────────────\n"
        f"**{'偏好项目' if lang == 'zh-cn' else 'Preferred Practices'}**: {', '.join(preferences) if preferences else ('未设置' if lang == 'zh-cn' else 'Not set')}\n"
        f"**{'可接受项目' if lang == 'zh-cn' else 'Acceptable Practices'}**: {', '.join(acceptable) if acceptable else ('未设置' if lang == 'zh-cn' else 'Not set')}\n"
        "───────────────────\n"
        "🎯 选择要修改的设置："
    )
    
    keyboard = [
        [InlineKeyboardButton("📋 偏好设定" if lang == "zh-cn" else "📋 Training Preferences", callback_data="training_preferences")],
        [InlineKeyboardButton("🎭 场景设定" if lang == "zh-cn" else "🎭 Scene Settings", callback_data="scene_settings")],
        [InlineKeyboardButton("👑 主人设定" if lang == "zh-cn" else "👑 Master Settings", callback_data="master_settings")],
        [InlineKeyboardButton("🧠 调教风格" if lang == "zh-cn" else "🧠 Training Style", callback_data=f"training_mode_{user_id}")],
        [InlineKeyboardButton("🏃 返回主菜单" if lang == "zh-cn" else "🏃 Back to Main Menu", callback_data="back_to_main")]
    ]
    
    try:
        await update.message.reply_text(status_text, reply_markup=InlineKeyboardMarkup(keyboard), parse_mode="Markdown")
        logger.info(f"用户 {user_id} 收到调教设定快捷回复菜单")
    except TelegramError as e:
        logger.error(f"发送调教设定快捷回复菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_create_custom_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理创建自定义模式请求"""
    from prompt_configs import check_membership_status
    
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查会员权限
    is_member = check_membership_status(user_data)
    
    if not is_member:
        # 非会员，显示权限提示
        try:
            await query.answer(
                "贱奴！自定义模式仅限会员使用，去充值成为会员吧！" if lang == "zh-cn" else 
                "Filthy dog! Custom mode is for members only, go purchase membership!",
                show_alert=True
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    # 会员用户，进入自定义模式创建流程
    try:
        await query.edit_message_text(
            "✨ **自定义调教模式** ✨\n"
            "───────────────────\n"
            "🎯 **重要说明**：自定义模式仅用于定义主人的语言风格！\n\n"
            "📋 **优先级排序**：\n"
            "1️⃣ 已填写的调教设定（主人设定、调教喜好、场景设定）\n"
            "2️⃣ 你的自定义语言风格\n\n"
            "✨ **自定义范围**：\n"
            "• 主人的说话方式和语气\n"
            "• 称呼习惯（如：小东西、贱狗等）\n"
            "• 性格特点（如：冷漠、暴躁、温柔等）\n"
            "• ❌ 不能改变角色关系和调教规则\n\n"
            "📝 **示例**：冷漠高傲，喜欢叫'小废物'，语气简短直接\n\n"
            "💡 请发送你的自定义风格（10-300字）："
            if lang == "zh-cn" else
            "✨ **Custom Training Mode** ✨\n"
            "───────────────────\n"
            "🎯 **Important**: Custom mode only defines master's language style!\n\n"
            "📋 **Priority Order**:\n"
            "1️⃣ Existing training settings (master, preferences, scene)\n"
            "2️⃣ Your custom language style\n\n"
            "✨ **Customization Scope**:\n"
            "• Master's speaking style and tone\n"
            "• Address habits (e.g., little thing, filthy dog, etc.)\n"
            "• Personality traits (e.g., cold, violent, gentle, etc.)\n"
            "• ❌ Cannot change role relationships and training rules\n\n"
            "📝 **Example**: Cold and arrogant, likes to call 'little waste', brief and direct tone\n\n"
            "💡 Please send your custom style (10-300 characters):",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "取消" if lang == "zh-cn" else "Cancel",
                    callback_data=f"training_mode_{user_id}"
                )]
            ]),
            parse_mode="Markdown"
        )
        
        # 设置状态，等待用户输入
        context.user_data["awaiting_custom_mode"] = True
        logger.info(f"用户 {user_id} 进入自定义模式创建流程")
        
    except TelegramError as e:
        logger.error(f"发送自定义模式创建菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_manage_custom_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理管理自定义模式菜单"""
    from prompt_configs import get_custom_mode_content
    
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 获取当前自定义内容
    custom_content = get_custom_mode_content(user_data)
    
    try:
        await query.edit_message_text(
            "✏️ **管理自定义模式** ✏️\n"
            "───────────────────\n"
            f"📝 **当前自定义内容**：\n\n{custom_content}\n\n"
            "───────────────────\n"
            "选择你要进行的操作："
            if lang == "zh-cn" else
            "✏️ **Manage Custom Mode** ✏️\n"
            "───────────────────\n"
            f"📝 **Current Custom Content**:\n\n{custom_content}\n\n"
            "───────────────────\n"
            "Choose your action:",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "✏️ 修改内容" if lang == "zh-cn" else "✏️ Edit Content",
                    callback_data=f"edit_custom_mode_{user_id}"
                )],
                [InlineKeyboardButton(
                    "🗑️ 删除自定义模式" if lang == "zh-cn" else "🗑️ Delete Custom Mode",
                    callback_data=f"delete_custom_mode_{user_id}"
                )],
                [InlineKeyboardButton(
                    "🔙 返回调教风格选择" if lang == "zh-cn" else "🔙 Back to Mode Selection",
                    callback_data=f"training_mode_{user_id}"
                )]
            ]),
            parse_mode="Markdown"
        )
        
    except TelegramError as e:
        logger.error(f"发送管理自定义模式菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_edit_custom_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理编辑自定义模式请求"""
    from prompt_configs import get_custom_mode_content
    
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 获取当前自定义内容
    custom_content = get_custom_mode_content(user_data)
    
    try:
        await query.edit_message_text(
            "✏️ **修改自定义模式** ✏️\n"
            "───────────────────\n"
            f"📝 **当前内容**：\n{custom_content}\n\n"
            "🎯 **提醒**：自定义模式仅定义主人的语言风格，不会改变已设定的调教规则\n\n"
            "💡 请发送新的自定义风格（10-300字）："
            if lang == "zh-cn" else
            "✏️ **Edit Custom Mode** ✏️\n"
            "───────────────────\n"
            f"📝 **Current Content**:\n{custom_content}\n\n"
            "🎯 **Reminder**: Custom mode only defines master's language style, won't change established training rules\n\n"
            "💡 Please send new custom style (10-300 characters):",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "取消" if lang == "zh-cn" else "Cancel",
                    callback_data=f"manage_custom_mode_{user_id}"
                )]
            ]),
            parse_mode="Markdown"
        )
        
        # 设置状态，等待用户输入
        context.user_data["awaiting_custom_mode"] = True
        context.user_data["editing_custom_mode"] = True
        logger.info(f"用户 {user_id} 进入自定义模式编辑流程")
        
    except TelegramError as e:
        logger.error(f"发送编辑自定义模式菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_delete_custom_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理删除自定义模式请求"""
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        await query.edit_message_text(
            "🗑️ **删除自定义模式** 🗑️\n"
            "───────────────────\n"
            "⚠️ 确认要删除自定义模式吗？\n\n"
            "此操作无法撤销，删除后需要重新创建。"
            if lang == "zh-cn" else
            "🗑️ **Delete Custom Mode** 🗑️\n"
            "───────────────────\n"
            "⚠️ Are you sure you want to delete the custom mode?\n\n"
            "This action cannot be undone, you'll need to recreate it after deletion.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "🗑️ 确认删除" if lang == "zh-cn" else "🗑️ Confirm Delete",
                    callback_data=f"confirm_delete_custom_mode_{user_id}"
                )],
                [InlineKeyboardButton(
                    "🔙 取消" if lang == "zh-cn" else "🔙 Cancel",
                    callback_data=f"manage_custom_mode_{user_id}"
                )]
            ]),
            parse_mode="Markdown"
        )
        
    except TelegramError as e:
        logger.error(f"发送删除确认菜单失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_confirm_delete_custom_mode(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理确认删除自定义模式"""
    query = update.callback_query
    user_id = int(query.data.split("_")[4])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        # 删除自定义模式内容
        user_data.pop("custom_training_mode", None)
        
        # 如果当前使用的是自定义模式，切换到默认模式
        if user_data.get("prompt_mode") == "custom_mode":
            user_data["prompt_mode"] = "bdsm_dom_mild"
        
        await save_user_data(user_id, user_data)
        
        await query.edit_message_text(
            "🗑️ **自定义模式已删除** 🗑️\n"
            "───────────────────\n"
            "✅ 自定义模式已成功删除！\n"
            "已自动切换到默认模式。"
            if lang == "zh-cn" else
            "🗑️ **Custom Mode Deleted** 🗑️\n"
            "───────────────────\n"
            "✅ Custom mode has been successfully deleted!\n"
            "Automatically switched to default mode.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton(
                    "🔙 返回调教风格选择" if lang == "zh-cn" else "🔙 Back to Mode Selection",
                    callback_data=f"training_mode_{user_id}"
                )]
            ]),
            parse_mode="Markdown"
        )
        
        logger.info(f"用户 {user_id} 删除自定义模式成功")
        
    except TelegramError as e:
        logger.error(f"确认删除自定义模式失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_custom_mode_input(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理自定义模式内容输入"""
    from prompt_configs import set_custom_mode_content
    
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查是否在等待自定义模式输入
    if not context.user_data.get("awaiting_custom_mode"):
        return
    
    content = update.message.text.strip()
    
    # 验证内容长度
    if len(content) > 300:
        try:
            await update.message.reply_text(
                f"❌ 内容过长！当前{len(content)}字，请控制在300字以内。"
                if lang == "zh-cn" else
                f"❌ Content too long! Current {len(content)} characters, please keep within 300 characters.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(
                        "取消" if lang == "zh-cn" else "Cancel",
                        callback_data=f"training_mode_{user_id}"
                    )]
                ])
            )
        except TelegramError as e:
            logger.error(f"发送内容过长提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    if len(content) < 10:
        try:
            await update.message.reply_text(
                "❌ 内容过短！请至少输入10个字符来描述主人的语言风格。"
                if lang == "zh-cn" else
                "❌ Content too short! Please enter at least 10 characters to describe the master's language style.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(
                        "取消" if lang == "zh-cn" else "Cancel",
                        callback_data=f"training_mode_{user_id}"
                    )]
                ])
            )
        except TelegramError as e:
            logger.error(f"发送内容过短提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    # 保存自定义内容
    success = set_custom_mode_content(user_data, content)
    
    if success:
        # 自动切换到自定义模式
        user_data["prompt_mode"] = "custom_mode"
        
        # 清理对话历史（模式切换时截断）
        user_data["conversation"] = []
        logger.info(f"用户 {user_id} 切换到自定义模式，已清理对话历史")
        
        await save_user_data(user_id, user_data)
        
        # 添加调试日志，确认保存状态
        logger.info(f"用户 {user_id} 自定义模式保存完成，内容长度：{len(content)}，用户数据中的custom_training_mode：{user_data.get('custom_training_mode', '无')}，当前模式：{user_data.get('prompt_mode', '无')}")
        
        # 清除等待状态
        context.user_data.pop("awaiting_custom_mode", None)
        is_editing = context.user_data.pop("editing_custom_mode", None)
        
        try:
            await update.message.reply_text(
                "✅ **自定义模式设置成功** ✅\n"
                "───────────────────\n"
                f"📝 **内容**：{content}\n\n"
                "🎯 **重要提醒**：\n"
                "• 自定义模式仅影响主人的语言风格\n"
                "• 你的调教设定（主人属性、调教偏好等）始终优先\n"
                "• 不会改变角色关系和调教规则\n\n"
                "🎉 自定义调教风格已启用！"
                if lang == "zh-cn" else
                "✅ **Custom Mode Set Successfully** ✅\n"
                "───────────────────\n"
                f"📝 **Content**: {content}\n\n"
                "🎯 **Important Reminder**:\n"
                "• Custom mode only affects master's language style\n"
                "• Your training settings (master attributes, preferences, etc.) always take priority\n"
                "• Won't change role relationships and training rules\n\n"
                "🎉 Custom training style is now active!",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(
                        "🔙 返回调教风格选择" if lang == "zh-cn" else "🔙 Back to Mode Selection",
                        callback_data=f"training_mode_{user_id}"
                    )]
                ]),
                parse_mode="Markdown"
            )
            
            action = "编辑" if is_editing else "创建"
            logger.info(f"用户 {user_id} {action}自定义模式成功：{content[:50]}...")
            
        except TelegramError as e:
            logger.error(f"发送设置成功提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        try:
            await update.message.reply_text(
                "❌ 设置失败，请稍后再试！"
                if lang == "zh-cn" else
                "❌ Setting failed, please try again later!",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton(
                        "取消" if lang == "zh-cn" else "Cancel",
                        callback_data=f"training_mode_{user_id}"
                    )]
                ])
            )
        except TelegramError as e:
            logger.error(f"发送设置失败提示失败，用户ID: {user_id}, 错误: {str(e)}")

async def handle_training_mode_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的调教风格 - 发送新消息版本"""
    from prompt_configs import check_membership_status, has_custom_mode, get_custom_mode_content
    
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查会员状态
    is_member = check_membership_status(user_data)
    has_custom = has_custom_mode(user_data)
    
    # 获取当前选择的模式
    current_mode = user_data.get("prompt_mode", "bdsm_dom_mild")
    
    # 构建当前模式显示文本
    if current_mode == "bdsm_dom_mild":
        current_mode_text = "默认模式" if lang == "zh-cn" else "Default Mode"
    elif current_mode == "bdsm_dom_extreme":
        current_mode_text = "羞辱模式" if lang == "zh-cn" else "Humiliation Mode"
    elif current_mode == "custom_mode":
        current_mode_text = "自定义模式" if lang == "zh-cn" else "Custom Mode"
    else:
        current_mode_text = "未知模式" if lang == "zh-cn" else "Unknown Mode"
    
    message = (
        "🧠 **调教风格选择** 🧠\n"
        "───────────────────\n"
        "选择你想要的AI调教风格：\n\n"
        "🔸 **默认模式**：优化的调教风格，减少强烈羞辱性言语，更注重征服感和心理支配\n\n"
        "🔸 **羞辱模式**：极端羞辱风格，包含强烈的语言攻击和心理摧毁\n\n"
        "🔸 **自定义模式**：🔥仅限会员🔥\n"
        "   • 只能自定义主人的语言风格（说话方式、称呼习惯等）\n"
        "   • 不会改变已设定的调教规则和角色关系\n"
        "   • 你的调教设定始终优先于自定义风格\n\n"
        f"当前模式：{current_mode_text}\n"
        "───────────────────\n"
        "🔥 选择你的命运，别让爷等太久！"
        if lang == "zh-cn" else
        "🧠 **Training Style Selection** 🧠\n"
        "───────────────────\n"
        "Choose your preferred AI training style:\n\n"
        "🔸 **Default Mode**: Optimized training style with less extreme humiliation, focusing on conquest and psychological dominance\n\n"
        "🔸 **Humiliation Mode**: Extreme humiliation style with intense verbal abuse and psychological destruction\n\n"
        "🔸 **Custom Mode**: 🔥Members Only🔥\n"
        "   • Only customize master's language style (speaking style, address habits, etc.)\n"
        "   • Won't change established training rules and role relationships\n"
        "   • Your training settings always take priority over custom style\n\n"
        f"Current mode: {current_mode_text}\n"
        "───────────────────\n"
        "🔥 Choose your fate, don't keep me waiting!"
    )
    
    keyboard = []
    
    # 默认模式按钮
    default_check = "✅" if current_mode == "bdsm_dom_mild" else "⬜"
    keyboard.append([InlineKeyboardButton(
        f"🔸 默认模式 {default_check}" if lang == "zh-cn" else f"🔸 Default Mode {default_check}",
        callback_data=f"set_prompt_mode_bdsm_dom_mild_{user_id}"
    )])
    
    # 羞辱模式按钮
    extreme_check = "✅" if current_mode == "bdsm_dom_extreme" else "⬜"
    keyboard.append([InlineKeyboardButton(
        f"🔸 羞辱模式 {extreme_check}" if lang == "zh-cn" else f"🔸 Humiliation Mode {extreme_check}",
        callback_data=f"set_prompt_mode_bdsm_dom_extreme_{user_id}"
    )])
    
    # 自定义模式按钮
    if has_custom:
        # 用户已设置自定义模式，显示选择按钮
        custom_check = "✅" if current_mode == "custom_mode" else "⬜"
        keyboard.append([InlineKeyboardButton(
            f"🔸 自定义模式 {custom_check}" if lang == "zh-cn" else f"🔸 Custom Mode {custom_check}",
            callback_data=f"set_prompt_mode_custom_mode_{user_id}"
        )])
        # 添加管理自定义模式的按钮
        keyboard.append([InlineKeyboardButton(
            "✏️ 管理自定义模式" if lang == "zh-cn" else "✏️ Manage Custom Mode",
            callback_data=f"manage_custom_mode_{user_id}"
        )])
    else:
        # 用户尚未设置自定义模式，显示创建按钮
        keyboard.append([InlineKeyboardButton(
            "✨ 自定义模式" if lang == "zh-cn" else "✨ Custom Mode",
            callback_data=f"create_custom_mode_{user_id}"
        )])
    
    # 返回按钮
    keyboard.append([InlineKeyboardButton(
        "🏃 返回主菜单" if lang == "zh-cn" else "🏃 Back to Main Menu",
        callback_data="back_to_main"
    )])
    
    try:
        await update.message.reply_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 通过快捷回复打开调教风格选择菜单")
    except TelegramError as e:
        logger.error(f"发送调教风格选择菜单失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！爷弄调教风格菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the training style menu, try again later!",
            reply_markup=get_main_menu(user_id)
        )

async def start_slave_type_settings(update: Update, context: ContextTypes.DEFAULT_TYPE, user_data: dict, slave_type_index: int, practice_index: int = 0):
    """开始奴种设定流程"""
    query = update.callback_query
    # 修复：处理文本输入调用的情况
    if query:
        user_id = query.from_user.id
    else:
        user_id = update.effective_user.id
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查是否为编辑模式
    is_edit = context.user_data.get("is_edit", False)
    
    # 检查奴种索引有效性
    if slave_type_index >= len(SLAVE_TYPE_ORDER):
        logger.error(f"无效的奴种索引: {slave_type_index}")
        return
        
    # 初始化奴种喜好数据结构
    if "slave_type_preferences" not in user_data["training_preferences"]:
        user_data["training_preferences"]["slave_type_preferences"] = {}
        
    # 获取奴种信息
    slave_type_id = SLAVE_TYPE_ORDER[slave_type_index]
    slave_type_info = SLAVE_TYPES[slave_type_id]
    total_practices = len(slave_type_info["practices"])
    
    # 检查玩法索引有效性
    if practice_index >= total_practices:
        logger.error(f"无效的玩法索引: {practice_index}")
        return
        
    # 初始化当前奴种的喜好数据
    if slave_type_id not in user_data["training_preferences"]["slave_type_preferences"]:
        user_data["training_preferences"]["slave_type_preferences"][slave_type_id] = {}
    
    current_preferences = user_data["training_preferences"]["slave_type_preferences"][slave_type_id]
    current_practice = slave_type_info["practices"][practice_index]
    practice_name = current_practice["name"]
    practice_desc = current_practice["description"]
    current_choice = current_preferences.get(practice_name, "")
    
    # 显示当前选择状态
    status_text = ""
    if current_choice == "super_love":
        status_text = " 💖"
    elif current_choice == "acceptable":
        status_text = " ✅"
    elif current_choice == "refuse":
        status_text = " ❌"
    
    # 构建设定界面（根据是否为编辑模式调整文本）
    if is_edit:
        # 编辑模式的标题
        title = (
            f"🔥 修改奴种喜好设定 ({slave_type_index + 1}/9) 🔥"
            if lang == "zh-cn" else
            f"🔥 Edit Slave Type Preferences ({slave_type_index + 1}/9) 🔥"
        )
    else:
        # 首次设定模式的标题
        title = (
            f"🔥 第二部分：奴种喜好设定 ({slave_type_index + 1}/9) 🔥"
            if lang == "zh-cn" else
            f"🔥 Part 2: Slave Type Preferences ({slave_type_index + 1}/9) 🔥"
        )
    
    text = (
        f"{title}\n\n"
        f"📋 {slave_type_info['name']}\n"
        f"{slave_type_info['description']}\n\n"
        f"玩法进度：{practice_index + 1}/{total_practices}\n\n"
        f"🎯 {practice_index + 1}. {practice_name}{status_text}\n\n"
        f"📝 {practice_desc}\n\n"
        f"请选择你对这个玩法的喜爱程度："
        if lang == "zh-cn" else
        f"{title}\n\n"
        f"📋 {slave_type_info['name']}\n"
        f"{slave_type_info['description']}\n\n"
        f"Practice Progress: {practice_index + 1}/{total_practices}\n\n"
        f"🎯 {practice_index + 1}. {practice_name}{status_text}\n\n"
        f"📝 {practice_desc}\n\n"
        f"Please select your preference level for this practice:"
    )
    
    # 构建单选框按钮
    keyboard = []
    
    # 构建单选框样式的按钮（一行三个）
    super_love_text = "🔘 超级喜欢" if current_choice == "super_love" else "⚪ 超级喜欢"
    acceptable_text = "🔘 可以被要求" if current_choice == "acceptable" else "⚪ 可以被要求"
    refuse_text = "🔘 明确拒绝" if current_choice == "refuse" else "⚪ 明确拒绝"
    
    if lang != "zh-cn":
        super_love_text = "🔘 Super Love" if current_choice == "super_love" else "⚪ Super Love"
        acceptable_text = "🔘 Acceptable" if current_choice == "acceptable" else "⚪ Acceptable"
        refuse_text = "🔘 Refuse" if current_choice == "refuse" else "⚪ Refuse"
    
    # 使用新的callback格式，避免解析错误
    choice_row = [
        InlineKeyboardButton(super_love_text, callback_data=f"slave_choice_{slave_type_index}_{practice_index}_super_love"),
        InlineKeyboardButton(acceptable_text, callback_data=f"slave_choice_{slave_type_index}_{practice_index}_acceptable"),
        InlineKeyboardButton(refuse_text, callback_data=f"slave_choice_{slave_type_index}_{practice_index}_refuse")
    ]
    keyboard.append(choice_row)
    
    # 导航按钮逻辑
    nav_row = []
    
    # 上一页按钮
    if practice_index > 0:
        nav_row.append(InlineKeyboardButton("⬅️ 上一个玩法" if lang == "zh-cn" else "⬅️ Previous", 
                                          callback_data=f"slave_practice_{slave_type_index}_{practice_index - 1}"))
    elif slave_type_index > 0:
        # 如果是第一个玩法但不是第一个奴种，可以返回上一个奴种的最后一个玩法
        prev_slave_type_id = SLAVE_TYPE_ORDER[slave_type_index - 1]
        prev_total_practices = len(SLAVE_TYPES[prev_slave_type_id]["practices"])
        nav_row.append(InlineKeyboardButton("⬅️ 上个奴种" if lang == "zh-cn" else "⬅️ Prev Type", 
                                          callback_data=f"slave_practice_{slave_type_index - 1}_{prev_total_practices - 1}"))
    
    # 下一页或下个奴种按钮
    if practice_index < total_practices - 1:
        # 不是最后一个玩法，显示下一个玩法
        nav_row.append(InlineKeyboardButton("下一个玩法 ➡️" if lang == "zh-cn" else "Next Practice ➡️", 
                                          callback_data=f"slave_practice_{slave_type_index}_{practice_index + 1}"))
    else:
        # 是最后一个玩法
        if slave_type_index < len(SLAVE_TYPE_ORDER) - 1:
            # 不是最后一个奴种，显示下个奴种
            nav_row.append(InlineKeyboardButton("下个奴种 ➡️" if lang == "zh-cn" else "Next Type ➡️", 
                                              callback_data=f"slave_practice_{slave_type_index + 1}_0"))
        else:
            # 是最后一个奴种的最后一个玩法，显示完成
            nav_row.append(InlineKeyboardButton("完成奴种设定 ✅" if lang == "zh-cn" else "Complete ✅", 
                                              callback_data="finish_all_slave_types"))
    
    if nav_row:
        keyboard.append(nav_row)
    
    # 添加返回主菜单按钮
    keyboard.append([InlineKeyboardButton("🏠 返回主菜单" if lang == "zh-cn" else "🏠 Main Menu", callback_data="back_to_main")])
    
    await save_user_data(user_id, user_data)
    
    try:
        if hasattr(update, 'callback_query') and update.callback_query:
            await update.callback_query.edit_message_text(text, reply_markup=InlineKeyboardMarkup(keyboard))
        else:
            await send_with_retry(
                bot=update.message._bot,
                method=update.message.reply_text,
                user_id=user_id,
                text=text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        logger.info(f"用户 {user_id} 进入{slave_type_info['name']}设定，玩法: {practice_index + 1}/{total_practices}")
    except Exception as e:
        logger.error(f"发送奴种设定界面失败，用户ID: {user_id}, 错误: {str(e)}")
        await send_with_retry(
            bot=update.effective_message.bot if hasattr(update, 'effective_message') else update.message._bot,
            method=update.effective_chat.send_message if hasattr(update, 'effective_chat') else update.message.reply_text,
            user_id=user_id,
            text=text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

async def handle_slave_preference_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理奴种玩法喜好选择"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    callback_data = query.data

    try:
        # 解析callback数据：slave_choice_{slave_type_index}_{practice_index}_{preference}
        # 使用更精确的分割方法
        if not callback_data.startswith("slave_choice_"):
            logger.error(f"无效的callback前缀: {callback_data}")
            return
            
        # 移除前缀，得到参数部分
        params = callback_data[13:]  # 移除"slave_choice_"
        
        # 按下划线分割，但只分割前两个下划线，保持preference完整
        parts = params.split("_", 2)
        if len(parts) != 3:
            logger.error(f"无效的callback格式: {callback_data}, 参数部分: {params}")
            return
            
        slave_type_index = int(parts[0])
        practice_index = int(parts[1])
        preference = parts[2]
        
        logger.info(f"DEBUG - 解析成功: slave_type_index={slave_type_index}, practice_index={practice_index}, preference={preference}")
        
        # 验证preference值
        if preference not in ["super_love", "acceptable", "refuse"]:
            logger.error(f"无效的preference值: {preference}")
            return
        
        # 获取奴种信息
        if slave_type_index >= len(SLAVE_TYPE_ORDER):
            logger.error(f"奴种索引越界: {slave_type_index}")
            return
            
        slave_type_id = SLAVE_TYPE_ORDER[slave_type_index]
        slave_type_info = SLAVE_TYPES[slave_type_id]
        
        if practice_index >= len(slave_type_info["practices"]):
            logger.error(f"玩法索引越界: {practice_index}")
            return
            
        practice_name = slave_type_info["practices"][practice_index]["name"]
        
        # 初始化数据结构
        if "slave_type_preferences" not in user_data["training_preferences"]:
            user_data["training_preferences"]["slave_type_preferences"] = {}
        if slave_type_id not in user_data["training_preferences"]["slave_type_preferences"]:
            user_data["training_preferences"]["slave_type_preferences"][slave_type_id] = {}
        
        # 保存选择
        user_data["training_preferences"]["slave_type_preferences"][slave_type_id][practice_name] = preference
        await save_user_data(user_id, user_data)
        
        # 刷新界面显示最新状态
        await start_slave_type_settings(update, context, user_data, slave_type_index, practice_index)
        
        # 给用户反馈
        feedback_text = ""
        if preference == "super_love":
            feedback_text = f"💖 你选择了超级喜欢 {practice_name}"
        elif preference == "acceptable":
            feedback_text = f"✅ 你选择了可以被要求 {practice_name}"
        elif preference == "refuse":
            feedback_text = f"❌ 你选择了明确拒绝 {practice_name}"
        
        try:
            await query.answer(feedback_text, show_alert=False)
        except:
            pass  # 忽略反馈错误
            
        logger.info(f"用户 {user_id} 选择奴种 {slave_type_id} 的玩法 {practice_name}: {preference}")
        
    except Exception as e:
        logger.error(f"处理奴种喜好选择失败，用户ID: {user_id}, callback: {callback_data}, 错误: {str(e)}")
        try:
            await query.answer("选择失败，请重试" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Selection failed, please retry")
        except:
            pass

async def handle_slave_type_navigation(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理奴种设定页面的导航"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    callback_data = query.data

    try:
        if callback_data == "finish_all_slave_types":
            # 检查是否为编辑模式
            is_edit = context.user_data.get("is_edit", False)
            lang = user_data.get("preferred_language", "zh-cn")
            
            if is_edit:
                # 编辑模式下，完成后直接返回主菜单
                # 扣费处理（非会员用户）
                from prompt_configs import check_membership_status
                is_member = check_membership_status(user_data)
                
                if not is_member:
                    # 非会员扣除15🦴
                    dog_food = user_data.get("dog_food", 0)
                    daily_free_dog_food = user_data.get("daily_free_dog_food", 0)
                    
                    # 优先扣除每日免费🦴
                    if daily_free_dog_food >= 15:
                        user_data["daily_free_dog_food"] = daily_free_dog_food - 15
                    else:
                        # 免费不够，扣除部分免费，剩余从充值中扣
                        remaining = 15 - daily_free_dog_food
                        user_data["daily_free_dog_food"] = 0
                        user_data["dog_food"] = max(0, dog_food - remaining)
                    
                    await save_user_data(user_id, user_data)
                    logger.info(f"用户 {user_id} 编辑调教喜好完成，扣除15🦴")
                
                # 清理上下文中的编辑状态
                context.user_data.pop("is_edit", None)
                context.user_data.pop("setting_type", None)
                context.user_data.pop("current_slave_type_index", None)
                
                # 显示修改完成信息
                completion_text = (
                    "🔥 调教喜好修改完成！🔥\n\n"
                    f"你已成功修改奴种喜好设定。{'' if is_member else '已扣除15🦴。'}"
                    if lang == "zh-cn" else
                    "🔥 Training Preferences Updated! 🔥\n\n"
                    f"You have successfully updated your slave type preferences.{'' if is_member else ' 15🦴 has been deducted.'}"
                )
                
                # 返回主菜单
                await query.edit_message_text(
                    text=completion_text,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
                    ])
                )
                logger.info(f"用户 {user_id} 完成编辑调教喜好，返回主菜单")
            else:
                # 首次设定流程，进入基本信息设定
                context.user_data["setting_type"] = "basic_info"
                context.user_data["setting_step"] = 1  # 开始基本信息第一步：犬名
                context.user_data.pop("current_slave_type_index", None)
                
                description_text = (
                    "🔥 第三部分：完善基本信息！🔥\n"
                    "请按以下格式填写基本信息（每项一行，犬名不能为空）。\n"
                    "可直接复制下一条消息的模板进行填写："
                    if lang == "zh-cn" else
                    "🔥 Part 3: Complete Basic Information! 🔥\n"
                    "Please fill in basic information in the format below (one per line, dog name cannot be empty).\n"
                    "You can copy the template from the next message:"
                )
                template_text = format_empty_template("basic_info", lang)

                await query.edit_message_text(
                    text=description_text,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                    ])
                )
                # 发送模板消息
                await query.message.reply_text(template_text)
                logger.info(f"用户 {user_id} 完成奴种设定，进入基本信息设定流程")
            
    except Exception as e:
        logger.error(f"处理奴种导航失败，用户ID: {user_id}, callback: {callback_data}, 错误: {str(e)}")
        try:
            await query.answer("处理失败，请重试" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Failed, please retry")
        except:
            pass

async def handle_slave_practice_navigation(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理奴种设定中的玩法导航"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    callback_data = query.data

    try:
        # 解析callback数据：slave_practice_{slave_type_index}_{practice_index}
        parts = callback_data.split("_")
        if len(parts) != 4:
            logger.error(f"无效的callback数据格式: {callback_data}")
            return
            
        slave_type_index = int(parts[2])
        practice_index = int(parts[3])
        
        await start_slave_type_settings(update, context, user_data, slave_type_index, practice_index)
        logger.info(f"用户 {user_id} 切换到奴种 {slave_type_index} 的玩法 {practice_index + 1}")
        
    except Exception as e:
        logger.error(f"处理玩法导航失败，用户ID: {user_id}, callback: {callback_data}, 错误: {str(e)}")
        await query.answer("处理失败，请重试" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Failed, please retry")

async def handle_reset_basic_info(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理基本信息重置"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        if query.data == "reset_basic_info_1":
            context.user_data["setting_step"] = 1
            await query.edit_message_text(
                text=(
                    "🔥 第二部分：完善基本信息！🔥\n"
                    "1. 已重置！给自己起一个犬名（不能为空）：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Complete Basic Information! 🔥\n"
                    "1. Reset! Choose a dog name for yourself (cannot be empty):\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_1")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif query.data == "reset_basic_info_2":
            context.user_data["setting_step"] = 2
            await query.edit_message_text(
                text=(
                    "🔥 第二部分：完善基本信息！🔥\n"
                    "2. 已重置！填写你的性别（如：男、女、其他）：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Complete Basic Information! 🔥\n"
                    "2. Reset! Enter your gender (e.g., male, female, other):\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_2")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif query.data == "reset_basic_info_3":
            context.user_data["setting_step"] = 3
            await query.edit_message_text(
                text=(
                    "🔥 第二部分：完善基本信息！🔥\n"
                    "3. 已重置！填写你的年龄：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Complete Basic Information! 🔥\n"
                    "3. Reset! Enter your age:\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_3")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif query.data == "reset_basic_info_4":
            context.user_data["setting_step"] = 4
            await query.edit_message_text(
                text=(
                    "🔥 第二部分：完善基本信息！🔥\n"
                    "4. 已重置！填写你与主人的关系（如：同学、邻居等）：\n"
                    "在对话框中输入："
                    if lang == "zh-cn" else
                    "🔥 Part 2: Complete Basic Information! 🔥\n"
                    "4. Reset! Enter your relationship with master (e.g., classmate, neighbor, etc.):\n"
                    "Enter in the chat:"
                ),
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_basic_info_4")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        
        logger.info(f"用户 {user_id} 重置基本信息步骤: {query.data}")
        
    except TelegramError as e:
        logger.error(f"重置基本信息失败，用户ID: {user_id}, callback: {query.data}, 错误: {str(e)}")
        await query.answer("重置失败，请稍后重试")

async def handle_reset_step(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    step = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    logger.info(f"用户 {user_id} 重置调教设定步骤 {step}")

    try:
        if step == 1:
            context.user_data["liked_practices"] = []
            keyboard = []
            for i in range(0, len(PRACTICES), 5):
                row = [InlineKeyboardButton(PRACTICES[j], callback_data=f"select_liked_{PRACTICES[j]}")
                       for j in range(i, min(i + 5, len(PRACTICES)))]
                keyboard.append(row)
            keyboard.append([InlineKeyboardButton("完成选择" if lang == "zh-cn" else "Finish Selection", callback_data="finish_liked_practices")])
            keyboard.append([InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_1")])
            keyboard.append([InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")])
            await query.edit_message_text(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "1. 已重置！选择一些你喜欢的玩法（可多选，选完点击\"完成选择\"）："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "1. Reset complete! Select some practices you like (multiple choices, click 'Finish Selection' when done):",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        elif step == 2:
            context.user_data["acceptable_practices"] = []
            available_practices = [p for p in PRACTICES if p not in context.user_data.get("liked_practices", [])]
            keyboard = []
            for i in range(0, len(available_practices), 5):
                row = [InlineKeyboardButton(available_practices[j], callback_data=f"select_acceptable_{available_practices[j]}")
                       for j in range(i, min(i + 5, len(available_practices)))]
                keyboard.append(row)
            keyboard.append([InlineKeyboardButton("完成选择" if lang == "zh-cn" else "Finish Selection", callback_data="finish_acceptable_practices")])
            keyboard.append([InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_2")])
            keyboard.append([InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")])
            await query.edit_message_text(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "2. 已重置！选择一些你不太喜欢，但主人要求你会做的玩法（可多选，选完点击\"完成选择\"）："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "2. Reset complete! Select practices you don't love but would do if Master demands (multiple choices, click 'Finish Selection' when done):",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        elif step == 3:
            user_data["training_preferences"]["extra_practices"] = "无" if lang == "zh-cn" else "None"
            await save_user_data(user_id, user_data)
            context.user_data["setting_step"] = 3
            await query.edit_message_text(
                "🔥 第一部分：正视你内心深处的自己！🔥\n"
                "3. 已重置！列出一些上述列表中不存在但是你想尝试的玩法（没有请填\"无\"或点击\"无需补充\"）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 1: Face Your Inner Self! 🔥\n"
                "3. Reset complete! List any practices not in the above list that you'd like to try (enter 'None' or click 'No Extra Practices'):\n"
                "Enter in the chat:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("无需补充" if lang == "zh-cn" else "No Extra Practices", callback_data="no_extra_practices")],
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_3")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif step == 4:
            user_data["training_preferences"]["dog_name"] = ""
            await save_user_data(user_id, user_data)
            context.user_data["setting_step"] = 4
            await query.edit_message_text(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "4. 已重置！给自己起一个犬名（不能为空）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "4. Reset complete! Choose a dog name for yourself (cannot be empty):\n"
                "Enter in the chat:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_4")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif step == 5:
            user_data["training_preferences"]["age"] = ""
            await save_user_data(user_id, user_data)
            context.user_data["setting_step"] = 5
            await query.edit_message_text(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "5. 已重置！你的年龄是多少岁？（请输入数字，例如：25）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "5. Reset complete! How old are you? (Enter a number, e.g., 25):\n"
                "Enter in the chat:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_5")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        elif step == 6:
            user_data["training_preferences"]["relationship"] = ""
            await save_user_data(user_id, user_data)
            context.user_data["setting_step"] = 6
            await query.edit_message_text(
                "🔥 第二部分：正视你内心深处的自己！🔥\n"
                "6. 已重置！输入你与主人的关系（如：同学、朋友、陌生人等）：\n"
                "在对话框中输入："
                if lang == "zh-cn" else
                "🔥 Part 2: Face Your Inner Self! 🔥\n"
                "6. Reset complete! Enter your relationship with the master (e.g., classmate, friend, stranger, etc.):\n"
                "Enter in the chat:",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("重置" if lang == "zh-cn" else "Reset", callback_data="reset_step_6")],
                    [InlineKeyboardButton("取消并返回主菜单" if lang == "zh-cn" else "Cancel and Return", callback_data="back_to_main")]
                ])
            )
        logger.info(f"用户 {user_id} 成功重置步骤 {step}")
    except TelegramError as e:
        logger.error(f"重置步骤 {step} 失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！重置时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong resetting, try again later!",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
        )

async def handle_complete_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理用户完成调教设定和调教风格选择的操作"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    try:
        # 用户已经选择了调教风格，现在完成整个设定流程
        from .base import send_message_and_clear_context
        await send_message_and_clear_context(
            chat_id=update.effective_chat.id,
            text=(
                "🔥 贱狗！你的调教设定和调教风格已完成！🔥\n"
                "直接打字或发送语音与爷互动吧！！！"
                if lang == "zh-cn" else
                "🔥 Filthy dog! Your training settings and style are complete! 🔥\n"
                "Master, this filthy dog is ready for training! Type or send voice to interact with me!"
            ),
            context=context,  # 传递 context 以便清除 remind_task
            reply_markup=get_main_menu(user_id)
        )
        logger.info(f"用户 {user_id} 完成所有调教设定和调教风格选择")
        
        # 自动触发AI的第一条回复
        # 构建初始问候消息
        initial_message = "你好主人，我已完成调教设定，请开始调教我吧" if lang == "zh-cn" else "Hello Master, I've completed my training settings, please start training me"
        logger.info(f"用户 {user_id} 完成所有调教设定和调教风格选择，自动触发AI回复")
        
        # 获取用户的语音设置
        from ai_dialogue.user_manager import determine_voice_usage
        use_voice, deduct_type = await determine_voice_usage(user_id, user_data, query.bot, update.effective_chat.id)
        
        # 将初始消息添加到AI队列
        from globals import global_ai_queue
        username = update.effective_user.username or update.effective_user.first_name
        try:
            await global_ai_queue.put((user_id, initial_message, update.effective_chat.id, use_voice, username))
            logger.info(f"用户 {user_id} 自动触发的初始消息已加入AI处理队列")
        except asyncio.QueueFull:
            logger.warning(f"队列已满，用户 {user_id} 自动触发的初始消息被丢弃")
            
    except TelegramError as e:
        logger.error(f"处理完成设定失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.answer("贱奴！处理完成设定时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong completing settings, try again later!")
