# handlers/base.py
import time
import asyncio
import os
import uuid
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError, Forbidden
from config import MASTER_ID, BOT_USERNAME, SEARCH_GROUP_ID, DOWNLOAD_DIR
from utils import get_main_menu, get_redeem_code_menu, get_free_credits_menu, send_with_retry, PRACTICES, get_quick_reply_menu
from logging_config import logger
from database import get_user_data, save_user_data
from globals import global_ai_queue

# Import functions from other handler modules that are needed in button_handler
from .settings import (
    handle_confirm_start, handle_edit_settings, handle_training_settings, handle_settings,
    handle_select_liked, handle_finish_liked_practices, handle_select_acceptable,
    handle_finish_acceptable_practices, handle_no_extra_practices,
    handle_reset_step, handle_training_mode, handle_set_prompt_mode, handle_select_master_gender, handle_select_master_gender_edit,
    handle_create_custom_mode, handle_manage_custom_mode, handle_edit_custom_mode, handle_delete_custom_mode, handle_confirm_delete_custom_mode,
    handle_slave_preference_selection, handle_slave_type_navigation, handle_reset_basic_info, handle_slave_practice_navigation,
    start_slave_type_settings, handle_complete_settings
)
from .payment import (
    handle_pay_membership, handle_payment_callback, handle_buy_redeem_code,
    handle_go_to_pay, handle_telegram_stars, handle_follow_channel, handle_verify_channel,
    handle_search_group, handle_search_group_invite, handle_channel_pinned_click,
    handle_share_bot, handle_personal_center, handle_free_credits, cancel_pending_payment
)
from .voice import (
    handle_voice_settings, handle_choose_voice, handle_prev_voice_page,
    handle_next_voice_page, handle_custom_voice, handle_submit_voice,
    handle_test_voice, handle_delete_voice, handle_confirm_delete_voice,
    handle_choose_language, handle_set_delay, handle_set_sentences, handle_voice_upload
)
from .drift_bottle import (
    handle_drift_bottle_menu, handle_throw_bottle, handle_cancel_throw_bottle,
    handle_pick_bottle, handle_confirm_throw_bottle, handle_voice_for_bottle,
    handle_view_bottle, handle_skip_bottle, handle_reply_bottle,
    handle_cancel_reply_bottle, handle_confirm_reply_bottle, handle_non_voice_for_bottle,
    handle_throw_another_bottle, handle_try_continue_reply, handle_continue_reply,
    handle_end_conversation
)
from .game import (
    handle_start_dice, handle_next_round, handle_end_game
)
from .memory import (
    handle_clear_all_memory, handle_clear_user_memory, handle_cancel_clear_memory
)
from .admin import handle_task_center
from .message import handle_share_link, handle_back_to_main

# Import additional functions needed in forward_message
from dice_game import end_game
from ai_dialogue import handle_broadcast_message, private_chat_with_user, broadcast_data, download_and_process_image, download_and_process_video
from ai_processing_status import ai_processing_status

async def send_message_and_clear_context(chat_id, text, context, reply_markup=None, user_id=None, show_quick_reply=True):
    try:
        # 先发送主消息
        await context.bot.send_message(chat_id=chat_id, text=text, reply_markup=reply_markup)
        
        # 如果需要显示快捷回复菜单并且有用户ID
        if show_quick_reply and user_id and reply_markup:
            # 检查reply_markup是否是主菜单（通过检查是否包含特定按钮）
            is_main_menu = False
            if hasattr(reply_markup, 'inline_keyboard'):
                for row in reply_markup.inline_keyboard:
                    for button in row:
                        if button.text in ["⚙️ 调教设定", "🎲 骰子羞辱", "🧠 任务中心"]:
                            is_main_menu = True
                            break
                    if is_main_menu:
                        break
            
            if is_main_menu:
                from database import get_user_data
                user_data = await get_user_data(user_id)
                lang = user_data.get("preferred_language", "zh-cn")
                quick_reply_menu = get_quick_reply_menu(user_id)
                
                await context.bot.send_message(
                    chat_id=chat_id,
                    text="快捷菜单已启用",  # 使用明确的提示文字
                    reply_markup=quick_reply_menu
                )
    except TelegramError as e:
        logger.error(f"发送消息失败，聊天ID: {chat_id}, 错误: {str(e)}")
    context.user_data.pop("setting_type", None)
    context.user_data.pop("is_edit", None)
    context.user_data.pop("setting_step", None)
    context.user_data.pop("liked_practices", None)
    context.user_data.pop("acceptable_practices", None)
    if "remind_task" in context.user_data:
        context.user_data["remind_task"].cancel()
        context.user_data.pop("remind_task", None)

async def get_pay_menu(user_id):
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    keyboard = [
        [InlineKeyboardButton("购买兑换码（微信支付宝）" if lang == "zh-cn" else "Buy Redeem Code (WeChat/Alipay)", callback_data=f"buy_redeem_code_{user_id}")],
        [InlineKeyboardButton("Telegram Stars" if lang == "zh-cn" else "Telegram Stars", callback_data=f"telegram_stars_{user_id}")],
        [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
    ]
    hint = (
        "💰 **贱奴！支付选项** 💰\n"
        "───────────────────\n"
        "选择 Telegram Stars 支付或购买兑换码（支持微信/支付宝）。\n"
        "───────────────────\n"
        "🔥 快选，别磨蹭！"
    ) if lang == "zh-cn" else (
        "💰 **Filthy dog! Payment Options** 💰\n"
        "───────────────────\n"
        "Choose Telegram Stars payment or buy a redeem code (supports WeChat/Alipay).\n"
        "───────────────────\n"
        "🔥 Pick one, don't dawdle!"
    )
    return InlineKeyboardMarkup(keyboard), hint

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    logger.info(f"收到 /start 命令，用户ID: {user_id}, 聊天ID: {chat_id}, 用户名: {update.effective_user.username}")

    if context.args:
        if context.args[0].startswith("shared_by_"):
            await handle_share_link(user_id, context.args[0], context)

    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 修复：优先检查设定完成标记
    instruction_states = user_data.get("instruction_states", {})
    setting_stage = instruction_states.get("setting_stage", "start")
    
    # 如果设定已标记为完成，直接进入主菜单
    if setting_stage == "complete":
        training_complete = True
        master_complete = True
        scene_complete = True
        all_settings_complete = True
    else:
        # 兼容新旧设定系统的判断逻辑
        # 新系统：检查奴种分类喜好、犬名、年龄
        new_system_complete = bool(
            user_data["training_preferences"].get("slave_type_preferences") and
            user_data["training_preferences"].get("dog_name") and
            user_data["training_preferences"].get("age")
        )
        
        # 旧系统：检查liked_practices、犬名、年龄
        old_system_complete = bool(
            user_data["training_preferences"].get("liked_practices") and
            user_data["training_preferences"].get("dog_name") and
            user_data["training_preferences"].get("age")
        )
        
        # 任一系统完成即可
        training_complete = new_system_complete or old_system_complete
        
        master_complete = bool(user_data["master_settings"].get("name"))
        scene_complete = bool(user_data["scene_settings"].get("location"))
        all_settings_complete = training_complete and master_complete and scene_complete

    if user_data["has_used_bot"] and all_settings_complete:
        reply_markup = get_main_menu(user_id, context)
        quick_reply_menu = get_quick_reply_menu(user_id)
        
        welcome_text = (
            "🔥 贱奴！跪在爷面前，准备接受调教吧！🔥\n"
            "直接打字说话，别浪费爷的时间！\n"
            "─────────────────"
        ) if lang == "zh-cn" else (
            "🔥 Filthy dog! Kneel before me, ready for training! 🔥\n"
            "Type directly, don't waste my time!\n"
            "─────────────────"
        )
        if str(chat_id) == MASTER_ID:
            try:
                await update.message.reply_text("至高无上的爹回来了！贱奴们，跪好迎接调教！输入 /start 重载命令！" if lang == "zh-cn" else "The supreme Master is back! Filthy dogs, kneel and prepare for training! Use /start to reload!")
            except TelegramError as e:
                logger.error(f"发送管理员欢迎消息失败，用户ID: {user_id}, 错误: {str(e)}")
        else:
            try:
                # 发送带inline键盘和快捷回复菜单的欢迎消息
                await update.message.reply_text(welcome_text, reply_markup=reply_markup)
                # 发送快捷菜单提示来激活快捷回复菜单
                await context.bot.send_message(chat_id=chat_id, text="快捷菜单已启用", reply_markup=quick_reply_menu)
            except TelegramError as e:
                logger.error(f"发送欢迎消息失败，用户ID: {user_id}, 错误: {str(e)}")
        return

    user_data["has_used_bot"] = True
    await save_user_data(user_id, user_data)
    logger.info(f"用户 {user_id} 开始设置，has_used_bot: {user_data['has_used_bot']}")

    message = (
        "🔥 贱狗！欢迎来到BDSM虚拟主人的世界！🔥\n"
        "你需要完成以下三个设定以接受调教：\n"
        "1. 调教喜好（玩法、犬名、年龄）\n"
        "2. 主人设定（姓名等信息）\n"
        "3. 场景设定（调教地点等）\n"
        "准备好了就点\"是\"，没准备好请离开！！"
    ) if lang == "zh-cn" else (
        "🔥 Filthy dog! Welcome to the world of your virtual BDSM Master! 🔥\n"
        "You need to complete the following three settings to be trained:\n"
        "1. Training preferences (practices, dog name, age)\n"
        "2. Master settings (name, etc.)\n"
        "3. Scene settings (training location, etc.)\n"
        "Ready? Click 'Yes', or leave if you're not!"
    )
    keyboard = [
        [InlineKeyboardButton("是" if lang == "zh-cn" else "Yes", callback_data="confirm_start")],
        [InlineKeyboardButton("离开" if lang == "zh-cn" else "Leave", callback_data="back_to_main")]
    ]
    try:
        await update.message.reply_text(message, reply_markup=InlineKeyboardMarkup(keyboard))
    except TelegramError as e:
        logger.error(f"发送欢迎信息失败，用户ID: {user_id}, 错误: {str(e)}")

async def clear_memory(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /clearmemory 命令，普通用户清除自己的记忆，管理员清除所有记忆"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if str(user_id) == MASTER_ID:
        # 管理员清除所有用户记忆
        keyboard = [
            [InlineKeyboardButton("确认清空所有记忆" if lang == "zh-cn" else "Confirm Clear All Memory", callback_data="confirm_clear_all_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "至高无上的爹！您要清空所有贱奴的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Supreme Master! Clear all filthy dogs' brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送管理员清空所有记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        # 普通用户清除自己的记忆
        keyboard = [
            [InlineKeyboardButton("确认清空我的记忆" if lang == "zh-cn" else "Confirm Clear My Memory", callback_data="confirm_clear_user_memory")],
            [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_clear_memory")]
        ]
        try:
            await update.message.reply_text(
                "贱奴！你要清空你的狗脑子对话记录？"
                if lang == "zh-cn" else
                "Filthy dog! Clear your brainless chat records?",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送用户清空记忆提示失败，用户ID: {user_id}, 错误: {str(e)}")

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理按钮回调"""
    query = update.callback_query
    callback_data = query.data
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    # 检查回调数据中是否包含用户ID，并限制操作者
    user_id_in_callback = None
    parts = callback_data.split("_")
    
    # 排除奴种设定相关的callback和其他通用callback，这些不包含用户ID
    if callback_data.startswith(("slave_choice_", "slave_practice_", "reset_basic_info_",
                                "edit_", "select_liked_", "select_acceptable_", "select_master_gender_",
                                "training_mode_", "set_prompt_mode_", "voice_settings", "language_",
                                "set_delay_", "set_sentences_", "start_dice", "next_round_", "end_game_",
                                "create_custom_mode_", "manage_custom_mode_", "edit_custom_mode_",
                                "delete_custom_mode_")) or \
       callback_data in ["finish_all_slave_types", "back_to_main", "confirm_start", 
                        "training_preferences", "scene_settings", "master_settings", 
                        "training_settings_menu", "finish_liked_practices", 
                        "finish_acceptable_practices", "no_extra_practices",
                        "finish_liked_practices_edit", "finish_acceptable_practices_edit", 
                        "no_extra_practices_edit", "complete_settings",
                        "confirm_clear_all_memory", "confirm_clear_user_memory", "cancel_clear_memory",
                        "cancel_pending_payment", "return_previous"]:
        user_id_in_callback = None
    # 处理会员支付相关回调：pay_membership_30_7359571866
    elif callback_data.startswith("pay_membership_") and len(parts) >= 4 and parts[-1].isdigit():
        user_id_in_callback = int(parts[-1])
    # 处理其他支付相关回调，支付回调的格式通常为 pay_text_{user_id}_{amount} 或类似
    elif callback_data.startswith("pay_") and len(parts) > 2 and parts[-2].isdigit():
        user_id_in_callback = int(parts[-2])
    # 处理一般回调，用户ID在最后
    elif len(parts) > 1 and parts[-1].isdigit():
        user_id_in_callback = int(parts[-1])
    
    if user_id_in_callback and user_id_in_callback != user_id:
        try:
            await query.answer(
                "贱奴！这是别人的菜单，滚去操作自己的！"
                if lang == "zh-cn" else
                "Filthy dog! This is someone else's menu, go use your own!"
            )
            logger.info(f"用户 {user_id} 尝试操作他人菜单，回调数据: {callback_data}")
            return
        except TelegramError as e:
            logger.error(f"发送菜单权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
            return

    try:
        if callback_data == "confirm_start":
            await handle_confirm_start(update, context)
        elif callback_data.startswith("edit_"):
            setting_type = callback_data[5:]
            await handle_edit_settings(update, context)
        elif callback_data == "training_settings_menu":
            await handle_training_settings(update, context)
        elif callback_data in ["training_preferences", "scene_settings", "master_settings"]:
            await handle_settings(update, context)  # 处理调教设定相关回调
        elif callback_data.startswith("voice_settings"):
            await handle_voice_settings(update, context)
        elif callback_data.startswith("personal_center_"):
            await handle_personal_center(update, context)
        elif callback_data.startswith("free_credits_"):
            await handle_free_credits(update, context)
        elif callback_data.startswith("drift_bottle_"):
            await handle_drift_bottle_menu(update, context)
        elif callback_data.startswith("throw_bottle_"):
            await handle_throw_bottle(update, context)
        elif callback_data.startswith("throw_another_bottle_"):
            await handle_throw_another_bottle(update, context)
        elif callback_data.startswith("pick_bottle_"):
            await handle_pick_bottle(update, context)
        elif callback_data == "cancel_throw_bottle":
            await handle_cancel_throw_bottle(update, context)
        elif callback_data == "confirm_throw_bottle":
            await handle_confirm_throw_bottle(update, context)
        elif callback_data.startswith("view_bottle_"):
            await handle_view_bottle(update, context)
        elif callback_data.startswith("skip_bottle_"):
            await handle_skip_bottle(update, context)
        elif callback_data.startswith("reply_bottle_"):
            await handle_reply_bottle(update, context)
        elif callback_data == "cancel_reply_bottle":
            await handle_cancel_reply_bottle(update, context)
        elif callback_data == "confirm_reply_bottle":
            await handle_confirm_reply_bottle(update, context)
        elif callback_data.startswith("try_continue_reply_"):
            await handle_try_continue_reply(update, context)
        elif callback_data.startswith("continue_reply_"):
            await handle_continue_reply(update, context)
        elif callback_data.startswith("end_conversation_"):
            await handle_end_conversation(update, context)
        elif callback_data.startswith("pay_"):
            await handle_pay_membership(update, context)
        elif callback_data.startswith("confirm_pay_"):
            await handle_payment_callback(update, context)
        elif callback_data.startswith("buy_redeem_code_"):
            await handle_buy_redeem_code(update, context)
        elif callback_data.startswith("redeem_code_"):
            await handle_payment_callback(update, context)
        elif callback_data.startswith("go_to_pay_"):
            await handle_go_to_pay(update, context)
        elif callback_data.startswith("telegram_stars_"):
            await handle_telegram_stars(update, context)
        elif callback_data.startswith("select_liked_"):
            await handle_select_liked(update, context)
        elif callback_data == "finish_liked_practices":
            await handle_finish_liked_practices(update, context)
        elif callback_data.startswith("select_acceptable_"):
            await handle_select_acceptable(update, context)
        elif callback_data == "finish_acceptable_practices":
            await handle_finish_acceptable_practices(update, context)
        elif callback_data == "no_extra_practices":
            await handle_no_extra_practices(update, context)
        elif callback_data.startswith("reset_step_"):
            await handle_reset_step(update, context)
        elif callback_data == "confirm_clear_all_memory":
            await handle_clear_all_memory(update, context)
        elif callback_data == "confirm_clear_user_memory":
            await handle_clear_user_memory(update, context)
        elif callback_data == "cancel_clear_memory":
            await handle_cancel_clear_memory(update, context)
        elif callback_data.startswith("language_"):
            await handle_choose_language(update, context)
        elif callback_data.startswith("set_delay_"):
            await handle_set_delay(update, context)
        elif callback_data.startswith("set_sentences_"):
            await handle_set_sentences(update, context)
        elif callback_data == "cancel_pending_payment":
            await cancel_pending_payment(update, context)
        elif callback_data == "return_previous":
            await handle_back_to_main(update, context)
        elif callback_data.startswith("start_dice"):
            await handle_start_dice(update, context)
        elif callback_data.startswith("next_round_"):
            await handle_next_round(update, context)
        elif callback_data.startswith("end_game_"):
            await handle_end_game(update, context)
        elif callback_data.startswith("choose_voice_"):
            await handle_choose_voice(update, context)
        elif callback_data.startswith("prev_voice_page_"):
            await handle_prev_voice_page(update, context)
        elif callback_data.startswith("next_voice_page_"):
            await handle_next_voice_page(update, context)
        elif callback_data.startswith("follow_channel_"):
            await handle_follow_channel(update, context)
        elif callback_data.startswith("verify_channel_"):
            await handle_verify_channel(update, context)
        elif callback_data.startswith("search_group_invite_"):
            await handle_search_group_invite(update, context)
        elif callback_data.startswith("search_group_"):
            await handle_search_group(update, context)
        elif callback_data.startswith("channel_pinned_click_"):
            await handle_channel_pinned_click(update, context)
        elif callback_data.startswith("share_bot_"):
            await handle_share_bot(update, context)
        elif callback_data.startswith("custom_voice_"):
            await handle_custom_voice(update, context)
        elif callback_data.startswith("submit_voice_"):
            await handle_submit_voice(update, context)
        elif callback_data.startswith("test_voice_"):
            await handle_test_voice(update, context)
        elif callback_data.startswith("delete_voice_"):
            await handle_delete_voice(update, context)
        elif callback_data.startswith("confirm_delete_voice_"):
            await handle_confirm_delete_voice(update, context)
        elif callback_data.startswith("task_center_"):
            await handle_task_center(update, context)
        elif callback_data.startswith("training_mode_"):
            await handle_training_mode(update, context)
        elif callback_data.startswith("set_prompt_mode_"):
            await handle_set_prompt_mode(update, context)
        elif callback_data.startswith("select_master_gender_"):
            if callback_data.endswith("_edit"):
                await handle_select_master_gender_edit(update, context)
            else:
                await handle_select_master_gender(update, context)
        elif callback_data.startswith("slave_choice_"):
            # 处理奴种喜好选择: slave_choice_{slave_type_index}_{practice_index}_{preference}
            await handle_slave_preference_selection(update, context)
        elif callback_data.startswith("slave_practice_"):
            # 处理奴种设定中的玩法导航: slave_practice_{slave_type_index}_{practice_index}
            await handle_slave_practice_navigation(update, context)
        elif callback_data == "finish_all_slave_types":
            # 处理奴种设定导航
            await handle_slave_type_navigation(update, context)
        elif callback_data.startswith("reset_basic_info_"):
            # 处理基本信息重置
            await handle_reset_basic_info(update, context)
        elif callback_data.startswith("create_custom_mode_"):
            await handle_create_custom_mode(update, context)
        elif callback_data.startswith("manage_custom_mode_"):
            await handle_manage_custom_mode(update, context)
        elif callback_data.startswith("edit_custom_mode_"):
            await handle_edit_custom_mode(update, context)
        elif callback_data.startswith("delete_custom_mode_"):
            await handle_delete_custom_mode(update, context)
        elif callback_data.startswith("confirm_delete_custom_mode_"):
            await handle_confirm_delete_custom_mode(update, context)
        elif callback_data.startswith("start_slave_edit_"):
            # 处理奴种编辑开始: start_slave_edit_{slave_type_index}
            from .settings import start_slave_type_settings
            slave_type_index = int(callback_data.split("_")[-1])
            await start_slave_type_settings(update, context, user_data, slave_type_index, 0)
        elif callback_data == "complete_settings":
            await handle_complete_settings(update, context)
        elif callback_data == "back_to_main":
            await handle_back_to_main(update, context)
        else:
            logger.warning(f"未知的回调数据，用户ID: {user_id}, 数据: {callback_data}")
            await query.answer("无效的操作！" if lang == "zh-cn" else "Invalid operation!")

    except Exception as e:
        logger.error(f"处理按钮回调失败，用户ID: {user_id}, 回调数据: {callback_data}, 错误: {str(e)}", exc_info=True)
        try:
            await query.edit_message_text(
                "贱奴！操作时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong, try again later!",
                reply_markup=get_main_menu(user_id)
            )
        except TelegramError as te:
            logger.error(f"发送错误提示失败，用户ID: {user_id}, 错误: {str(te)}")

async def forward_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    处理所有类型的消息，包括文本、图片、语音、文档等
    进行初步分类和预处理后转发给相应的处理器
    """
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    chat_type = update.effective_chat.type
    username = update.effective_user.username or update.effective_user.first_name

    # 更新用户最后交互时间（用于漂流瓶优先级计算）
    try:
        from database import update_user_last_interaction
        asyncio.create_task(update_user_last_interaction(user_id))
    except Exception as e:
        logger.error(f"更新用户最后交互时间失败，用户ID: {user_id}, 错误: {str(e)}")

    # 检查是否为管理员的用户信息查看流程输入
    if update.message and update.message.text:
        from .admin import handle_admin_user_input
        admin_handled = await handle_admin_user_input(update, context)
        if admin_handled:
            return  # 如果被管理员处理器处理了，直接返回

    # 获取用户数据
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 如果用户之前被标记为已拉黑，现在收到了新消息，则重置拉黑状态
    if user_data.get("is_blocked", 0) == 1:
        user_data["is_blocked"] = 0
        await save_user_data(user_id, user_data)
        logger.info(f"用户 {user_id} 重新与Bot交互，重置is_blocked状态为0")

    logger.info(f"用户 {user_id} 在聊天 {chat_id} ({chat_type}) 中发送了消息")

    # 定义快捷回复菜单选项
    quick_reply_options = set(["菜单", "犬舍", "调教设定", "调教风格", "语音设置", 
                           "会员身份", "免费积分", "骰子游戏", "任务中心", "个人中心"])
    
    # 检查是否为快捷回复消息
    is_quick_reply = update.message.text and update.message.text in quick_reply_options
    
    # 检查用户是否正在AI处理中
    user_is_processing = await ai_processing_status.is_user_processing(user_id)

    # 如果用户正在AI处理中且不是快捷回复，则只转发消息但不进行AI处理
    if user_is_processing and not is_quick_reply:
        # 增加忽略计数
        await ai_processing_status.increment_ignored_count(user_id)

        # 静默忽略AI处理，但仍然转发消息给管理员
        logger.info(f"用户 {user_id} 正在AI处理中，静默忽略AI处理但转发消息: {update.message.text if update.message.text else '非文本消息'}")

        # 设置标志，表示只转发不处理AI
        forward_only = True
    else:
        forward_only = False

    # 辅助函数：使用官方转发功能转发用户消息到MASTER_ID
    async def forward_user_message_to_master():
        try:
            # 使用 Telegram 官方转发功能
            forwarded_message = await context.bot.forward_message(
                chat_id=MASTER_ID,
                from_chat_id=chat_id,
                message_id=update.message.message_id
            )
            logger.debug(f"官方转发用户消息到 MASTER_ID，来源聊天ID: {chat_id}, 消息ID: {update.message.message_id}")

            # 存储转发消息映射关系
            if forwarded_message:
                from utils import store_forwarded_message_mapping
                await store_forwarded_message_mapping(
                    original_chat_id=chat_id,
                    original_message_id=update.message.message_id,
                    forwarded_message_id=forwarded_message.message_id
                )
                logger.debug(f"存储转发消息映射：原始({chat_id}:{update.message.message_id}) -> 转发({forwarded_message.message_id})")

        except TelegramError as e:
            logger.error(f"官方转发用户消息失败，来源聊天ID: {chat_id}, 消息ID: {update.message.message_id}, 错误: {str(e)}")
        except Exception as e_gen:
            logger.error(f"官方转发时发生一般错误: {e_gen}, 来源聊天ID: {chat_id}, 消息ID: {update.message.message_id}")

    # 辅助函数：静默转发消息到MASTER_ID（保留用于AI回复和需要拼接信息的场景）
    async def silent_forward_to_master(message_type, content=None, file_id=None, caption=None, original_sender_info=""):
        try:
            # 构造基础信息
            base_info = f"类型: {message_type}\n来源聊天: {chat_id} ({update.effective_chat.title or '未知私聊或群组'})"
            if original_sender_info: # 如果提供了原始发送者信息（例如频道帖子）
                 log_message = f"{original_sender_info}\n{base_info}"
            elif update.effective_user: # 普通用户消息
                log_message = f"用户: {update.effective_user.username or update.effective_user.full_name} (ID: {update.effective_user.id})\n{base_info}"
            else: # 其他情况，例如机器人自身消息或其他未预料情况
                log_message = base_info

            if file_id:
                full_caption = log_message + (f"\n原始说明: {caption}" if caption else "")
                if message_type == "photo":
                    await context.bot.send_photo(chat_id=MASTER_ID, photo=file_id, caption=full_caption[:1024])
                elif message_type == "video":
                    await context.bot.send_video(chat_id=MASTER_ID, video=file_id, caption=full_caption[:1024])
                elif message_type == "document":
                    await context.bot.send_document(chat_id=MASTER_ID, document=file_id, caption=full_caption[:1024])
                elif message_type == "voice":
                    await context.bot.send_voice(chat_id=MASTER_ID, voice=file_id, caption=full_caption[:1024])
                elif message_type == "audio":
                    await context.bot.send_audio(chat_id=MASTER_ID, audio=file_id, caption=full_caption[:1024])

            else:
                full_text = log_message + f"\n内容: {content}"
                await context.bot.send_message(chat_id=MASTER_ID, text=full_text[:4096])
            logger.debug(f"静默转发 {message_type} 到 MASTER_ID，来源聊天ID: {chat_id}")
        except TelegramError as e:
            logger.error(f"静默转发 {message_type} 失败，来源聊天ID: {chat_id}, 错误: {str(e)}")
        except Exception as e_gen:
            logger.error(f"静默转发时发生一般错误: {e_gen}, 类型: {message_type}, 来源聊天ID: {chat_id}")

    # 1. 处理频道消息 (Channel Posts)
    if update.channel_post:
        message = update.channel_post
        sender_chat_info = f"频道: {message.sender_chat.title if message.sender_chat else '未知频道'} (ID: {message.sender_chat.id if message.sender_chat else '未知'})"
        if message.photo:
            await silent_forward_to_master("photo", file_id=message.photo[-1].file_id, caption=message.caption, original_sender_info=sender_chat_info)
        elif message.video:
            await silent_forward_to_master("video", file_id=message.video.file_id, caption=message.caption, original_sender_info=sender_chat_info)
        elif message.text:
            await silent_forward_to_master("text", content=message.text.strip(), original_sender_info=sender_chat_info)
        elif message.voice:
            await silent_forward_to_master("voice", file_id=message.voice.file_id, caption=message.caption, original_sender_info=sender_chat_info)
        # 可以根据需要添加对其他频道消息类型的处理，如 document, audio 等
        return # 频道消息处理完毕

    # 2. 处理用户消息 (私聊或群组)
    if not update.message: # 如果不是频道消息，则必须是用户消息
        logger.debug(f"收到的更新不是频道帖子或用户消息: {update}")
        return

    is_search_group = str(chat_id) == SEARCH_GROUP_ID # 提前判断是否为搜索群

    # 检查是否为广播消息输入
    if str(update.effective_user.id) == MASTER_ID and update.effective_user.id in broadcast_data:
        await handle_broadcast_message(update, context)
        return
    # 检查是否为私聊消息输入
    if str(update.effective_user.id) == MASTER_ID and update.message.text and update.message.text.startswith("/notice "):
        await private_chat_with_user(update, context)
        return

    # --- 图片消息处理 ---
    if update.message.photo:
        photo_id = update.message.photo[-1].file_id
        caption_text = update.message.caption or ""

        # 使用官方转发功能转发用户图片消息
        await forward_user_message_to_master()

        # 检查重复图片并保存记录 (这部分逻辑可以保留，因为它有助于追踪用户行为)
        existing_photos = [p["file_id"] for p in user_data.get("photo_file_ids", []) if isinstance(p, dict) and "file_id" in p]
        if photo_id in existing_photos:
            await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                  text="贱奴！你提交了重复的照片，滚去重新拍一张！" if lang == "zh-cn" else "Filthy dog! You submitted a duplicate photo, go take a new one!",
                                  reply_to_message_id=update.message.message_id)
            logger.info(f"用户 {user_id} 提交重复照片，file_id: {photo_id}")
            return
        user_data.setdefault("photo_file_ids", []).append({"file_id": photo_id, "timestamp": datetime.now().isoformat()})
        await save_user_data(user_id, user_data)

        # 条件判断：是否进行AI处理和发送识图提示
        should_process_for_ai = False
        reply_to_message_id_for_notification = None

        if chat_type == "private":
            should_process_for_ai = True
        elif chat_type in ["group", "supergroup"]:
            is_bot_mentioned_in_caption = BOT_USERNAME in caption_text
            is_reply_to_bot = update.message.reply_to_message and update.message.reply_to_message.from_user.id == context.bot.id
            if is_reply_to_bot or is_bot_mentioned_in_caption:
                should_process_for_ai = True
                reply_to_message_id_for_notification = update.message.message_id
            else:
                logger.info(f"群聊 {chat_id} 中用户 {user_id} 发送图片，但未明确引用Bot，仅转发管理员。")

        if should_process_for_ai and not forward_only:
            # 检查用户是否在漂流瓶状态下发送了非语音消息
            from .drift_bottle import handle_non_voice_for_bottle
            bottle_handled = await handle_non_voice_for_bottle(update, context)
            if bottle_handled:
                return  # 如果被漂流瓶处理器处理了，直接返回

            if user_data.get("waiting_for_proof"): # 骰子游戏证明优先
                # 导入图片审核函数
                from dice_game import verify_dice_proof_image
                
                # 获取当前游戏状态
                current_clothes = user_data.get("clothes", 0)
                previous_clothes = user_data.get("previous_clothes", 5)
                round_num = user_data.get("dice_round", 1) - 1  # 当前轮次是提交证明的轮次
                
                # 进行图片审核
                verification_passed, verification_message, is_fully_naked = await verify_dice_proof_image(
                    context.bot, photo_id, user_id, current_clothes, previous_clothes, round_num
                )
                
                # 记录骰子游戏验证结果到日志
                logger.info(f"骰子游戏证明验证结果 - 用户ID: {user_id}, 轮次: 第{round_num}轮, "
                           f"当前衣物: {current_clothes}件, 上轮衣物: {previous_clothes}件, "
                           f"验证通过: {verification_passed}, 全脱光: {is_fully_naked}, "
                           f"验证消息: {verification_message}")
                
                # 转发AI审核结论给管理员
                try:
                    admin_report = (
                        f"🎲 骰子游戏图片证明报告 🎲\n"
                        f"───────────────────\n"
                        f"用户ID: {user_id}\n"
                        f"游戏轮次: 第{round_num}轮\n"
                        f"衣物状态: {previous_clothes}→{current_clothes}件\n"
                        f"证明类型: 图片\n"
                        f"审核结果: {'✅通过' if verification_passed else '❌不通过'}\n"
                        f"全脱光状态: {'✅是' if is_fully_naked else '❌否'}\n"
                        f"最终判定: {'✅自动结束游戏' if is_fully_naked else ('✅允许进入下一轮' if verification_passed else '❌需要重新提交')}\n"
                        f"备注: {verification_message}\n"
                        f"───────────────────"
                    )
                    await context.bot.send_message(chat_id=MASTER_ID, text=admin_report, parse_mode=None)
                    logger.info(f"用户 {user_id} 骰子游戏图片证明报告已转发给管理员")
                except Exception as e:
                    logger.error(f"转发用户 {user_id} 骰子游戏图片证明报告给管理员失败: {str(e)}")
                
                if verification_passed:
                    # 验证通过时重置连续失败次数
                    user_data["dice_fail_count"] = 0
                    
                    # 检查是否全脱光，如果是则直接结束游戏
                    if is_fully_naked:
                        # 导入结束游戏函数
                        from dice_game import end_game
                        
                        # 发送全脱光消息
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n检测到全脱光，骰子游戏直接结束！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nDetected fully naked, dice game ends immediately!",
                                              reply_to_message_id=update.message.message_id)
                        
                        # 重置游戏状态并结束游戏
                        user_data["dice_round"] = 0
                        user_data["clothes"] = 0
                        user_data["previous_clothes"] = 5
                        user_data["waiting_for_proof"] = False
                        user_data["dice_fail_count"] = 0
                        await save_user_data(user_id, user_data)
                        
                        # 发送游戏结束消息
                        await end_game(update, context, chat_id)
                        logger.info(f"用户 {user_id} 骰子游戏因全脱光自动结束")
                    else:
                        # 审核通过，允许继续游戏
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n乖乖等着下一轮！请输入 /dice 进行下一轮游戏！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nWait for the next round! Please enter /dice to continue to the next round!",
                                              reply_to_message_id=update.message.message_id)
                        user_data["waiting_for_proof"] = False
                        await save_user_data(user_id, user_data)
                        logger.info(f"用户 {user_id} 骰子游戏证明审核通过，已允许进入下一轮")
                else:
                    # 审核不通过，不重新加载用户数据，直接使用当前的用户数据来保持失败计数
                    current_fail_count = user_data.get("dice_fail_count", 0)
                    fail_count = current_fail_count + 1
                    user_data["dice_fail_count"] = fail_count
                    
                    logger.info(f"用户 {user_id} 骰子游戏验证失败，当前失败次数：{current_fail_count} → {fail_count}")
                    
                    # 检查是否连续两次失败
                    if fail_count >= 2:
                        # 连续两次失败，自动结束游戏
                        from dice_game import end_game
                        
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n连续两次验证失败，骰子游戏强制结束！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nTwo consecutive verification failures, dice game forcibly ended!",
                                              reply_to_message_id=update.message.message_id)
                        
                        # 重置游戏状态并结束游戏
                        user_data["dice_round"] = 0
                        user_data["clothes"] = 0
                        user_data["previous_clothes"] = 5
                        user_data["waiting_for_proof"] = False
                        user_data["dice_fail_count"] = 0
                        await save_user_data(user_id, user_data)
                        
                        # 发送游戏结束消息
                        await end_game(update, context, chat_id)
                        logger.info(f"用户 {user_id} 骰子游戏因连续两次失败自动结束")
                    else:
                        # 第一次失败，要求重新提交
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n重新拍照提交，别想糊弄爷！（失败次数：{fail_count}/2）" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nRetake the photo and submit, don't try to fool me! (Failures: {fail_count}/2)",
                                              reply_to_message_id=update.message.message_id)
                        # 保存失败次数，保持waiting_for_proof状态，要求重新提交
                        await save_user_data(user_id, user_data)
                        logger.info(f"用户 {user_id} 骰子游戏证明审核不通过，失败次数：{fail_count}，要求重新提交")
            else: # 标准AI识图流程
                try:
                    await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                          text="AI爹正在识图中，请稍候...",
                                          reply_to_message_id=reply_to_message_id_for_notification)
                    logger.info(f"用户 {user_id} 在 {chat_type} ({chat_id}) 发送了图片，已发送识图提示。")
                except Exception as e_notify:
                    logger.error(f"发送识图提示给用户 {user_id} ({chat_type}) 失败: {e_notify}")

                # 检查是否有支持多模态的API
                from config import AI_API_CONFIGS
                has_multimodal_api = any(config.get("is_multimodal", False) for config in AI_API_CONFIGS)

                if has_multimodal_api:
                    # 优先使用多模态API处理
                    try:
                        # 下载图片并转换为base64
                        image_data = await download_and_process_image(context.bot, photo_id, user_id, logger)

                        if image_data:
                            # 构建更自然的多模态消息
                            if caption_text.strip():
                                # 用户有说明文字，直接使用
                                multimodal_message = caption_text
                            else:
                                # 用户没有说明文字，根据对话历史决定如何处理
                                has_recent_conversation = user_data.get("conversation") and len(user_data["conversation"]) > 0

                                if has_recent_conversation:
                                    # 有对话历史，使用更简洁的提示让AI自然融入对话
                                    if lang == "zh-cn":
                                        multimodal_message = ""  # 空消息，让AI根据图片和上下文自主回应
                                    else:
                                        multimodal_message = ""
                                else:
                                    # 没有对话历史，使用简单提示
                                    if lang == "zh-cn":
                                        multimodal_message = "看看这个"
                                    else:
                                        multimodal_message = "Look at this"

                            # 根据用户的语音设置决定是否使用语音回复
                            from ai_dialogue.user_manager import determine_voice_usage
                            use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)

                            # 使用多模态对话处理
                            from ai_dialogue.chat_processor import chat_with_ai_multimodal

                            # 直接调用多模态处理函数，不通过队列
                            try:
                                responses, final_deduct_type = await chat_with_ai_multimodal(
                                    user_id, multimodal_message, image_data, deduct_type, logger, context.bot
                                )

                                if responses:
                                    # 发送响应
                                    from ai_dialogue.response_sender import send_response
                                    await send_response(
                                        context.bot, chat_id, user_id, (responses, final_deduct_type),
                                        use_voice, username, None, update
                                    )
                                    logger.info(f"用户 {user_id} 多模态图片处理成功，用户说明: '{caption_text}'")
                                    return  # 多模态处理成功，直接返回
                                else:
                                    logger.warning(f"用户 {user_id} 多模态API返回空响应，回退到本地识图")
                            except Exception as e:
                                logger.warning(f"用户 {user_id} 多模态处理失败，回退到本地识图: {str(e)}")
                        else:
                            logger.warning(f"用户 {user_id} 图片下载失败，回退到本地识图")
                    except Exception as e:
                        logger.warning(f"用户 {user_id} 多模态处理异常，回退到本地识图: {str(e)}")

                # 回退到原有的本地识图流程
                image_description = await download_and_process_image(context.bot, photo_id, user_id, logger)

                # 构建包含用户说明和AI识图描述的完整消息
                if caption_text.strip():
                    fake_message = f"[📷图片识别内容]\n用户发送了一张图片并说明：{caption_text}\n\n🔍AI识图分析结果：\n{image_description}\n\n请基于这张图片的内容与用户进行对话。" if lang == "zh-cn" else f"[📷Image Recognition Content]\nUser sent a photo with caption: {caption_text}\n\n🔍AI Image Analysis Result:\n{image_description}\n\nPlease engage with the user based on this image content."
                else:
                    fake_message = f"[📷图片识别内容]\n用户发送了一张图片\n\n🔍AI识图分析结果：\n{image_description}\n\n请基于这张图片的内容与用户进行对话。" if lang == "zh-cn" else f"[📷Image Recognition Content]\nUser sent a photo\n\n🔍AI Image Analysis Result:\n{image_description}\n\nPlease engage with the user based on this image content."

                try:
                    # 根据用户的语音设置决定是否使用语音回复
                    from ai_dialogue.user_manager import determine_voice_usage

                    use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)

                    await global_ai_queue.put((user_id, fake_message, chat_id, use_voice, username))
                    logger.info(f"用户 {user_id} 发送图片，使用本地识图，用户说明: '{caption_text}'，AI描述: {image_description[:100]}...")
                except asyncio.QueueFull:
                    logger.warning(f"队列已满，用户 {user_id} 图片消息触发AI羞辱被丢弃")
                    await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                          text="贱奴！爷忙得要死，稍后再来跪舔！" if lang == "zh-cn" else "Filthy dog! I'm swamped, come back later to grovel!",
                                          reply_to_message_id=update.message.message_id if chat_type != "private" else None)
        return # 图片消息处理完毕

    # --- 视频消息处理 ---
    if update.message.video:
        video_id = update.message.video.file_id
        caption_text = update.message.caption or ""

        # 使用官方转发功能转发用户视频消息
        await forward_user_message_to_master()

        # 检查重复视频并保存记录
        existing_videos = [v["file_id"] for v in user_data.get("video_file_ids", []) if isinstance(v, dict) and "file_id" in v]
        if video_id in existing_videos:
            await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                  text="贱奴！你提交了重复的视频，滚去重新拍一段！" if lang == "zh-cn" else "Filthy dog! You submitted a duplicate video, go take a new one!",
                                  reply_to_message_id=update.message.message_id)
            logger.info(f"用户 {user_id} 提交重复视频，file_id: {video_id}")
            return
        user_data.setdefault("video_file_ids", []).append({"file_id": video_id, "timestamp": datetime.now().isoformat()})
        await save_user_data(user_id, user_data)

        # 条件判断：是否进行AI处理和发送识别提示
        should_process_for_ai = False
        reply_to_message_id_for_notification = None

        if chat_type == "private":
            should_process_for_ai = True
        elif chat_type in ["group", "supergroup"]:
            is_bot_mentioned_in_caption = BOT_USERNAME in caption_text
            is_reply_to_bot = update.message.reply_to_message and update.message.reply_to_message.from_user.id == context.bot.id
            if is_reply_to_bot or is_bot_mentioned_in_caption:
                should_process_for_ai = True
                reply_to_message_id_for_notification = update.message.message_id
            else:
                logger.info(f"群聊 {chat_id} 中用户 {user_id} 发送视频，但未明确引用Bot，仅转发管理员。")

        if should_process_for_ai and not forward_only:
            # 检查用户是否在漂流瓶状态下发送了非语音消息
            from .drift_bottle import handle_non_voice_for_bottle
            bottle_handled = await handle_non_voice_for_bottle(update, context)
            if bottle_handled:
                return  # 如果被漂流瓶处理器处理了，直接返回

            if user_data.get("waiting_for_proof"): # 骰子游戏证明优先
                # 导入视频审核函数
                from dice_game import verify_dice_proof_video
                
                # 获取当前游戏状态
                current_clothes = user_data.get("clothes", 0)
                previous_clothes = user_data.get("previous_clothes", 5)
                round_num = user_data.get("dice_round", 1) - 1  # 当前轮次是提交证明的轮次
                
                # 进行视频审核
                verification_passed, verification_message, is_fully_naked = await verify_dice_proof_video(
                    context.bot, video_id, user_id, current_clothes, previous_clothes, round_num
                )
                
                # 记录骰子游戏验证结果到日志
                logger.info(f"骰子游戏视频证明验证结果 - 用户ID: {user_id}, 轮次: 第{round_num}轮, "
                           f"当前衣物: {current_clothes}件, 上轮衣物: {previous_clothes}件, "
                           f"验证通过: {verification_passed}, 全脱光: {is_fully_naked}, "
                           f"验证消息: {verification_message}")
                
                # 转发AI审核结论给管理员
                try:
                    admin_report = (
                        f"🎲 骰子游戏视频证明报告 🎲\n"
                        f"───────────────────\n"
                        f"用户ID: {user_id}\n"
                        f"游戏轮次: 第{round_num}轮\n"
                        f"衣物状态: {previous_clothes}→{current_clothes}件\n"
                        f"证明类型: 视频\n"
                        f"审核结果: {'✅通过' if verification_passed else '❌不通过'}\n"
                        f"全脱光状态: {'✅是' if is_fully_naked else '❌否'}\n"
                        f"最终判定: {'✅自动结束游戏' if is_fully_naked else ('✅允许进入下一轮' if verification_passed else '❌需要重新提交')}\n"
                        f"备注: {verification_message}\n"
                        f"───────────────────"
                    )
                    await context.bot.send_message(chat_id=MASTER_ID, text=admin_report, parse_mode=None)
                    logger.info(f"用户 {user_id} 骰子游戏视频证明报告已转发给管理员")
                except Exception as e:
                    logger.error(f"转发用户 {user_id} 骰子游戏视频证明报告给管理员失败: {str(e)}")
                
                if verification_passed:
                    # 验证通过时重置连续失败次数
                    user_data["dice_fail_count"] = 0
                    
                    # 检查是否全脱光，如果是则直接结束游戏
                    if is_fully_naked:
                        # 导入结束游戏函数
                        from dice_game import end_game
                        
                        # 发送全脱光消息
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n检测到全脱光，骰子游戏直接结束！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nDetected fully naked, dice game ends immediately!",
                                              reply_to_message_id=update.message.message_id)
                        
                        # 重置游戏状态并结束游戏
                        user_data["dice_round"] = 0
                        user_data["clothes"] = 0
                        user_data["previous_clothes"] = 5
                        user_data["waiting_for_proof"] = False
                        user_data["dice_fail_count"] = 0
                        await save_user_data(user_id, user_data)
                        
                        # 发送游戏结束消息
                        await end_game(update, context, chat_id)
                        logger.info(f"用户 {user_id} 骰子游戏因全脱光自动结束")
                    else:
                        # 审核通过，允许继续游戏
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n乖乖等着下一轮！请输入 /dice 进行下一轮游戏！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nWait for the next round! Please enter /dice to continue to the next round!",
                                              reply_to_message_id=update.message.message_id)
                        user_data["waiting_for_proof"] = False
                        await save_user_data(user_id, user_data)
                        logger.info(f"用户 {user_id} 骰子游戏视频证明审核通过，已允许进入下一轮")
                else:
                    # 审核不通过，不重新加载用户数据，直接使用当前的用户数据来保持失败计数
                    current_fail_count = user_data.get("dice_fail_count", 0)
                    fail_count = current_fail_count + 1
                    user_data["dice_fail_count"] = fail_count
                    
                    logger.info(f"用户 {user_id} 骰子游戏视频验证失败，当前失败次数：{current_fail_count} → {fail_count}")
                    
                    # 检查是否连续两次失败
                    if fail_count >= 2:
                        # 连续两次失败，自动结束游戏
                        from dice_game import end_game
                        
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n连续两次验证失败，骰子游戏强制结束！" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nTwo consecutive verification failures, dice game forcibly ended!",
                                              reply_to_message_id=update.message.message_id)
                        
                        # 重置游戏状态并结束游戏
                        user_data["dice_round"] = 0
                        user_data["clothes"] = 0
                        user_data["previous_clothes"] = 5
                        user_data["waiting_for_proof"] = False
                        user_data["dice_fail_count"] = 0
                        await save_user_data(user_id, user_data)
                        
                        # 发送游戏结束消息
                        await end_game(update, context, chat_id)
                        logger.info(f"用户 {user_id} 骰子游戏因连续两次失败自动结束")
                    else:
                        # 第一次失败，要求重新提交
                        await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                              text=f"贱奴！{verification_message}\n重新拍摄视频提交，别想糊弄爷！（失败次数：{fail_count}/2）" if lang == "zh-cn" else f"Filthy dog! {verification_message}\nRetake the video and submit, don't try to fool me! (Failures: {fail_count}/2)",
                                              reply_to_message_id=update.message.message_id)
                        # 保存失败次数，保持waiting_for_proof状态，要求重新提交
                        await save_user_data(user_id, user_data)
                        logger.info(f"用户 {user_id} 骰子游戏视频证明审核不通过，失败次数：{fail_count}，要求重新提交")
            else: # 标准AI识别流程
                try:
                    await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                          text="AI爹正在识别视频中，请稍候...",
                                          reply_to_message_id=reply_to_message_id_for_notification)
                    logger.info(f"用户 {user_id} 在 {chat_type} ({chat_id}) 发送了视频，已发送识别提示。")
                except Exception as e_notify:
                    logger.error(f"发送视频识别提示给用户 {user_id} ({chat_type}) 失败: {e_notify}")

                # 检查是否有支持多模态的API
                from config import AI_API_CONFIGS
                has_multimodal_api = any(config.get("is_multimodal", False) for config in AI_API_CONFIGS)

                if has_multimodal_api:
                    # 优先使用多模态API处理视频多帧
                    try:
                        # 下载视频并提取多个关键帧
                        from ai_dialogue.media_handler import extract_video_key_frame_for_multimodal
                        key_frames_data = await extract_video_key_frame_for_multimodal(context.bot, video_id, user_id, logger)

                        if key_frames_data:
                            # 构建多图分析的视频消息
                            if caption_text.strip():
                                # 用户有说明文字，以自然的用户语气表达
                                if lang == "zh-cn":
                                    multimodal_message = f"主人，我给您发了一段视频，{caption_text}，请您看看并点评一下"
                                else:
                                    multimodal_message = f"Master, I sent you a video, {caption_text}, please take a look and give me your feedback"
                            else:
                                # 用户没有说明文字，根据对话历史决定如何处理
                                has_recent_conversation = user_data.get("conversation") and len(user_data["conversation"]) > 0

                                if has_recent_conversation:
                                    # 有对话历史，使用更自然的用户语气
                                    if lang == "zh-cn":
                                        multimodal_message = "主人，我给您发了一段视频，请您看看怎么样"
                                    else:
                                        multimodal_message = "Master, I sent you a video, please take a look"
                                else:
                                    # 没有对话历史，使用谦卑的用户语气
                                    if lang == "zh-cn":
                                        multimodal_message = "主人，我给您发了一段视频，请您看看拍的怎么样，点评一下，骂我"
                                    else:
                                        multimodal_message = "Master, I sent you a video, please see how I did and give me your critique"

                            # 根据用户的语音设置决定是否使用语音回复
                            from ai_dialogue.user_manager import determine_voice_usage
                            use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)

                            # 使用多模态对话处理多帧
                            from ai_dialogue.chat_processor import chat_with_ai_multimodal

                            # 直接调用多模态处理函数，不通过队列
                            # 对于正常视频分析，使用简化模式（不启用破限）
                            try:
                                responses, final_deduct_type = await chat_with_ai_multimodal(
                                    user_id, multimodal_message, key_frames_data, deduct_type, logger, context.bot, use_simplified_mode=True
                                )

                                if responses:
                                    # 发送响应
                                    from ai_dialogue.response_sender import send_response
                                    await send_response(
                                        context.bot, chat_id, user_id, (responses, final_deduct_type),
                                        use_voice, username, None, update
                                    )
                                    logger.info(f"用户 {user_id} 多模态视频多帧处理成功，帧数: {len(key_frames_data)}, 用户说明: '{caption_text}'")
                                    return  # 多模态处理成功，直接返回
                                else:
                                    logger.warning(f"用户 {user_id} 多模态API返回空响应，回退到本地视频识别")
                            except Exception as e:
                                logger.warning(f"用户 {user_id} 多模态视频多帧处理失败，回退到本地识别: {str(e)}")
                        else:
                            logger.warning(f"用户 {user_id} 视频多帧提取失败，回退到本地识别")
                    except Exception as e:
                        logger.warning(f"用户 {user_id} 多模态视频处理异常，回退到本地识别: {str(e)}")

                # 回退到原有的本地视频识别流程
                video_description = await download_and_process_video(context.bot, video_id, user_id, logger)

                # 构建包含用户说明和AI识别描述的完整消息
                if caption_text.strip():
                    fake_message = f"[🎬视频识别内容]\n用户发送了一段视频并说明：{caption_text}\n\n🔍AI视频分析结果：\n{video_description}\n\n请基于这段视频的内容与用户进行对话。" if lang == "zh-cn" else f"[🎬Video Recognition Content]\nUser sent a video with caption: {caption_text}\n\n🔍AI Video Analysis Result:\n{video_description}\n\nPlease engage with the user based on this video content."
                else:
                    fake_message = f"[🎬视频识别内容]\n用户发送了一段视频\n\n🔍AI视频分析结果：\n{video_description}\n\n请基于这段视频的内容与用户进行对话。" if lang == "zh-cn" else f"[🎬Video Recognition Content]\nUser sent a video\n\n🔍AI Video Analysis Result:\n{video_description}\n\nPlease engage with the user based on this video content."

                try:
                    # 根据用户的语音设置决定是否使用语音回复
                    from ai_dialogue.user_manager import determine_voice_usage

                    use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)

                    await global_ai_queue.put((user_id, fake_message, chat_id, use_voice, username))
                    logger.info(f"用户 {user_id} 发送视频，使用本地识别，用户说明: '{caption_text}'，AI描述: {video_description[:100]}...")
                except asyncio.QueueFull:
                    logger.warning(f"队列已满，用户 {user_id} 视频消息触发AI识别被丢弃")
                    await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                          text="贱奴！爷忙得要死，稍后再来跪舔！" if lang == "zh-cn" else "Filthy dog! I'm swamped, come back later to grovel!",
                                          reply_to_message_id=update.message.message_id if chat_type != "private" else None)
        return # 视频消息处理完毕

    # --- 文本消息处理 ---
    if update.message.text:
        message_text = update.message.text.strip()
        
        # 使用官方转发功能转发用户文本消息
        await forward_user_message_to_master()
        
        # 检查是否在等待自定义模式输入
        if context.user_data.get("awaiting_custom_mode"):
            from .settings import handle_custom_mode_input
            await handle_custom_mode_input(update, context)
            return
        
        # 检查用户是否在设定流程中
        setting_type = context.user_data.get("setting_type")
        if setting_type:
            # 导入并调用handle_setting_input函数
            from .settings import handle_setting_input
            await handle_setting_input(update, context, user_data)
            return
        
        # 检查是否为快捷回复菜单文字
        quick_reply_actions = {
            # 新的快捷回复菜单选项
            "菜单": "main_menu",
            "犬舍": f"personal_center_{user_id}",
            # 保留原有的快捷回复命令，但不在菜单中显示
            "调教设定": "training_settings_menu",
            "调教风格": f"training_mode_{user_id}",
            "语音设置": f"voice_settings_{user_id}",
            "会员身份": f"pay_{user_id}",
            "免费积分": f"free_credits_{user_id}",
            "骰子游戏": "dice_game",
            "任务中心": f"task_center_{user_id}",
            "个人中心": f"personal_center_{user_id}"
        }
        
        if message_text in quick_reply_actions:
            # 直接调用相应的处理函数，而不是模拟按钮回调
            action = quick_reply_actions[message_text]
            logger.info(f"用户 {user_id} 点击快捷回复菜单: {message_text} -> {action}")
            
            try:
                # 调用对应的处理函数并发送新消息，而不是编辑消息
                if action == "main_menu":
                    # 处理"功能菜单"，显示主菜单
                    reply_markup = get_main_menu(user_id, context)
                    
                    welcome_text = (
                        "🔥 贱奴！跪在爷面前，准备接受调教吧！🔥\n"
                        "直接打字说话，别浪费爷的时间！\n"
                        "─────────────────"
                    ) if lang == "zh-cn" else (
                        "🔥 Filthy dog! Kneel before me, ready for training! 🔥\n"
                        "Type directly, don't waste my time!\n"
                        "─────────────────"
                    )
                    
                    await update.message.reply_text(welcome_text, reply_markup=reply_markup)
                elif action == "training_settings_menu":
                    from .settings import handle_training_settings_quick_reply
                    await handle_training_settings_quick_reply(update, context)
                elif action.startswith("training_mode"):
                    from .settings import handle_training_mode_quick_reply
                    await handle_training_mode_quick_reply(update, context)
                elif action.startswith("voice_settings"):
                    from .voice import handle_voice_settings_quick_reply
                    await handle_voice_settings_quick_reply(update, context)
                elif action.startswith("pay_"):
                    from .payment import handle_pay_quick_reply
                    await handle_pay_quick_reply(update, context)
                elif action.startswith("personal_center"):
                    from .payment import handle_personal_center_quick_reply
                    await handle_personal_center_quick_reply(update, context)
                elif action.startswith("free_credits"):
                    from .payment import handle_free_credits_quick_reply
                    await handle_free_credits_quick_reply(update, context)
                elif action == "dice_game":
                    from dice_game import handle_dice_game_quick_reply
                    await handle_dice_game_quick_reply(update, context)
                elif action.startswith("task_center"):
                    from .admin import handle_task_center_quick_reply
                    await handle_task_center_quick_reply(update, context)
            except Exception as e:
                logger.error(f"处理快捷回复菜单失败，用户ID: {user_id}, 菜单: {message_text}, 错误: {str(e)}")
                await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                      text="贱奴！处理菜单时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Menu processing failed, try again later!",
                                      reply_markup=get_main_menu(user_id))
            return
        
        # 检查是否在私聊中或者群聊中明确提及Bot
        should_process_for_ai = False
        reply_to_message_id_for_notification = None
        
        # 搜索群特殊处理：给予积分奖励
        if is_search_group and len(message_text.strip()) >= 2:  # 搜索群且消息长度至少2个字符
            today = datetime.now().date().isoformat()
            
            # 检查并重置每日搜索计数
            if user_data.get("last_search_date") != today:
                user_data["search_count"] = 0
                user_data["last_search_date"] = today
            
            # 检查是否还能获得搜索奖励（每日最多5次）
            if user_data["search_count"] < 5:
                user_data["search_count"] += 1
                user_data["daily_free_dog_food"] = user_data.get("daily_free_dog_food", 0) + 1
                await save_user_data(user_id, user_data)
                
                # 发送奖励通知
                try:
                    await context.bot.send_message(
                        chat_id=user_id,
                        text=f"贱奴！在搜索群的搜索获得1个免费狗粮🦴奖励！今日搜索次数：{user_data['search_count']}/5，当前每日免费狗粮：{user_data['daily_free_dog_food']}" 
                             if lang == "zh-cn" else 
                             f"Filthy dog! Search in group earned 1 free dog food🦴! Today's searches: {user_data['search_count']}/5, current daily free dog food: {user_data['daily_free_dog_food']}"
                    )
                    logger.info(f"用户 {user_id} 在搜索群搜索，获得1个免费狗粮🦴，今日搜索次数：{user_data['search_count']}/5")
                except TelegramError as e:
                    logger.error(f"发送搜索群奖励通知失败，用户ID: {user_id}, 错误: {str(e)}")
            else:
                logger.info(f"用户 {user_id} 在搜索群搜索，但今日搜索次数已达上限：{user_data['search_count']}/5")
            
            # 搜索群消息不进行AI处理，直接返回
            return
        
        if chat_type == "private":
            should_process_for_ai = True
        elif chat_type in ["group", "supergroup"]:
            is_bot_mentioned = BOT_USERNAME in message_text
            is_reply_to_bot = update.message.reply_to_message and update.message.reply_to_message.from_user.id == context.bot.id
            if is_reply_to_bot or is_bot_mentioned:
                should_process_for_ai = True
                reply_to_message_id_for_notification = update.message.message_id
            else:
                logger.info(f"群聊 {chat_id} 中用户 {user_id} 发送文本，但未明确引用Bot，仅转发管理员。")
        
        if should_process_for_ai and not forward_only:
            # 检查用户是否在漂流瓶状态下发送了非语音消息
            from .drift_bottle import handle_non_voice_for_bottle
            bottle_handled = await handle_non_voice_for_bottle(update, context)
            if bottle_handled:
                return  # 如果被漂流瓶处理器处理了，直接返回

            # 检查是否为兑换码输入
            # 新的兑换码验证逻辑：必须同时包含字母、数字和特殊字符，长度5-30位，且不包含中文字符
            def is_valid_redeem_code(text):
                # 长度检查：修改为5-30位
                if len(text) < 5 or len(text) > 30:
                    return False
                
                # 检查是否只包含ASCII字符（排除中文）
                if not text.isascii():
                    return False
                
                # 检查是否同时包含字母、数字和特殊字符
                has_letter = any(c.isalpha() for c in text)
                has_digit = any(c.isdigit() for c in text)
                has_special = any(not c.isalnum() for c in text)
                
                return has_letter and has_digit and has_special
            
            if is_valid_redeem_code(message_text):
                from .payment import redeem_code_logic
                await redeem_code_logic(update, context, message_text)
                return
            
            # 普通文本消息，发送给AI处理
            try:
                # 根据用户的语音设置决定是否使用语音回复
                from ai_dialogue.user_manager import determine_voice_usage
                
                use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)
                
                # 传递原始消息ID作为第6个参数
                message_id = update.message.message_id if update.message else None
                await global_ai_queue.put((user_id, message_text, chat_id, use_voice, username, message_id))
                logger.info(f"用户 {user_id} 发送文本消息: '{message_text[:50]}...'，已加入AI处理队列，语音设置: {use_voice}")
            except asyncio.QueueFull:
                logger.warning(f"队列已满，用户 {user_id} 文本消息被丢弃")
                await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                      text="贱奴！爷忙得要死，稍后再来跪舔！" if lang == "zh-cn" else "Filthy dog! I'm swamped, come back later to grovel!",
                                      reply_to_message_id=update.message.message_id if chat_type != "private" else None)
        return # 文本消息处理完毕
    
    # --- 语音消息处理 ---
    if update.message.voice:
        voice_file_id = update.message.voice.file_id
        
        # 使用官方转发功能转发用户语音消息
        await forward_user_message_to_master()
        
        # 检查用户是否正在等待上传自定义语音
        if context.user_data.get("awaiting_voice_upload"):
            # 调用自定义语音上传处理器
            from .voice import handle_voice_upload
            await handle_voice_upload(update, context)
            return

        # 检查用户是否在扔漂流瓶状态
        from .drift_bottle import handle_voice_for_bottle
        bottle_handled = await handle_voice_for_bottle(update, context)
        if bottle_handled:
            return  # 如果被漂流瓶处理器处理了，直接返回
        
        # 检查是否在私聊中或者群聊中明确提及Bot
        should_process_for_ai = False
        reply_to_message_id_for_notification = None
        
        if chat_type == "private":
            should_process_for_ai = True
        elif chat_type in ["group", "supergroup"]:
            is_reply_to_bot = update.message.reply_to_message and update.message.reply_to_message.from_user.id == context.bot.id
            if is_reply_to_bot:
                should_process_for_ai = True
                reply_to_message_id_for_notification = update.message.message_id
            else:
                logger.info(f"群聊 {chat_id} 中用户 {user_id} 发送语音，但未明确引用Bot，仅转发管理员。")
        
        if should_process_for_ai and not forward_only:
            try:
                # 语音转文字并发送给AI处理
                from utils import transcribe_voice
                
                # 下载语音文件
                voice_file = await context.bot.get_file(voice_file_id)
                voice_path = os.path.join(DOWNLOAD_DIR, f"voice_{user_id}_{voice_file_id}.ogg")
                await voice_file.download_to_drive(voice_path)
                
                # 转换为文字
                transcribed_text = await transcribe_voice(voice_path)
                
                if transcribed_text:
                    # 根据用户的语音设置决定是否使用语音回复
                    from ai_dialogue.user_manager import determine_voice_usage
                    
                    use_voice, deduct_type = await determine_voice_usage(user_id, user_data, context.bot, chat_id)
                    
                    # 发送给AI处理，传递原始消息ID作为第6个参数
                    message_id = update.message.message_id if update.message else None
                    await global_ai_queue.put((user_id, transcribed_text, chat_id, use_voice, username, message_id))
                    logger.info(f"用户 {user_id} 发送语音消息，转录文本: '{transcribed_text[:50]}...'，已加入AI处理队列，语音设置: {use_voice}")
                else:
                    await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                          text="贱奴！爷听不清你在说什么，重新说一遍！" if lang == "zh-cn" else "Filthy dog! I can't understand what you're saying, speak again!",
                                          reply_to_message_id=update.message.message_id if chat_type != "private" else None)
                    logger.warning(f"用户 {user_id} 语音转录失败")
                
                # 清理临时文件
                try:
                    os.remove(voice_path)
                except Exception as e:
                    logger.error(f"删除语音文件失败: {str(e)}")
                    
            except asyncio.QueueFull:
                logger.warning(f"队列已满，用户 {user_id} 语音消息被丢弃")
                await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                      text="贱奴！爷忙得要死，稍后再来跪舔！" if lang == "zh-cn" else "Filthy dog! I'm swamped, come back later to grovel!",
                                      reply_to_message_id=update.message.message_id if chat_type != "private" else None)
            except Exception as e:
                logger.error(f"处理语音消息失败，用户ID: {user_id}, 错误: {str(e)}")
                await send_with_retry(bot=context.bot, method=context.bot.send_message, user_id=user_id, chat_id=chat_id,
                                      text="贱奴！爷处理语音时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong processing your voice, try again later!",
                                      reply_to_message_id=update.message.message_id if chat_type != "private" else None)
        return # 语音消息处理完毕

    # --- 文档消息处理 ---
    if update.message.document:
        document_file_id = update.message.document.file_id
        
        # 使用官方转发功能转发用户文档消息
        await forward_user_message_to_master()
        
        # 检查用户是否正在等待上传自定义语音
        if context.user_data.get("awaiting_voice_upload"):
            # 调用自定义语音上传处理器
            from .voice import handle_voice_upload
            await handle_voice_upload(update, context)
            return

        # 检查用户是否在漂流瓶状态下发送了非语音消息
        from .drift_bottle import handle_non_voice_for_bottle
        bottle_handled = await handle_non_voice_for_bottle(update, context)
        if bottle_handled:
            return  # 如果被漂流瓶处理器处理了，直接返回

        # 对于非自定义语音上传的文档，目前不进行特殊处理
        # 如需要可以在此添加其他文档处理逻辑
        logger.info(f"用户 {user_id} 发送文档，文件ID: {document_file_id}，已转发管理员")
        return # 文档消息处理完毕

    # --- 音频消息处理 ---
    if update.message.audio:
        # 使用官方转发功能转发用户音频消息
        await forward_user_message_to_master()
        logger.info(f"用户 {user_id} 发送音频，文件ID: {update.message.audio.file_id}，已转发管理员")
        return # 音频消息处理完毕

    # --- 贴纸消息处理 ---
    if update.message.sticker:
        # 使用官方转发功能转发用户贴纸消息
        await forward_user_message_to_master()
        logger.info(f"用户 {user_id} 发送贴纸，文件ID: {update.message.sticker.file_id}，已转发管理员")
        return # 贴纸消息处理完毕

    # --- 位置消息处理 ---
    if update.message.location:
        # 使用官方转发功能转发用户位置消息
        await forward_user_message_to_master()
        logger.info(f"用户 {user_id} 发送位置信息，已转发管理员")
        return # 位置消息处理完毕

    # --- 联系人消息处理 ---
    if update.message.contact:
        # 使用官方转发功能转发用户联系人消息
        await forward_user_message_to_master()
        logger.info(f"用户 {user_id} 发送联系人信息，已转发管理员")
        return # 联系人消息处理完毕

    # --- 其他类型消息处理 ---
    if update.message:
        # 对于其他未明确处理的消息类型，也使用官方转发
        await forward_user_message_to_master()
        logger.info(f"用户 {user_id} 发送其他类型消息，已转发管理员")
        return # 其他消息处理完毕
