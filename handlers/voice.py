import os
import time
import asyncio
import shutil
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError, BadRequest
from config import (
    BOT_USERNAME, DOWNLOAD_DIR, TTS_CONFIG, DEFAULT_VOICE
)
from utils import get_main_menu, get_voice_settings_menu, text_to_speech, transcribe_voice
from logging_config import logger
from database import get_user_data, save_user_data


async def handle_voice_settings(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    menu_text, reply_markup = await get_voice_settings_menu(user_id)
    try:
        await query.edit_message_text(
            menu_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    except TelegramError as e:
        logger.error(f"发送语音设置菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.message.reply_text(
            "贱奴！爷弄语音设置菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the voice settings menu, try again later!",
            reply_markup=reply_markup
        )


async def handle_prev_voice_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    logger.debug(f"用户 {user_id} 点击上一页，处理开始")
    
    try:
        current_page = user_data.get("voice_settings_page", 0)
        logger.debug(f"用户 {user_id} 当前页码: {current_page}")
        
        if current_page <= 0:
            await query.answer("贱奴！已经在第一页了，别乱翻！" if lang == "zh-cn" else "Filthy dog! Already on the first page, stop flipping!")
            logger.debug(f"用户 {user_id} 尝试翻到上一页，已在第一页，页码: {current_page}")
            return
        
        new_page = current_page - 1
        user_data["voice_settings_page"] = new_page
        logger.debug(f"用户 {user_id} 更新页码为: {new_page}")
        await save_user_data(user_id, user_data)
        
        menu_text, reply_markup = await get_voice_settings_menu(user_id, new_page)
        await query.edit_message_text(menu_text, reply_markup=reply_markup, parse_mode="Markdown")
        logger.info(f"用户 {user_id} 翻到语音设置上一页，页码: {new_page}")
    
    except BadRequest as e:
        logger.error(f"翻到语音设置上一页失败，用户ID: {user_id}, 回调查询无效，错误: {str(e)}")
        await query.answer("贱奴！翻页超时，重新打开菜单试试！" if lang == "zh-cn" else "Filthy dog! Page flip timed out, reopen the menu!")
    except TelegramError as e:
        logger.error(f"翻到语音设置上一页失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text("贱奴！翻页时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong flipping pages, try again later!")
    except Exception as e:
        logger.error(f"翻到语音设置上一页意外错误，用户ID: {user_id}, 错误类型: {type(e).__name__}, 错误: {str(e)}", exc_info=True)
        await query.answer("贱奴！翻页出错了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Page flip errored, try again later!")


async def handle_next_voice_page(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    logger.debug(f"用户 {user_id} 点击下一页，处理开始")
    
    try:
        current_page = user_data.get("voice_settings_page", 0)
        logger.debug(f"用户 {user_id} 当前页码: {current_page}")
        
        # 获取所有可用的语音列表，与 get_voice_settings_menu 保持一致
        voices = []
        for svc in ["fishaudio", "local_tts"]:
            if TTS_CONFIG.get(svc, {}).get("enabled", False):
                voices.extend(TTS_CONFIG[svc].get("voices", []))
        
        # 检查用户是否有自定义语音
        custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
        if os.path.exists(custom_voice_path):
            custom_voice_id = f"custom_{user_id}"
            if not any(v.get("reference_id") == custom_voice_id for v in voices):
                voices.append({
                    "name": "自定义声音" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Custom Voice",
                    "reference_id": custom_voice_id,
                    "reference_audio": custom_voice_path,
                    "service": "local_tts"
                })
        
        voices_per_page = 8
        total_pages = max(1, (len(voices) + voices_per_page - 1) // voices_per_page)  # 确保至少1页
        logger.debug(f"用户 {user_id} 总页数: {total_pages}, 语音总数: {len(voices)}")
        
        if current_page >= total_pages - 1:
            await query.answer("贱奴！已经在最后一页了，别乱翻！" if lang == "zh-cn" else "Filthy dog! Already on the last page, stop flipping!")
            logger.debug(f"用户 {user_id} 尝试翻到下一页，已在最后一页，页码: {current_page}")
            # 确保页码显示与提示一致，重新渲染当前页
            menu_text, reply_markup = await get_voice_settings_menu(user_id, current_page)
            await query.edit_message_text(menu_text, reply_markup=reply_markup, parse_mode="Markdown")
            return
        
        new_page = current_page + 1
        user_data["voice_settings_page"] = new_page
        logger.debug(f"用户 {user_id} 更新页码为: {new_page}")
        await save_user_data(user_id, user_data)
        
        menu_text, reply_markup = await get_voice_settings_menu(user_id, new_page)
        await query.edit_message_text(menu_text, reply_markup=reply_markup, parse_mode="Markdown")
        logger.info(f"用户 {user_id} 翻到语音设置下一页，页码: {new_page}")
    
    except BadRequest as e:
        logger.error(f"翻到语音设置下一页失败，用户ID: {user_id}, 回调查询无效，错误: {str(e)}")
        await query.answer("贱奴！翻页超时，重新打开菜单试试！" if lang == "zh-cn" else "Filthy dog! Page flip timed out, reopen the menu!")
    except TelegramError as e:
        logger.error(f"翻到语音设置下一页失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text("贱奴！翻页时抽风了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Something went wrong flipping pages, try again later!")
    except Exception as e:
        logger.error(f"翻到语音设置下一页意外错误，用户ID: {user_id}, 错误类型: {type(e).__name__}, 错误: {str(e)}", exc_info=True)
        await query.answer("贱奴！翻页出错了，稍后再试！" if lang == "zh-cn" else "Filthy dog! Page flip errored, try again later!")


async def handle_choose_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    data = query.data
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    # Correctly extract selected_voice from callback data
    if data.startswith("choose_voice_"):
        # 移除前缀 'choose_voice_'
        voice_data_str = data[len("choose_voice_"):]
        # 找到最后一个下划线，其后面是 user_id
        last_underscore_index = voice_data_str.rfind("_")
        if last_underscore_index != -1:
            # reference_id 是从开始到最后一个下划线之前的部分
            selected_voice = voice_data_str[:last_underscore_index]
            # user_id 部分也可以从这里提取，虽然函数开头已经提取了
            # user_id_from_data = voice_data_str[last_underscore_index + 1:]
        else:
            # 如果没有下划线，说明是像 'text' 这样的情况
            selected_voice = voice_data_str
    else:
        logger.error(f"Unexpected callback data format for voice selection: {data}")
        await query.answer("贱奴！无效的语音选择操作！" if lang == "zh-cn" else "Filthy dog! Invalid voice selection operation!")
        return
    logger.info(f"用户 {user_id} 选择语音模型: {selected_voice}")
    if user_data["selected_voice"] == selected_voice:
        try:
            await query.answer("贱奴！你已经选了这个方式，别他妈重复点！" if lang == "zh-cn" else "Filthy dog! You already chose this method, stop clicking!")
        except TelegramError as e:
            logger.error(f"发送重复选择提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    # 检查所选语音模型是否属于启用的服务
    voice_service = None
    voice_found = False
    
    # 首先检查是否是自定义语音
    if selected_voice.startswith("custom_"):
        custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
        if os.path.exists(custom_voice_path):
            voice_service = "local_tts"
            voice_found = True
            logger.info(f"用户 {user_id} 选择自定义语音模型 {selected_voice}，文件路径: {custom_voice_path}")
        else:
            logger.warning(f"用户 {user_id} 选择的自定义语音文件不存在: {custom_voice_path}")
    
    # 如果不是自定义语音或自定义语音文件不存在，检查配置中的语音
    if not voice_found:
        for svc in ["fishaudio", "local_tts"]:
            if TTS_CONFIG.get(svc, {}).get("enabled", False):
                for v in TTS_CONFIG[svc].get("voices", []):
                    if v.get("reference_id") == selected_voice:
                        voice_service = svc
                        voice_found = True
                        logger.info(f"用户 {user_id} 选择语音模型 {selected_voice}，属于服务 {svc}")
                        break
            if voice_found:
                break
    
    # 如果用户选择的语音模型不可用，回退到默认语音模型
    if not voice_found and selected_voice != "text":
        try:
            await query.answer("贱奴！所选语音模型不可用，已切换到默认语音模型！" if lang == "zh-cn" else "Filthy dog! Selected voice model unavailable, switched to default voice model!")
            selected_voice = DEFAULT_VOICE
            # 再次检查默认语音模型是否可用
            voice_found = False
            for svc in ["fishaudio", "local_tts"]:
                if TTS_CONFIG.get(svc, {}).get("enabled", False):
                    for v in TTS_CONFIG[svc].get("voices", []):
                        if v.get("reference_id") == selected_voice:
                            voice_service = svc
                            voice_found = True
                            logger.info(f"用户 {user_id} 回退到默认语音模型 {selected_voice}，属于服务 {svc}")
                            break
                if voice_found:
                    break
            if not voice_found:
                logger.warning(f"用户 {user_id} 选择的默认语音模型 {selected_voice} 也不可用，无法切换")
                selected_voice = "text"
        except TelegramError as e:
            logger.error(f"发送不可用语音模型提示失败，用户ID: {user_id}, 错误: {str(e)}")
    
    # 允许任何人切换语音模型，不再检查积分限制
    # 积分检查将在对话时进行，如果没有积分会自动切换为文字模式
    user_data["selected_voice"] = selected_voice
    await save_user_data(user_id, user_data)
    
    # 重新生成并发送更新后的语音设置菜单
    try:
        menu_text, reply_markup = await get_voice_settings_menu(user_id)
        await query.edit_message_text(
            f"{'贱奴！羞辱方式已选好！' if lang == 'zh-cn' else 'Filthy dog! Humiliation method set!'}\n{menu_text}",
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 切换语音模型为 {selected_voice}")
    except TelegramError as e:
        if "Message is not modified" in str(e):
            logger.warning(f"消息内容未变化，用户ID: {user_id}，尝试强制更新菜单")
            try:
                # 强制更新菜单，重新获取当前页码
                current_page = user_data.get("voice_settings_page", 0)
                menu_text, reply_markup = await get_voice_settings_menu(user_id, current_page)
                await query.edit_message_text(
                    f"{'贱奴！羞辱方式已选好！' if lang == 'zh-cn' else 'Filthy dog! Humiliation method set!'}\n{menu_text}",
                    reply_markup=reply_markup,
                    parse_mode="Markdown"
                )
                logger.info(f"用户 {user_id} 强制更新语音设置菜单成功")
            except TelegramError as e2:
                logger.error(f"强制更新语音设置菜单失败，用户ID: {user_id}，错误: {str(e2)}")
        else:
            logger.error(f"发送语音选择确认失败，用户ID: {user_id}，错误: {str(e)}")


async def set_default_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if str(user_id) != MASTER_ID:
        try:
            await update.message.reply_text(
                "贱奴！你没资格用这个命令，滚！"
                if lang == "zh-cn" else
                "Filthy dog! You don't have permission for this command, get lost!"
            )
        except TelegramError as e:
            logger.error(f"发送权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    global DEFAULT_VOICE
    
    if len(context.args) != 1:
        voices_list = "\n".join([f"{v['name']} - {v['reference_id']}" for v in TTS_CONFIG["fishaudio"]["voices"]])
        try:
            await update.message.reply_text(
                f"用法：/setdefaultvoice <reference_id>\n可用语音模型：\n{voices_list}\n当前默认模型：{DEFAULT_VOICE}"
                if lang == "zh-cn" else
                f"Usage: /setdefaultvoice <reference_id>\nAvailable voice models:\n{voices_list}\nCurrent default model: {DEFAULT_VOICE}"
            )
        except TelegramError as e:
            logger.error(f"发送语音列表失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    new_voice_id = context.args[0]
    valid_voices = [v["reference_id"] for v in TTS_CONFIG["fishaudio"]["voices"]]
    if new_voice_id not in valid_voices:
        try:
            await update.message.reply_text(
                f"贱奴！{new_voice_id} 不是有效的语音模型ID，滚去检查！"
                if lang == "zh-cn" else
                f"Filthy dog! {new_voice_id} is not a valid voice model ID, go check!"
            )
        except TelegramError as e:
            logger.error(f"发送无效语音提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    DEFAULT_VOICE = new_voice_id
    try:
        await update.message.reply_text(
            f"爷已将默认语音模型切换为 {new_voice_id}！"
            if lang == "zh-cn" else
            f"I've switched the default voice model to {new_voice_id}!",
            reply_markup=get_main_menu(user_id)
        )
        logger.info(f"管理员 {user_id} 将默认语音模型切换为 {new_voice_id}")
    except TelegramError as e:
        logger.error(f"发送切换语音提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_custom_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        await query.edit_message_text(
            "想自定义你的主人玩儿你的声音么？\n"
            "你可以上传不大于500kb的语音文件给我，或者是直接发送一条不大于500kb的语音（大概一句话就够了）。\n"
            "自定义的声音，只有你在有付费语音积分的时候才能用！"
            if lang == "zh-cn" else
            "Want to customize the voice of your master playing with you?\n"
            "You can upload an audio file no larger than 500kb to me, or directly send a voice message no larger than 500kb (about one sentence is enough).\n"
            "Custom voices can only be used when you have paid voice credits!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("提交声音" if lang == "zh-cn" else "Submit Voice", callback_data=f"submit_voice_{user_id}"),
                 InlineKeyboardButton("试听声音" if lang == "zh-cn" else "Test Voice", callback_data=f"test_voice_{user_id}")],
                [InlineKeyboardButton("删除声音" if lang == "zh-cn" else "Delete Voice", callback_data=f"delete_voice_{user_id}")],
                [InlineKeyboardButton("返回语音设置" if lang == "zh-cn" else "Back to Voice Settings", callback_data=f"voice_settings_{user_id}")]
            ])
        )
    except TelegramError as e:
        logger.error(f"发送自定义声音菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.message.reply_text(
            "贱奴！爷弄自定义声音菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the custom voice menu, try again later!"
        )


async def handle_submit_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    context.user_data["awaiting_voice_upload"] = True
    try:
        await query.edit_message_text(
            "贱奴！请上传不大于500kb的语音文件，或者直接录制一条不大于500kb的语音（大概一句话就够了）。"
            if lang == "zh-cn" else
            "Filthy dog! Please upload an audio file no larger than 500kb, or directly record a voice message no larger than 500kb (about one sentence is enough).",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data=f"custom_voice_{user_id}")]
            ])
        )
    except TelegramError as e:
        logger.error(f"发送提交声音提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_test_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    test_audio_path = os.path.join(DOWNLOAD_DIR, f"test_voice_{user_id}.wav")
    
    if os.path.exists(custom_voice_path):
        # 每次试听时都重新生成测试音频，以确保使用最新的自定义语音
        try:
            test_text = "贱狗，滚过来！跪下让爹看看你的傻逼样！" if lang == "zh-cn" else "Dog, hurry over here, kneel and call me Daddy!"
            
            # 临时保存用户当前的语音模型
            original_voice = user_data.get("selected_voice", DEFAULT_VOICE)
            
            # 临时切换到自定义语音模型进行试听
            user_data["selected_voice"] = f"custom_{user_id}"
            await save_user_data(user_id, user_data)
            
            test_audio = await text_to_speech(test_text, logger, user_id)
            
            # 恢复用户原来的语音模型设置
            user_data["selected_voice"] = original_voice
            await save_user_data(user_id, user_data)
            
            if test_audio and os.path.exists(test_audio):
                # 覆盖旧的测试音频文件
                if os.path.exists(test_audio_path):
                    os.remove(test_audio_path)
                shutil.copy(test_audio, test_audio_path)
                with open(test_audio, "rb") as audio_file:
                    await context.bot.send_voice(
                        chat_id=query.message.chat_id,
                        voice=audio_file,
                        caption=test_text
                    )
                logger.info(f"用户 {user_id} 试听自定义声音成功，生成并保存新测试音频")
                os.remove(test_audio)
            else:
                await query.edit_message_text(
                    "贱奴！生成试听声音失败，稍后再试！"
                    if lang == "zh-cn" else
                    "Filthy dog! Failed to generate test voice, try again later!",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
                    ])
                )
        except Exception as e:
            logger.error(f"生成或发送试听声音失败，用户ID: {user_id}, 错误: {str(e)}")
            # 确保即使出错也恢复用户原来的语音模型设置
            try:
                if 'original_voice' in locals():
                    user_data["selected_voice"] = original_voice
                    await save_user_data(user_id, user_data)
            except:
                pass
            await query.edit_message_text(
                "贱奴！生成试听声音失败，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Failed to generate test voice, try again later!",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
                ])
            )
    else:
        await query.edit_message_text(
            "贱奴！尚未自定义声音，请先提交声音文件！"
            if lang == "zh-cn" else
            "Filthy dog! No custom voice set, please submit a voice file first!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
            ])
        )


async def handle_delete_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    test_audio_path = os.path.join(DOWNLOAD_DIR, f"test_voice_{user_id}.wav")
    
    if os.path.exists(custom_voice_path):
        try:
            await query.edit_message_text(
                "贱奴！确认要删除自定义声音吗？此操作无法撤销！"
                if lang == "zh-cn" else
                "Filthy dog! Are you sure you want to delete the custom voice? This action cannot be undone!",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("确认删除" if lang == "zh-cn" else "Confirm Delete", callback_data=f"confirm_delete_voice_{user_id}"),
                     InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data=f"custom_voice_{user_id}")]
                ])
            )
        except TelegramError as e:
            logger.error(f"发送删除确认提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        await query.edit_message_text(
            "贱奴！尚未自定义声音，无法删除！"
            if lang == "zh-cn" else
            "Filthy dog! No custom voice set to delete!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
            ])
        )


async def handle_confirm_delete_voice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    test_audio_path = os.path.join(DOWNLOAD_DIR, f"test_voice_{user_id}.wav")
    
    try:
        if os.path.exists(custom_voice_path):
            os.remove(custom_voice_path)
        if os.path.exists(test_audio_path):
            os.remove(test_audio_path)
        await query.edit_message_text(
            "贱奴！自定义声音已删除！"
            if lang == "zh-cn" else
            "Filthy dog! Custom voice has been deleted!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
            ])
        )
        logger.info(f"用户 {user_id} 删除自定义声音文件 {custom_voice_path} 和测试音频 {test_audio_path}")
    except Exception as e:
        logger.error(f"删除自定义声音文件失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！删除自定义声音失败，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Failed to delete custom voice, try again later!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
            ])
        )


async def handle_voice_upload(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if not context.user_data.get("awaiting_voice_upload"):
        return
    
    voice = update.message.voice
    document = update.message.document
    file = voice if voice else document
    file_size = file.file_size if file else 0
    
    if not file:
        await update.message.reply_text(
            "贱奴！请上传有效的语音文件或录制语音！"
            if lang == "zh-cn" else
            "Filthy dog! Please upload a valid audio file or record a voice message!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data=f"custom_voice_{user_id}")]
            ])
        )
        return
    
    if file_size > 500 * 1024:  # 500kb
        await update.message.reply_text(
            f"贱奴！文件大小超过500kb，当前大小：{file_size/1024:.2f}kb，请上传更小的文件！"
            if lang == "zh-cn" else
            f"Filthy dog! File size exceeds 500kb, current size: {file_size/1024:.2f}kb, please upload a smaller file!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data=f"custom_voice_{user_id}")]
            ])
        )
        logger.info(f"用户 {user_id} 上传文件大小超限，大小：{file_size/1024:.2f}kb")
        return
    
    file_obj = await file.get_file()
    temp_file_path = os.path.join(DOWNLOAD_DIR, f"temp_voice_{user_id}.mp3")
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    await file_obj.download_to_drive(temp_file_path)
    
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    os.makedirs("reference_audios", exist_ok=True)
    # 覆盖旧的声音文件
    if os.path.exists(custom_voice_path):
        os.remove(custom_voice_path)
        logger.info(f"用户 {user_id} 覆盖旧的自定义声音文件 {custom_voice_path}")
    shutil.copy(temp_file_path, custom_voice_path)
    
    # 更新 TTS_CONFIG 中的自定义声音路径
    found = False
    for voice in TTS_CONFIG["local_tts"]["voices"]:
        if voice["name"] == "自定义声音" and voice["reference_id"].startswith("custom_"):
            voice["reference_audio"] = custom_voice_path
            voice["reference_id"] = f"custom_{user_id}"
            found = True
            break
    if not found:
        TTS_CONFIG["local_tts"]["voices"].append({
            "name": "自定义声音",
            "reference_id": f"custom_{user_id}",
            "reference_audio": custom_voice_path,
            "service": "local_tts"
        })
    
    # 自动切换用户语音模型为自定义语音
    user_data["selected_voice"] = f"custom_{user_id}"
    await save_user_data(user_id, user_data)
    
    context.user_data["awaiting_voice_upload"] = False
    os.remove(temp_file_path)
    
    await update.message.reply_text(
        "贱奴！自定义声音已提交成功，已自动切换为你的自定义语音！"
        if lang == "zh-cn" else
        "Filthy dog! Custom voice submitted successfully and automatically switched to your custom voice!",
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("返回自定义声音" if lang == "zh-cn" else "Back to Custom Voice", callback_data=f"custom_voice_{user_id}")]
        ])
    )
    logger.info(f"用户 {user_id} 成功上传自定义声音文件到 {custom_voice_path}，已自动切换语音模型")


async def handle_choose_language(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    new_lang = "en" if lang == "zh-cn" else "zh-cn"
    user_data["preferred_language"] = new_lang
    await save_user_data(user_id, user_data)
    try:
        menu_text, reply_markup = await get_voice_settings_menu(user_id)
        await query.edit_message_text(
            f"{'贱奴！语言已切换为' if lang == 'zh-cn' else 'Filthy dog! Language switched to'} {'中文' if new_lang == 'zh-cn' else 'English'}！\n{menu_text}",
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 切换语言为 {new_lang}")
    except TelegramError as e:
        logger.error(f"发送语言切换提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_set_delay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    context.user_data["setting_delay"] = True
    try:
        await query.edit_message_text(
            "贱奴！输入回复延迟（秒，0.5-5.0）："
            if lang == "zh-cn" else
            "Filthy dog! Enter response delay (seconds, 0.5-5.0):",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="back_to_main")]])
        )
    except TelegramError as e:
        logger.error(f"发送延迟设置提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_set_sentences(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    context.user_data["setting_sentences"] = True
    try:
        await query.edit_message_text(
            "贱奴！输入最大句数（1-10）："
            if lang == "zh-cn" else
            "Filthy dog! Enter maximum sentences (1-10):",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="back_to_main")]])
        )
    except TelegramError as e:
        logger.error(f"发送句数设置提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_voice_settings_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的语音设置 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查用户是否有自定义声音
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    if os.path.exists(custom_voice_path):
        custom_voice_id = f"custom_{user_id}"
        # 获取所有可用的语音列表
        voices = []
        for svc in ["fishaudio", "local_tts"]:
            if TTS_CONFIG.get(svc, {}).get("enabled", False):
                voices.extend(TTS_CONFIG[svc].get("voices", []))
        
        # 添加自定义语音
        if not any(v.get("reference_id") == custom_voice_id for v in voices):
            voices.append({
                "name": "自定义声音" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Custom Voice",
                "reference_id": custom_voice_id,
                "reference_audio": custom_voice_path,
                "service": "local_tts"
            })
        logger.info(f"用户 {user_id} 的自定义声音已添加到语音列表，ID: {custom_voice_id}, 文件路径: {custom_voice_path}")

    menu_text, reply_markup = await get_voice_settings_menu(user_id)
    try:
        await update.message.reply_text(
            menu_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
        logger.info(f"用户 {user_id} 通过快捷回复访问语音设置菜单")
    except TelegramError as e:
        logger.error(f"发送语音设置菜单失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！爷弄语音设置菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the voice settings menu, try again later!",
            reply_markup=get_main_menu(user_id)
        )
