import time
import asyncio
import json
import os
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, LabeledPrice
from telegram.ext import ContextTypes
from telegram.error import TelegramError
from config import (
    MASTER_ID, ENABLE_TELEGRAM_PAYMENTS, TEXT_CODES_FILE, VOICE_CODES_FILE,
    SETTINGS_CODES_FILE, TEXT_50_CODES_FILE, VOICE_20_CODES_FILE, VOICE_50_CODES_FILE,
    REDEEM_CODE_PURCHASE_URL, CH<PERSON>NEL_LINK, SEARCH_GROUP_LINK, PAYMENT_QR_FILE_ID,
    MEMBERSHIP_30_DAYS_CODES_FILE, MEMBERSHIP_365_DAYS_CODES_FILE, MEMBERSHIP_PERMANENT_CODES_FILE,
    MEMBERSHIP_PRICES, redis_client, BOT_USERNAME
)
from utils import get_main_menu, get_redeem_code_menu, get_free_credits_menu, get_search_group_invite_link, send_with_retry
from logging_config import logger
from database import get_user_data, save_user_data, get_user_number


async def handle_pay_membership(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    data_parts = query.data.split("_")
    payment_type = data_parts[1]  # membership
    membership_days = data_parts[2]  # 30, 365, 或 permanent
    user_id = int(data_parts[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if not ENABLE_TELEGRAM_PAYMENTS:
        await query.edit_message_text(
            "贱奴！你爹我没开 Telegram Stars 支付，别在这浪费时间！"
            if lang == "zh-cn" else
            "Filthy dog! I haven't enabled Telegram Stars payments, stop wasting time!"
        )
        return
    keyboard = [
        [InlineKeyboardButton("确认支付" if lang == "zh-cn" else "Confirm Payment", callback_data=f"confirm_pay_{payment_type}_{membership_days}_{user_id}")],
        [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_payment")]
    ]
    # 定义支付金额（Stars）和会员类型
    if membership_days == "30":
        star_amount = MEMBERSHIP_PRICES["30_days"]  # 300 Stars
        membership_desc = "1个月会员" if lang == "zh-cn" else "1 Month Membership"
    elif membership_days == "365":
        star_amount = MEMBERSHIP_PRICES["365_days"]  # 2000 Stars
        membership_desc = "1年会员" if lang == "zh-cn" else "1 Year Membership"
    elif membership_days == "permanent":
        star_amount = MEMBERSHIP_PRICES["permanent"]  # 2500 Stars
        membership_desc = "永久会员" if lang == "zh-cn" else "Permanent Membership"
    
    # 存储实际支付金额和会员类型到 context.user_data 和数据库，供后续验证使用
    context.user_data["payment_amount_stars"] = star_amount
    context.user_data["payment_membership_days"] = membership_days
    context.user_data["payment_type"] = payment_type
    
    # 同时保存到数据库的 user_data 中
    user_data["pending_payment"] = {"type": payment_type, "membership_days": membership_days}
    await save_user_data(user_id, user_data)
    logger.debug(f"用户 {user_id} 保存会员支付订单信息到数据库: {user_data['pending_payment']}")
    
    # 也保存到 context.user_data（备用）
    context.user_data["pending_payment"] = {"type": payment_type, "membership_days": membership_days}
    
    amount = f"{star_amount} Stars - {membership_desc}"
    try:
        await query.edit_message_text(
            f"贱奴！{amount}，确认支付："
            if lang == "zh-cn" else
            f"Filthy dog! {amount}, confirm payment:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        logger.info(f"用户 {user_id} 进入支付确认，类型: {payment_type}, 会员天数: {membership_days}, 金额: {star_amount} Stars")
    except TelegramError as e:
        logger.error(f"发送支付确认菜单失败，用户ID: {user_id}, 错误: {str(e)}")


async def pay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /pay 命令，显示支付菜单"""
    from handlers.base import get_pay_menu
    user_id = update.effective_user.id
    logger.info(f"收到 /pay 命令，用户ID: {user_id}")
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        reply_markup, hint = await get_pay_menu(user_id)
        await send_with_retry(
            bot=context.bot,
            method=context.bot.send_message,
            user_id=user_id,
            chat_id=update.effective_chat.id,
            text=(
                "🔥 贱奴！选择支付方式给爷进贡！��\n"
                "💎 **会员特权**：会员期间对话不消耗任何积分，无限次数调教！\n"
                "───────────────────\n"
                f"{hint}"
                if lang == "zh-cn" else
                "🔥 Filthy dog! Choose a payment method to tribute me! 🔥\n"
                "💎 **Membership Benefits**: No credits consumed during membership, unlimited training sessions!\n"
                "───────────────────\n"
                f"{hint}"
            ),
            reply_markup=reply_markup
        )
        logger.info(f"发送支付菜单成功，用户ID: {user_id}, 菜单内容: {hint}")
    except TelegramError as e:
        logger.error(f"发送支付菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await send_with_retry(
            bot=context.bot,
            method=context.bot.send_message,
            user_id=user_id,
            chat_id=update.effective_chat.id,
            text=(
                "贱奴！支付菜单发送失败，稍后再试或联系 @master_kabot！"
                if lang == "zh-cn" else
                "Filthy dog! Failed to send payment menu, try again later or contact @master_kabot!"
            )
        )


async def handle_payment_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    data = query.data
    logger.info(f"处理按钮回调，用户ID: {user_id}, 数据: {data}")
    
    if data.startswith("pay_membership_"):
        _, _, membership_days, user_id_str = data.split("_")
        user_id = int(user_id_str)
        payment_type = "membership"
        
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        if membership_days == "30":
            title = "1个月会员" if lang == "zh-cn" else "1 Month Membership"
            description = "购买1个月会员资格" if lang == "zh-cn" else "Purchase 1 month membership"
            price = MEMBERSHIP_PRICES["30_days"]
        elif membership_days == "365":
            title = "1年会员" if lang == "zh-cn" else "1 Year Membership"  
            description = "购买1年会员资格" if lang == "zh-cn" else "Purchase 1 year membership"
            price = MEMBERSHIP_PRICES["365_days"]
        elif membership_days == "permanent":
            title = "永久会员" if lang == "zh-cn" else "Permanent Membership"
            description = "购买永久会员资格" if lang == "zh-cn" else "Purchase permanent membership"
            price = MEMBERSHIP_PRICES["permanent"]
        
        try:
            # 保存订单信息到数据库
            user_data["pending_payment"] = {"type": payment_type, "membership_days": membership_days}
            await save_user_data(user_id, user_data)
            logger.debug(f"用户 {user_id} 保存订单信息到数据库: {user_data['pending_payment']}")
            
            # 保存到 context.user_data（备用）
            context.user_data["pending_payment"] = {"type": payment_type, "membership_days": membership_days}
            
            invoice = await context.bot.send_invoice(
                chat_id=user_id,
                title=title,
                description=description,
                payload=f"membership_{membership_days}_{user_id}",
                provider_token="",  # 使用 Telegram Stars，无需 provider_token
                currency="XTR",
                prices=[LabeledPrice(title, price * 1)],  # Telegram Stars 使用 100 倍价格
                need_name=False,
                need_phone_number=False,
                need_email=False,
                need_shipping_address=False,
                is_flexible=False
            )
            logger.info(f"发送支付账单，用户ID: {user_id}, 类型: {payment_type}, 会员天数: {membership_days}, 价格: {price} XTR")
            
            # 确认账单发送
            keyboard = [
                [InlineKeyboardButton("确认支付" if lang == "zh-cn" else "Confirm Payment", callback_data=f"confirm_pay_{payment_type}_{membership_days}_{user_id}")],
                [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_payment")]
            ]
            await query.edit_message_text(
                f"{'贱奴！已发送支付账单，请检查并完成支付！' if lang == 'zh-cn' else 'Filthy dog! Payment invoice sent, check and complete it!'}",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送支付账单失败，用户ID: {user_id}, 错误: {str(e)}")
            user_data["pending_payment"] = None
            await save_user_data(user_id, user_data)
            context.user_data.pop("pending_payment", None)
            await query.edit_message_text(
                "贱奴！发送账单时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Something went wrong sending the invoice, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data.startswith("confirm_pay_"):
        _, _, payment_type, membership_days, user_id_str = data.split("_")
        user_id = int(user_id_str)
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        # 验证订单信息
        order_info = user_data.get("pending_payment") or context.user_data.get("pending_payment")
        if order_info and order_info["type"] == payment_type and order_info["membership_days"] == membership_days:
            try:
                # 根据会员类型设置发票信息
                if membership_days == "30":
                    title = "1个月会员" if lang == "zh-cn" else "1 Month Membership"
                    description = "购买1个月会员资格" if lang == "zh-cn" else "Purchase 1 month membership"
                    price = MEMBERSHIP_PRICES["30_days"]
                elif membership_days == "365":
                    title = "1年会员" if lang == "zh-cn" else "1 Year Membership"  
                    description = "购买1年会员资格" if lang == "zh-cn" else "Purchase 1 year membership"
                    price = MEMBERSHIP_PRICES["365_days"]
                elif membership_days == "permanent":
                    title = "永久会员" if lang == "zh-cn" else "Permanent Membership"
                    description = "购买永久会员资格" if lang == "zh-cn" else "Purchase permanent membership"
                    price = MEMBERSHIP_PRICES["permanent"]
                
                # 发送发票
                invoice = await context.bot.send_invoice(
                    chat_id=user_id,
                    title=title,
                    description=description,
                    payload=f"membership_{membership_days}_{user_id}",
                    provider_token="",  # 使用 Telegram Stars，无需 provider_token
                    currency="XTR",
                    prices=[LabeledPrice(title, price * 1)],  # Telegram Stars 使用 1 倍价格
                    need_name=False,
                    need_phone_number=False,
                    need_email=False,
                    need_shipping_address=False,
                    is_flexible=False
                )
                logger.info(f"发送支付账单，用户ID: {user_id}, 类型: {payment_type}, 会员天数: {membership_days}, 价格: {price} XTR")
                
                # 更新消息显示发票已发送
                await query.edit_message_text(
                    f"{'贱奴！已发送支付账单，请检查并完成支付！' if lang == 'zh-cn' else 'Filthy dog! Payment invoice sent, check and complete it!'}",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_payment")]])
                )
            except TelegramError as e:
                logger.error(f"发送支付账单失败，用户ID: {user_id}, 错误: {str(e)}")
                # 清理订单信息
                user_data["pending_payment"] = None
                await save_user_data(user_id, user_data)
                context.user_data.pop("pending_payment", None)
                await query.edit_message_text(
                    "贱奴！发送账单时抽风了，稍后再试！"
                    if lang == "zh-cn" else
                    "Filthy dog! Something went wrong sending the invoice, try again later!",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
                )
        else:
            logger.warning(f"确认支付失败，用户ID: {user_id}, 订单信息无效: {order_info}")
            user_data["pending_payment"] = None
            await save_user_data(user_id, user_data)
            context.user_data.pop("pending_payment", None)
            await query.edit_message_text(
                "贱奴！支付信息无效，滚去重新操作！"
                if lang == "zh-cn" else
                "Filthy dog! Invalid payment info, go try again!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data.startswith("pay_edit_"):
        _, _, setting_type, user_id_str = data.split("_")
        user_id = int(user_id_str)
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        try:
            # 保存订单信息到数据库
            user_data["pending_payment"] = {"type": "settings", "amount": 1}
            await save_user_data(user_id, user_data)
            logger.debug(f"用户 {user_id} 保存设置修改订单信息到数据库: {user_data['pending_payment']}")
            
            context.user_data["pending_payment"] = {"type": "settings", "amount": 1}
            
            invoice = await context.bot.send_invoice(
                chat_id=user_id,
                title="调教设定修改" if lang == "zh-cn" else "Training Settings Edit",
                description="购买1次调教设定修改机会" if lang == "zh-cn" else "Purchase 1 training settings edit",
                payload=f"settings_1_{user_id}",
                provider_token="",
                currency="XTR",
                prices=[LabeledPrice("调教设定修改" if lang == "zh-cn" else "Training Settings Edit", 50 * 1)],
                need_name=False,
                need_phone_number=False,
                need_email=False,
                need_shipping_address=False,
                is_flexible=False
            )
            logger.info(f"发送支付账单，用户ID: {user_id}, 类型: settings, 数量: 1, 价格: 50 XTR")
            
            keyboard = [
                [InlineKeyboardButton("确认支付" if lang == "zh-cn" else "Confirm Payment", callback_data=f"confirm_pay_edit_{setting_type}_{user_id}")],
                [InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_payment")]
            ]
            await query.edit_message_text(
                f"{'贱奴！已发送支付账单，请检查并完成支付！' if lang == 'zh-cn' else 'Filthy dog! Payment invoice sent, check and complete it!'}",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except TelegramError as e:
            logger.error(f"发送设置修改账单失败，用户ID: {user_id}, 错误: {str(e)}")
            user_data["pending_payment"] = None
            await save_user_data(user_id, user_data)
            context.user_data.pop("pending_payment", None)
            await query.edit_message_text(
                "贱奴！发送账单时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Something went wrong sending the invoice, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data.startswith("confirm_pay_edit_"):
        _, _, setting_type, user_id_str = data.split("_")
        user_id = int(user_id_str)
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        order_info = user_data.get("pending_payment") or context.user_data.get("pending_payment")
        if order_info and order_info["type"] == "settings" and order_info["amount"] == 1:
            try:
                await query.edit_message_text(
                    f"{'贱奴！请在 Telegram 内完成支付！' if lang == 'zh-cn' else 'Filthy dog! Complete the payment in Telegram!'}",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("取消" if lang == "zh-cn" else "Cancel", callback_data="cancel_payment")]])
                )
            except TelegramError as e:
                logger.error(f"更新设置修改确认支付消息失败，用户ID: {user_id}, 错误: {str(e)}")
        else:
            logger.warning(f"确认设置修改支付失败，用户ID: {user_id}, 订单信息无效: {order_info}")
            user_data["pending_payment"] = None
            await save_user_data(user_id, user_data)
            context.user_data.pop("pending_payment", None)
            await query.edit_message_text(
                "贱奴！支付信息无效，滚去重新操作！"
                if lang == "zh-cn" else
                "Filthy dog! Invalid payment info, go try again!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data.startswith("redeem_code_"):
        user_id = int(data.split("_")[2])
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        context.user_data["awaiting_redeem_code"] = True
        
        try:
            reply_markup, hint = await get_redeem_code_menu(user_id)
            await query.edit_message_text(
                hint,
                reply_markup=reply_markup
            )
            logger.info(f"用户 {user_id} 进入兑换码输入模式")
        except TelegramError as e:
            logger.error(f"发送兑换码菜单失败，用户ID: {user_id}, 错误: {str(e)}")
            context.user_data.pop("awaiting_redeem_code", None)
            await query.edit_message_text(
                "贱奴！兑换码菜单抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Redeem code menu failed, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data.startswith("go_to_pay_"):
        from handlers.base import get_pay_menu
        user_id = int(data.split("_")[3])
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        try:
            reply_markup, hint = await get_pay_menu(user_id)
            await query.edit_message_text(
                hint,
                reply_markup=reply_markup
            )
            logger.info(f"用户 {user_id} 返回支付菜单")
        except TelegramError as e:
            logger.error(f"发送支付菜单失败，用户ID: {user_id}, 错误: {str(e)}")
            await query.edit_message_text(
                "贱奴！支付菜单抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Payment menu failed, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
    
    elif data == "cancel_payment":
        from handlers.base import get_pay_menu
        user_id = query.from_user.id
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        try:
            user_data["pending_payment"] = None
            await save_user_data(user_id, user_data)
            context.user_data.pop("pending_payment", None)
            reply_markup, hint = await get_pay_menu(user_id)
            await query.edit_message_text(
                f"{'贱奴！支付已取消！' if lang == 'zh-cn' else 'Filthy dog! Payment canceled!'}\n{hint}",
                reply_markup=reply_markup
            )
            logger.info(f"用户 {user_id} 取消支付")
        except TelegramError as e:
            logger.error(f"取消支付失败，用户ID: {user_id}, 错误: {str(e)}")
            await query.edit_message_text(
                "贱奴！取消支付时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Something went wrong canceling payment, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )


async def redeem_code(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from handlers.base import get_pay_menu
    user_id = update.effective_user.id
    logger.info(f"收到 /redeem 命令，用户ID: {user_id}")
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    context.user_data["awaiting_redeem_code"] = True
    logger.debug(f"用户 {user_id} 设置 awaiting_redeem_code 状态为 True")
    
    reply_markup, hint = await get_redeem_code_menu(user_id)
    try:
        await send_with_retry(
            bot=context.bot,
            method=context.bot.send_message,
            user_id=user_id,
            chat_id=update.effective_chat.id,
            text=hint,
            reply_markup=reply_markup
        )
        logger.info(f"用户 {user_id} 收到兑换码输入提示")
    except TelegramError as e:
        logger.error(f"发送兑换码输入提示失败，用户ID: {user_id}, 错误: {str(e)}")
        await send_with_retry(
            bot=context.bot,
            method=context.bot.send_message,
            user_id=user_id,
            chat_id=update.effective_chat.id,
            text="贱奴！爷弄兑换码菜单时抽风了，稍后再试！"
                 if lang == "zh-cn" else
                 "Filthy dog! My system choked on the redeem code menu, try again later!"
        )


async def redeem_code_logic(update: Update, context: ContextTypes.DEFAULT_TYPE, code: str):
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    # 确保包含所有兑换码文件，包括会员兑换码文件
    code_files = {
        TEXT_CODES_FILE: ("text_100", 100, "remaining_count", "获得100次文字调教次数"),
        VOICE_CODES_FILE: ("voice_100", 100, "voice_count", "获得100次语音调教次数"),
        SETTINGS_CODES_FILE: ("settings", 1, "paid_change_count", "获得1次调教设定修改机会"),
        TEXT_50_CODES_FILE: ("text_50", 50, "remaining_count", "获得50次文字调教次数"),
        VOICE_20_CODES_FILE: ("voice_20", 20, "voice_count", "获得20次语音调教次数"),
        VOICE_50_CODES_FILE: ("voice_50", 50, "voice_count", "获得50次语音调教次数"),
        MEMBERSHIP_30_DAYS_CODES_FILE: ("membership_30_days", 30, None, "获得30天会员资格"),
        MEMBERSHIP_365_DAYS_CODES_FILE: ("membership_365_days", 365, None, "获得365天会员资格"),
        MEMBERSHIP_PERMANENT_CODES_FILE: ("membership_permanent", None, None, "获得永久会员资格")
    }
    logger.info(f"用户 {user_id} 尝试兑换码 {code}，检查文件列表: {list(code_files.keys())}")
    redeemed = False
    for file_path, (reward_type, amount, field, reward_text) in code_files.items():
        try:
            if not os.path.exists(file_path):
                logger.warning(f"兑换码文件 {file_path} 不存在")
                continue
                
            logger.debug(f"检查兑换码文件: {file_path}")
            with open(file_path, "r+") as f:
                lines = f.readlines()
                codes = [line.strip() for line in lines if line.strip()]
                logger.debug(f"兑换码文件 {file_path} 包含 {len(codes)} 个兑换码，内容示例: {codes[:3]}...")
                if code in codes:
                    codes.remove(code)
                    f.seek(0)
                    f.writelines([c + "\n" for c in codes])
                    f.truncate()
                    if reward_type == "text_100" or reward_type == "text_50":
                        user_data["dog_food"] += (50 if reward_type == "text_50" else 100)  # 保持原数量
                        message = f"获得{50 if reward_type == 'text_50' else 100}个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    elif reward_type == "voice_100" or reward_type == "voice_50" or reward_type == "voice_20":
                        if reward_type == "voice_20":
                            amount_food = 20
                        elif reward_type == "voice_50":
                            amount_food = 50
                        else:  # voice_100
                            amount_food = 100
                        user_data["dog_food"] += amount_food
                        message = f"获得{amount_food}个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    elif reward_type == "settings":
                        user_data["paid_change_count"] += amount
                        message = f"{reward_text}！当前剩余付费修改次数：{user_data['paid_change_count']}，狗粮数量：{user_data['dog_food']}"
                    elif reward_type == "membership_30_days":
                        current_expiry = user_data.get("membership_expiry")
                        # 简化验证逻辑，只检查是否为有效的ISO时间格式
                        if current_expiry:
                            try:
                                current_expiry_date = datetime.fromisoformat(current_expiry)
                                new_expiry = current_expiry_date + timedelta(days=30)
                            except (ValueError, TypeError) as e:
                                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                                new_expiry = datetime.now() + timedelta(days=30)
                        else:
                            new_expiry = datetime.now() + timedelta(days=30)
                        user_data["membership_expiry"] = new_expiry.isoformat()
                        user_data["dog_food"] += 20
                        user_data["voice_enabled"] = True
                        # 保存用户数据
                        await save_user_data(user_id, user_data)
                        message = f"获得30天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和20个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    elif reward_type == "membership_365_days":
                        current_expiry = user_data.get("membership_expiry")
                        # 简化验证逻辑，只检查是否为有效的ISO时间格式
                        if current_expiry:
                            try:
                                current_expiry_date = datetime.fromisoformat(current_expiry)
                                new_expiry = current_expiry_date + timedelta(days=365)
                            except (ValueError, TypeError) as e:
                                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                                new_expiry = datetime.now() + timedelta(days=365)
                        else:
                            new_expiry = datetime.now() + timedelta(days=365)
                        user_data["membership_expiry"] = new_expiry.isoformat()
                        user_data["dog_food"] += 100
                        user_data["voice_enabled"] = True
                        # 保存用户数据
                        await save_user_data(user_id, user_data)
                        message = f"获得365天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和100个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    elif reward_type == "membership_permanent":
                        user_data["membership_expiry"] = None
                        user_data["paid_change_count"] += 100
                        user_data["permanent_member"] = 1  # 使用整数1标记为永久会员
                        # 保存用户数据
                        await save_user_data(user_id, user_data)
                        logger.info(f"用户 {user_id} 通过兑换码获得永久会员身份")
                        message = f"获得永久会员资格和100次角色设定修改次数！当前狗粮数量：{user_data['dog_food']}"
                    await save_user_data(user_id, user_data)
                    redeemed = True
                    
                    try:
                        if update.message:
                            await update.message.reply_text(
                                f"贱奴！兑换码 {code} 有效，{message}"
                                if lang == "zh-cn" else
                                f"Filthy dog! Code {code} is valid, {message}"
                            )
                        else:
                            await update.callback_query.message.edit_text(
                                f"贱奴！兑换码 {code} 有效，{message}"
                                if lang == "zh-cn" else
                                f"Filthy dog! Code {code} is valid, {message}"
                            )
                        logger.info(f"用户 {user_id} 兑换码 {code} 成功，奖励: {amount} {reward_type}")
                    except TelegramError as e:
                        logger.error(f"发送兑换成功提示失败，用户ID: {user_id}, 错误: {str(e)}")
                    break
        except FileNotFoundError:
            logger.error(f"兑换码文件 {file_path} 不存在")
            continue
        except Exception as e:
            logger.error(f"处理兑换码文件 {file_path} 失败，用户ID: {user_id}, 错误: {str(e)}")
            continue
    if not redeemed:
        try:
            # 获取兑换码菜单
            reply_markup, _ = await get_redeem_code_menu(user_id)
            
            if update.message:
                await update.message.reply_text(
                    "贱奴！这兑换码是假的，滚去买真的！"
                    if lang == "zh-cn" else
                    "Filthy dog! This code is invalid, go buy a real one!",
                    reply_markup=reply_markup
                )
            else:
                await update.callback_query.message.edit_text(
                    "贱奴！这兑换码是假的，滚去买真的！"
                    if lang == "zh-cn" else
                    "Filthy dog! This code is invalid, go buy a real one!",
                    reply_markup=reply_markup
                )
            logger.info(f"用户 {user_id} 尝试兑换无效码: {code}")
        except TelegramError as e:
            logger.error(f"发送无效兑换码提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def pre_checkout_query(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理预支付查询，暂存到 Redis"""
    query = update.pre_checkout_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    logger.info(f"收到 pre_checkout_query，查询ID: {query.id}，用户ID: {user_id}")
    
    # 验证支付金额和类型 - 修改为支持会员制度
    payload = query.invoice_payload
    if not payload.startswith("membership_"):
        await query.answer(ok=False, error_message=(
            "无效的支付类型！" if lang == "zh-cn" else "Invalid payment type!"
        ))
        return
    
    # 暂存 pending 支付到 Redis，设置 30 分钟超时
    pending_key = f"payment:pending:{user_id}"
    payment_data = {
        "query_id": query.id,
        "payload": payload,
        "timestamp": datetime.now().isoformat(),
        "amount": query.total_amount
    }
    await redis_client.setex(pending_key, 1800, json.dumps(payment_data))
    logger.info(f"用户 {user_id} 的支付状态已暂存到 Redis，查询ID: {query.id}")
    
    try:
        await query.answer(ok=True)
    except TelegramError as e:
        logger.error(f"回答 pre_checkout_query 失败，用户ID: {user_id}, 错误: {str(e)}")
        await redis_client.delete(pending_key)
        await query.answer(ok=False, error_message=(
            "支付验证失败，请稍后再试！"
            if lang == "zh-cn" else
            "Payment verification failed, try again later!"
        ))


async def successful_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理成功支付，更新用户积分，从 Redis 删除 pending 状态"""
    user_id = update.effective_user.id
    payment = update.message.successful_payment
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    logger.info(f"收到 successful_payment，支付ID: {payment.telegram_payment_charge_id}，用户ID: {user_id}")
    
    pending_key = f"payment:pending:{user_id}"
    pending_data = await redis_client.get(pending_key)
    if not pending_data:
        logger.warning(f"用户 {user_id} 无对应的 pending 支付记录，支付ID: {payment.telegram_payment_charge_id}")
        return
    
    try:
        pending_payment = json.loads(pending_data)
        if pending_payment["payload"] == payment.invoice_payload:
            if pending_payment["payload"].startswith("membership_"):
                membership_type = "_".join(pending_payment["payload"].split("_")[1:-1])  # 从payload中提取会员类型，排除最后的user_id
                if membership_type == "30":
                    membership_type = "30_days"
                elif membership_type == "365":
                    membership_type = "365_days"
                
                if membership_type == "30_days":
                    current_expiry = user_data.get("membership_expiry")
                    # 简化验证逻辑，只检查是否为有效的ISO时间格式
                    if current_expiry:
                        try:
                            current_expiry_date = datetime.fromisoformat(current_expiry)
                            new_expiry = current_expiry_date + timedelta(days=30)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                            new_expiry = datetime.now() + timedelta(days=30)
                    else:
                        new_expiry = datetime.now() + timedelta(days=30)
                    user_data["membership_expiry"] = new_expiry.isoformat()
                    user_data["dog_food"] += 20
                    user_data["voice_enabled"] = True
                    message = f"获得30天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和20次语音调教次数！当前剩余文字次数：{user_data['remaining_count']}，语音次数：{user_data['voice_count']}"
                elif membership_type == "365_days":
                    current_expiry = user_data.get("membership_expiry")
                    # 简化验证逻辑，只检查是否为有效的ISO时间格式
                    if current_expiry:
                        try:
                            current_expiry_date = datetime.fromisoformat(current_expiry)
                            new_expiry = current_expiry_date + timedelta(days=365)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                            new_expiry = datetime.now() + timedelta(days=365)
                    else:
                        new_expiry = datetime.now() + timedelta(days=365)
                    user_data["membership_expiry"] = new_expiry.isoformat()
                    user_data["dog_food"] += 50
                    user_data["voice_enabled"] = True
                    message = f"获得365天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和100次语音调教次数！当前剩余文字次数：{user_data['remaining_count']}，语音次数：{user_data['voice_count']}"
                elif membership_type == "permanent":
                    user_data["membership_expiry"] = None
                    user_data["paid_change_count"] += 100
                    user_data["permanent_member"] = 1  # 使用整数1标记为永久会员
                    logger.info(f"用户 {user_id} 通过支付获得永久会员身份")
                    message = f"获得永久会员资格和100次角色设定修改次数！当前狗粮数量：{user_data['dog_food']}"
                
                user_data["processed_payments"].append({
                    "charge_id": payment.telegram_payment_charge_id,
                    "payload": payment.invoice_payload,
                    "amount": payment.total_amount,
                    "timestamp": datetime.now().isoformat()
                })
                user_data["pending_payment"] = None
                await save_user_data(user_id, user_data)
                await redis_client.delete(pending_key)
                logger.info(f"用户 {user_id} 支付成功，更新积分，删除 Redis pending 状态")
                
                try:
                    await update.message.reply_text(message, reply_markup=get_main_menu(user_id))
                except TelegramError as e:
                    logger.error(f"发送支付成功提示失败，用户ID: {user_id}, 错误: {str(e)}")
            else:
                logger.warning(f"用户 {user_id} 的支付 payload 不匹配，pending: {pending_payment['payload']}, 实际: {payment.invoice_payload}")
    except Exception as e:
        logger.error(f"处理成功支付失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)


async def handle_edit_payment(update: Update, context: ContextTypes.DEFAULT_TYPE, user_data, payload):
    user_id = update.effective_user.id
    setting_type = "_".join(payload[1:-2])
    lang = user_data.get("preferred_language", "zh-cn")
    user_data["paid_change_count"] += 1
    await save_user_data(user_id, user_data)
    free_change_key = f"free_{setting_type.split('_')[0]}_change"
    try:
        await update.message.reply_text(
            f"贱奴！支付 {update.message.successful_payment.total_amount} XTR 到手，获得1次{setting_type.replace('_', ' ')}修改机会！当前剩余付费修改次数：{user_data['paid_change_count']}"
            if lang == "zh-cn" else
            f"Filthy dog! Paid {update.message.successful_payment.total_amount} XTR, got 1 {setting_type.replace('_', ' ')} edit! Current paid edits: {user_data['paid_change_count']}"
        )
        await context.bot.send_message(
            chat_id=update.effective_chat.id,
            text=(
                f"贱狗！修改你的{setting_type.replace('_', ' ')}！\n剩余免费修改次数：{user_data[free_change_key]}次\n剩余付费修改次数：{user_data['paid_change_count']}次\n按以下格式回复（每项一行，空行表示跳过）：\n"
                + (
                    "姓名：\n年龄：\n职业：\n穿着：\n身高（厘米）：\n体重（千克）：\n爱好：\n鸡巴软长度（厘米）：\n鸡巴勃起长（厘米）：\n鸡巴勃起粗（厘米）：\n脚码："
                    if setting_type == "master_settings" else
                    "调教地点：\n环境描述：\n相关人员：\n其他："
                    if setting_type == "scene_settings" else
                    "喜欢的玩法：\n可接受的玩法：\n额外玩法：\n犬名：\n年龄："
                )
                if lang == "zh-cn" else
                f"Filthy dog! Edit your {setting_type.replace('_', ' ')}!\nRemaining free edits: {user_data[free_change_key]}\nRemaining paid edits: {user_data['paid_change_count']}\nReply in the following format (one per line, leave blank to skip):\n"
                + (
                    "Name:\nAge:\nOccupation:\nClothing:\nHeight (cm):\nWeight (kg):\nHobbies:\nPenis soft length (cm):\nPenis erect length (cm):\nPenis thickness (cm):\nShoe size:"
                    if setting_type == "master_settings" else
                    "Training location:\nEnvironment description:\nRelated people:\nOther:"
                    if setting_type == "scene_settings" else
                    "Favorite practices:\nAcceptable practices:\nExtra practices:\nDog name:\nAge:"
                )
            ),
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
        )
        context.user_data["setting_type"] = setting_type
        context.user_data["is_edit"] = True
        asyncio.create_task(remind_user(update.effective_chat.id, context))
    except TelegramError as e:
        logger.error(f"发送支付编辑提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def remind_user(chat_id, context):
    user_data = await get_user_data(context._user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    await asyncio.sleep(300)
    if "setting_type" in context.user_data:
        try:
            await context.bot.send_message(
                chat_id,
                "贱奴！你还没输入新的设定，别让爷等太久！快按格式回复！"
                if lang == "zh-cn" else
                "Filthy dog! You haven't entered the new settings yet, don't keep me waiting! Reply in the format!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
        except TelegramError as e:
            logger.error(f"发送提醒失败，聊天ID: {chat_id}, 错误: {str(e)}")


async def freecredits(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    logger.info(f"收到 /freecredits 命令，用户ID: {user_id}")
    try:
        reply_markup, hint = await get_free_credits_menu(user_id)
        await update.message.reply_text(hint, reply_markup=reply_markup)
    except TelegramError as e:
        logger.error(f"发送免费积分菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！爷弄积分菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the credits menu, try again later!"
        )


async def handle_free_credits(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    reply_markup, hint = await get_free_credits_menu(user_id)
    try:
        await query.edit_message_text(hint, reply_markup=reply_markup)
    except TelegramError as e:
        logger.error(f"发送免费积分菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！爷弄积分菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the credits menu, try again later!"
        )


async def handle_follow_channel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    today = datetime.now().date().isoformat()
    
    if user_data["channel_joined"] == 0:
        user_data["channel_joined"] = 1
        user_data["daily_free_dog_food"] = user_data.get("daily_free_dog_food", 0) + 5
        user_data["last_channel_visit"] = today
        await save_user_data(user_id, user_data)
        try:
            await query.edit_message_text(
                f"贱奴！首次关注频道，赏你5个免费狗粮🦴！当前每日免费狗粮：{user_data['daily_free_dog_food']}\n请点击下方链接加入频道，并点击置顶消息获取额外2个免费狗粮🦴！\n{CHANNEL_LINK}"
                if lang == "zh-cn" else
                f"Filthy dog! First channel follow, you get 5 free dog food🦴! Current daily free dog food: {user_data['daily_free_dog_food']}\nJoin the channel and click the pinned message for 2 extra free dog food🦴!\n{CHANNEL_LINK}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("验证关注" if lang == "zh-cn" else "Verify Follow", callback_data=f"verify_channel_{user_id}")],
                    [InlineKeyboardButton("点击置顶消息" if lang == "zh-cn" else "Click Pinned Message", callback_data=f"channel_pinned_click_{user_id}")],
                    [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
                ])
            )
            logger.info(f"用户 {user_id} 首次关注频道，获得5个免费狗粮🦴")
        except TelegramError as e:
            logger.error(f"发送首次关注提示失败，用户ID: {user_id}, 错误: {str(e)}")
    elif user_data["last_channel_visit"] != today:
        user_data["daily_free_dog_food"] = user_data.get("daily_free_dog_food", 0) + 2
        user_data["last_channel_visit"] = today
        await save_user_data(user_id, user_data)
        try:
            await query.edit_message_text(
                f"贱奴！今日访问频道，赏你2个免费狗粮🦴！当前每日免费狗粮：{user_data['daily_free_dog_food']}\n请点击下方链接加入频道，并点击置顶消息获取额外2个免费狗粮🦴！\n{CHANNEL_LINK}"
                if lang == "zh-cn" else
                f"Filthy dog! Today's channel visit, you get 2 free dog food🦴! Current daily free dog food: {user_data['daily_free_dog_food']}\nJoin the channel and click the pinned message for 2 extra free dog food🦴!\n{CHANNEL_LINK}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("验证关注" if lang == "zh-cn" else "Verify Follow", callback_data=f"verify_channel_{user_id}")],
                    [InlineKeyboardButton("点击置顶消息" if lang == "zh-cn" else "Click Pinned Message", callback_data=f"channel_pinned_click_{user_id}")],
                    [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
                ])
            )
            logger.info(f"用户 {user_id} 今日访问频道，获得2个免费狗粮🦴")
        except TelegramError as e:
            logger.error(f"发送每日访问提示失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        try:
            await query.edit_message_text(
                f"贱奴！你今天已经拿过频道奖励了，点击置顶消息获取额外2个免费狗粮🦴！当前每日免费狗粮：{user_data['daily_free_dog_food']}\n{CHANNEL_LINK}"
                if lang == "zh-cn" else
                f"Filthy dog! You've already claimed today's channel reward, click the pinned message for 2 extra free dog food🦴! Current daily free dog food: {user_data['daily_free_dog_food']}\n{CHANNEL_LINK}",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("点击置顶消息" if lang == "zh-cn" else "Click Pinned Message", callback_data=f"channel_pinned_click_{user_id}")],
                    [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
                ])
            )
            logger.info(f"用户 {user_id} 今日已领取频道奖励，提示点击置顶消息")
        except TelegramError as e:
            logger.error(f"发送重复领取提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_verify_channel(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    try:
        chat_member = await context.bot.get_chat_member(CHANNEL_LINK.split("/")[-1], user_id)
        if chat_member.status in ["member", "administrator", "creator"]:
            try:
                await query.edit_message_text(
                    "贱奴！已验证你加入了频道，奖励已发放！"
                    if lang == "zh-cn" else
                    "Filthy dog! Verified you joined the channel, rewards granted!",
                    reply_markup=get_main_menu(user_id)
                )
                logger.info(f"用户 {user_id} 验证加入频道成功")
            except TelegramError as e:
                logger.error(f"发送验证成功提示失败，用户ID: {user_id}, 错误: {str(e)}")
        else:
            try:
                await query.edit_message_text(
                    f"贱奴！你还没加入频道，滚去加入！\n{CHANNEL_LINK}"
                    if lang == "zh-cn" else
                    f"Filthy dog! You haven't joined the channel, go join!\n{CHANNEL_LINK}",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("验证关注" if lang == "zh-cn" else "Verify Follow", callback_data=f"verify_channel_{user_id}")],
                        [InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]
                    ])
                )
                logger.info(f"用户 {user_id} 未加入频道")
            except TelegramError as e:
                logger.error(f"发送未加入提示失败，用户ID: {user_id}, 错误: {str(e)}")
    except TelegramError as e:
        logger.error(f"验证频道成员失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.edit_message_text(
                "贱奴！验证频道时抽风了，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Channel verification failed, try again later!",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
        except TelegramError as e2:
            logger.error(f"发送验证失败提示失败，用户ID: {user_id}, 错误: {str(e2)}")


async def handle_channel_pinned_click(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    today = datetime.now().date().isoformat()
    
    if user_data["last_channel_visit"] != today:
        user_data["last_channel_visit"] = today
        user_data["daily_free_dog_food"] = user_data.get("daily_free_dog_food", 0) + 2
        await save_user_data(user_id, user_data)
        try:
            await query.edit_message_text(
                f"贱奴！首次点击频道置顶消息，赏你2个免费狗粮🦴！当前每日免费狗粮：{user_data['daily_free_dog_food']}"
                if lang == "zh-cn" else
                f"Filthy dog! First click on channel pinned message, granted 2 free dog food🦴! Current daily free dog food: {user_data['daily_free_dog_food']}",
                reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
            )
            logger.info(f"用户 {user_id} 首次点击频道置顶消息，获得2个免费狗粮🦴")
        except TelegramError as e:
            logger.error(f"发送置顶消息积分通知失败，用户ID: {user_id}, 错误: {str(e)}")
    else:
        try:
            await query.answer(
                "贱奴！你今天已经拿过置顶消息奖励了，明天再来！"
                if lang == "zh-cn" else
                "Filthy dog! You've already claimed today's pinned message reward, come back tomorrow!"
            )
            logger.info(f"用户 {user_id} 今日已领取置顶消息奖励，拒绝重复领取")
        except TelegramError as e:
            logger.error(f"发送重复领取提示失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_search_group(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    # 从最后一个部分获取 user_id，兼容 search_group_ 格式
    user_id_str = query.data.split("_")[-1]
    user_id = int(user_id_str)
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    today = datetime.now().date().isoformat()
    
    if user_data["last_search_date"] != today:
        user_data["search_count"] = 0
        user_data["last_search_date"] = today
        await save_user_data(user_id, user_data)
    
    # 提示用户加入搜索群并搜索以获得积分
    try:
        await query.edit_message_text(
            f"贱奴！请加入搜索群并发送消息进行搜索，每次搜索可获得1个免费狗粮🦴，每日最多5次！当前每日免费狗粮：{user_data.get('daily_free_dog_food', 0)}\n今日已搜索次数：{user_data['search_count']}/5\n请点击下方链接加入搜索群：\n{SEARCH_GROUP_LINK}"
            if lang == "zh-cn" else
            f"Filthy dog! Join the search group and send a message to search. Each search grants 1 free dog food🦴, up to 5 per day! Current daily free dog food: {user_data.get('daily_free_dog_food', 0)}\nSearches today: {user_data['search_count']}/5\nJoin the search group: {SEARCH_GROUP_LINK}",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回免费积分菜单" if lang == "zh-cn" else "Back to Free Credits Menu", callback_data=f"free_credits_{user_id}")]
            ])
        )
        logger.info(f"用户 {user_id} 被提示加入搜索群以获得狗粮积分")
    except TelegramError as e:
        logger.error(f"发送搜索群提示失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！显示搜索群信息时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong showing search group info, try again later!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回免费积分菜单" if lang == "zh-cn" else "Back to Free Credits Menu", callback_data=f"free_credits_{user_id}")]
            ])
        )


async def handle_search_group_invite(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理搜索群拉新邀请链接的显示"""
    query = update.callback_query
    user_id = int(query.data.split("_")[-1])  # 从最后一个部分获取 user_id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    search_invite_link = get_search_group_invite_link(user_id)
    try:
        await query.edit_message_text(
            f"贱奴！将以下链接分享给他人，每拉1人加入搜索群并进行有效搜索，赏你1个狗粮🦴！每拉5人，额外赏2个狗粮🦴！\n你的邀请链接：{search_invite_link}"
            if lang == "zh-cn" else
            f"Filthy dog! Share this link with others, get 1 dog food🦴 for each person who joins the search group and performs a valid search! Every 5 people, get an extra 2 dog food🦴!\nYour invite link: {search_invite_link}",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回免费积分菜单" if lang == "zh-cn" else "Back to Free Credits Menu", callback_data=f"free_credits_{user_id}")]
            ])
        )
        logger.info(f"用户 {user_id} 查看搜索群拉新邀请链接")
    except TelegramError as e:
        logger.error(f"发送搜索群拉新链接失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！显示邀请链接时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong showing the invite link, try again later!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("返回免费积分菜单" if lang == "zh-cn" else "Back to Free Credits Menu", callback_data=f"free_credits_{user_id}")]
            ])
        )


async def handle_personal_center(update: Update, context: ContextTypes.DEFAULT_TYPE):
    from handlers.base import get_pay_menu
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 获取犬名 - 从training_preferences中获取
    dog_name = user_data.get("training_preferences", {}).get("dog_name", "")
    if not dog_name or not dog_name.strip():
        dog_name = "未设置" if lang == "zh-cn" else "Not set"
    
    # 获取编号
    user_number = await get_user_number(user_id)
    
    # 获取推广次数
    share_count = user_data["daily_new_shares"] + user_data["daily_old_shares"]

    # 检查会员状态
    membership_status = "非会员" if lang == "zh-cn" else "Non-Member"
    membership_expiry = user_data.get("membership_expiry")
    is_member = False
    
    # 首先检查是否为永久会员
    if user_data.get("permanent_member", False):
        membership_status = "永久会员" if lang == "zh-cn" else "Permanent Member"
        is_member = True
    elif membership_expiry:
        # 先检查是否为JSON对象形式
        is_json_object = False
        try:
            if isinstance(membership_expiry, str) and membership_expiry.strip():
                # 检查是否以花括号开头结尾（JSON对象格式）
                if membership_expiry.strip().startswith("{") and membership_expiry.strip().endswith("}"):
                    is_json_object = True
        except:
            pass
        
        if is_json_object:
            # 检测到是JSON对象格式，记录日志并重置
            logger.warning(f"用户 {user_id} 的 membership_expiry 字段是JSON对象: {membership_expiry}，重置为 None")
            user_data["membership_expiry"] = None
            await save_user_data(user_id, user_data)
        else:
            # 尝试作为日期处理
            try:
                expiry_date = datetime.fromisoformat(membership_expiry)
                if datetime.now() < expiry_date:
                    membership_status = f"会员（到期：{membership_expiry[:10]}）" if lang == "zh-cn" else f"Member (Expires: {membership_expiry[:10]})"
                    is_member = True
                else:
                    membership_status = f"会员已过期（到期：{membership_expiry[:10]}）" if lang == "zh-cn" else f"Membership Expired (Expired: {membership_expiry[:10]})"
            except (ValueError, TypeError) as e:
                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {membership_expiry}，错误: {str(e)}，重置为 None")
                user_data["membership_expiry"] = None
                await save_user_data(user_id, user_data)

    # 获取狗粮信息
    dog_food_count = user_data.get('dog_food', 0)
    daily_free_count = user_data.get('daily_free_dog_food', 0)
    
    # 根据会员状态显示狗粮信息
    if is_member:
        dog_food_display = f"{dog_food_count} 🦴（会员对话不消耗🦴）" if lang == "zh-cn" else f"{dog_food_count} 🦴 (Member: no consumption🦴)"
        free_dog_food_display = f"{daily_free_count} 🦴（会员对话不消耗🦴）" if lang == "zh-cn" else f"{daily_free_count} 🦴 (Member: no consumption🦴)"
    else:
        dog_food_display = f"{dog_food_count} 🦴" if lang == "zh-cn" else f"{dog_food_count} 🦴"
        free_dog_food_display = f"{daily_free_count} 🦴" if lang == "zh-cn" else f"{daily_free_count} 🦴"
    
    message = (
        "🐕 **贱犬档案** 🐕\n"
        "───────────────────\n"
        f"犬名：{dog_name}\n"
        f"编号：{user_number}\n"
        f"会员状态：{membership_status}\n"
        f"狗粮余额：{dog_food_display}\n"
        f"免费狗粮：{free_dog_food_display}\n"
        f"推广次数：{share_count} 只\n"
        "───────────────────\n"
        "🔥 想多挨骂？选个方式！"
        if lang == "zh-cn" else
        "🐕 **Filthy Dog Profile** 🐕\n"
        "───────────────────\n"
        f"Dog name: {dog_name}\n"
        f"ID: {user_number}\n"
        f"Membership: {membership_status}\n"
        f"Dog Food: {dog_food_display}\n"
        f"Free Dog Food: {free_dog_food_display}\n"
        f"Referrals: {share_count} dogs\n"
        "───────────────────\n"
        "🔥 Want more humiliation? Pick a way!"
    )
    keyboard = [
        [InlineKeyboardButton("📢 分享赚次数" if lang == "zh-cn" else "📢 Share to Earn", callback_data=f"share_bot_{user_id}")],
        [InlineKeyboardButton("💸 滚去支付" if lang == "zh-cn" else "💸 Go Pay", callback_data=f"go_to_pay_{user_id}")],
        [InlineKeyboardButton("🏃 滚回去" if lang == "zh-cn" else "🏃 Get Lost", callback_data="back_to_main")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    try:
        await query.edit_message_text(message, reply_markup=reply_markup, parse_mode="Markdown")
        logger.info(f"用户 {user_id} 进入个人中心")
    except TelegramError as e:
        logger.error(f"发送个人中心信息失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.message.reply_text(
            "贱奴！爷弄个人中心时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on your profile, try again later!",
            reply_markup=reply_markup
        )


async def handle_share_bot(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    bot_username = BOT_USERNAME.lstrip("@")
    share_link = f"https://t.me/{bot_username}?start=shared_by_{user_id}"
    try:
        await query.edit_message_text(
            f"贱奴！把这链接发出去，每拉一个新狗进来，你爹我赏你3个狗粮🦴！\n分享链接：{share_link}"
            if lang == "zh-cn" else
            f"Filthy dog! Share this link, for every new dog you bring, I'll reward you 3 dog food🦴!\nShare link: {share_link}",
            reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("滚回去" if lang == "zh-cn" else "Get Lost", callback_data="back_to_main")]])
        )
    except TelegramError as e:
        logger.error(f"发送分享链接失败，用户ID: {user_id}, 错误: {str(e)}")


async def handle_go_to_pay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if not (ENABLE_TELEGRAM_PAYMENTS or ENABLE_API_KEY):
        try:
            await query.edit_message_text(
                "贱奴！你爹我没开支付功能，别在这丢人现眼！"
                if lang == "zh-cn" else
                "Filthy dog! I haven't enabled payments, stop embarrassing yourself!"
            )
        except TelegramError as e:
            logger.error(f"发送支付提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    try:
        reply_markup, hint = await get_pay_menu(user_id)
        await query.edit_message_text(
            f"贱奴！想继续被你爹我羞辱？\n{hint}"
            if lang == "zh-cn" else
            f"Filthy dog! Want to keep being humiliated by me?\n{hint}",
            reply_markup=reply_markup
        )
    except TelegramError as e:
        logger.error(f"发送支付菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！爷弄支付菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the payment menu, try again later!"
        )


async def handle_telegram_stars(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[2])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    if not ENABLE_TELEGRAM_PAYMENTS:
        await query.edit_message_text(
            "贱奴！你爹我没开 Telegram Stars 支付，别在这浪费时间！"
            if lang == "zh-cn" else
            "Filthy dog! I haven't enabled Telegram Stars payments, stop wasting time!"
        )
        return
    keyboard = [
        [InlineKeyboardButton(f"{MEMBERSHIP_PRICES['30_days']} Stars = 1个月会员" if lang == "zh-cn" else f"{MEMBERSHIP_PRICES['30_days']} Stars = 1 Month Membership", callback_data=f"pay_membership_30_{user_id}")],
        [InlineKeyboardButton(f"{MEMBERSHIP_PRICES['365_days']} Stars = 1年会员" if lang == "zh-cn" else f"{MEMBERSHIP_PRICES['365_days']} Stars = 1 Year Membership", callback_data=f"pay_membership_365_{user_id}")],
        [InlineKeyboardButton(f"{MEMBERSHIP_PRICES['permanent']} Stars = 永久会员" if lang == "zh-cn" else f"{MEMBERSHIP_PRICES['permanent']} Stars = Permanent Membership", callback_data=f"pay_membership_permanent_{user_id}")],
        [InlineKeyboardButton("取消未完成支付" if lang == "zh-cn" else "Cancel Pending Payment", callback_data="cancel_pending_payment")],
        [InlineKeyboardButton("返回上一级" if lang == "zh-cn" else "Back", callback_data=f"go_to_pay_{user_id}")]
    ]
    await query.edit_message_text(
        "贱奴！Telegram Stars 支付：\n选个会员套餐让你爹我调教你，别磨蹭！"
        if lang == "zh-cn" else
        "Filthy dog! Telegram Stars payment:\nPick a membership plan for me to train you, don't dawdle!",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )


async def handle_buy_redeem_code(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    user_id = int(query.data.split("_")[3])
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    reply_markup, hint = await get_redeem_code_menu(user_id)
    try:
        await query.edit_message_text(hint, reply_markup=reply_markup)
        logger.info(f"用户 {user_id} 点击购买兑换码，显示兑换码菜单")
    except TelegramError as e:
        logger.error(f"发送兑换码菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！兑换码菜单抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Redeem code menu crashed, try again later!",
            reply_markup=reply_markup
        )


async def cancel_pending_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """取消用户的 pending 支付状态"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    pending_key = f"payment:pending:{user_id}"
    pending_data = await redis_client.get(pending_key)
    
    if not pending_data:
        try:
            await query.edit_message_text(
                "贱奴！当前没有未完成的支付！"
                if lang == "zh-cn" else
                "Filthy dog! No pending payments found!",
                reply_markup=get_main_menu(user_id)
            )
        except TelegramError as e:
            logger.error(f"发送无 pending 支付提示失败，用户ID: {user_id}, 错误: {str(e)}")
        return
    
    try:
        await redis_client.delete(pending_key)
        user_data["pending_payment"] = None
        await save_user_data(user_id, user_data)
        logger.info(f"用户 {user_id} 取消 pending 支付")
        
        await query.edit_message_text(
            "贱奴！未完成的支付已取消！"
            if lang == "zh-cn" else
            "Filthy dog! Pending payment canceled!",
            reply_markup=get_main_menu(user_id)
        )
    except Exception as e:
        logger.error(f"取消 pending 支付失败，用户ID: {user_id}, 错误: {str(e)}")
        await query.edit_message_text(
            "贱奴！取消支付时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! Something went wrong canceling payment, try again later!",
            reply_markup=get_main_menu(user_id)
        )


async def use_redeem_code(code: str, user_id: str, user_data: dict) -> tuple[bool, str]:
    """验证兑换码并更新用户积分，从文件中删除已使用的兑换码"""
    lang = user_data.get("preferred_language", "zh-cn")
    code = code.strip()
    logger.info(f"用户 {user_id} 尝试使用兑换码: {code}")
    
    # 定义兑换码文件和对应的奖励
    code_files = {
        SETTINGS_CODES_FILE: ("settings", 1, "paid_change_count", "获得1次调教设定修改机会"),
        TEXT_50_CODES_FILE: ("text_50", 50, "remaining_count", "获得50次文字调教次数"),
        TEXT_CODES_FILE: ("text_100", 100, "remaining_count", "获得100次文字调教次数"),
        VOICE_20_CODES_FILE: ("voice_20", 20, "voice_count", "获得20次语音调教次数"),
        VOICE_50_CODES_FILE: ("voice_50", 50, "voice_count", "获得50次语音调教次数"),
        VOICE_CODES_FILE: ("voice_100", 100, "voice_count", "获得100次语音调教次数"),
        MEMBERSHIP_30_DAYS_CODES_FILE: ("membership_30_days", 30, None, "获得30天会员资格"),
        MEMBERSHIP_365_DAYS_CODES_FILE: ("membership_365_days", 365, None, "获得365天会员资格"),
        MEMBERSHIP_PERMANENT_CODES_FILE: ("membership_permanent", None, None, "获得永久会员资格")
    }
    
    try:
        # 遍历兑换码文件
        for file_path, (code_type, amount, field, reward_text) in code_files.items():
            if not os.path.exists(file_path):
                logger.warning(f"兑换码文件 {file_path} 不存在")
                continue
            
            logger.debug(f"检查兑换码文件: {file_path}")
            # 读取文件内容
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    codes = [line.strip() for line in f if line.strip()]
                logger.debug(f"兑换码文件 {file_path} 包含 {len(codes)} 个兑换码")
            except Exception as e:
                logger.error(f"读取兑换码文件 {file_path} 失败，错误: {str(e)}")
                continue
            
            # 检查兑换码是否在文件中
            if code in codes:
                # 从文件中移除兑换码
                try:
                    codes.remove(code)
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write("\n".join(codes) + "\n")
                    logger.info(f"兑换码 {code} 已从文件 {file_path} 中移除")
                except Exception as e:
                    logger.error(f"从文件 {file_path} 移除兑换码 {code} 失败，错误: {str(e)}")
                    return False, (
                        "兑换码处理失败，请稍后再试！"
                        if lang == "zh-cn" else
                        "Redeem code processing failed, try again later!"
                    )
                
                # 更新用户积分
                if field == "voice_count":
                    user_data["voice_enabled"] = True
                
                # 处理会员兑换码
                if code_type == "membership_30_days":
                    current_expiry = user_data.get("membership_expiry")
                    # 简化验证逻辑，只检查是否为有效的ISO时间格式
                    if current_expiry:
                        try:
                            current_expiry_date = datetime.fromisoformat(current_expiry)
                            new_expiry = current_expiry_date + timedelta(days=30)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                            new_expiry = datetime.now() + timedelta(days=30)
                    else:
                        new_expiry = datetime.now() + timedelta(days=30)
                    user_data["membership_expiry"] = new_expiry.isoformat()
                    user_data["dog_food"] += 20
                    user_data["voice_enabled"] = True
                    # 保存用户数据
                    await save_user_data(user_id, user_data)
                    message = f"获得30天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和20个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    return True, message
                elif code_type == "membership_365_days":
                    current_expiry = user_data.get("membership_expiry")
                    # 简化验证逻辑，只检查是否为有效的ISO时间格式
                    if current_expiry:
                        try:
                            current_expiry_date = datetime.fromisoformat(current_expiry)
                            new_expiry = current_expiry_date + timedelta(days=365)
                        except (ValueError, TypeError) as e:
                            logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {current_expiry}，错误: {str(e)}，重置为当前时间")
                            new_expiry = datetime.now() + timedelta(days=365)
                    else:
                        new_expiry = datetime.now() + timedelta(days=365)
                    user_data["membership_expiry"] = new_expiry.isoformat()
                    user_data["dog_food"] += 100
                    user_data["voice_enabled"] = True
                    # 保存用户数据
                    await save_user_data(user_id, user_data)
                    message = f"获得365天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和100个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
                    return True, message
                elif code_type == "membership_permanent":
                    user_data["membership_expiry"] = None
                    user_data["paid_change_count"] += 100
                    user_data["permanent_member"] = 1  # 使用整数1标记为永久会员
                    # 保存用户数据
                    await save_user_data(user_id, user_data)
                    logger.info(f"用户 {user_id} 通过兑换码获得永久会员身份")
                    message = f"获得永久会员资格和100次角色设定修改次数！当前狗粮数量：{user_data['dog_food']}"
                    return True, message
                elif field is not None:
                    # 处理其他类型的兑换码
                    current_value = user_data.get(field, 0)
                    user_data[field] = current_value + amount
                    # 保存用户数据
                    await save_user_data(user_id, user_data)
                    message = f"贱奴！{reward_text}！当前剩余{field}：{user_data[field]}"
                    return True, message
                
        # 如果遍历完所有文件都没找到兑换码
        return False, (
            "兑换码无效或已被使用！"
            if lang == "zh-cn" else
            "Redeem code invalid or already used!"
        )
    except Exception as e:
        logger.error(f"处理兑换码 {code} 失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        return False, (
            "兑换码处理失败，请稍后再试！"
            if lang == "zh-cn" else
            "Redeem code processing failed, try again later!"
        )


async def handle_personal_center_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的个人中心 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 获取犬名 - 从training_preferences中获取
    dog_name = user_data.get("training_preferences", {}).get("dog_name", "")
    if not dog_name or not dog_name.strip():
        dog_name = "未设置" if lang == "zh-cn" else "Not set"
    
    # 获取编号
    user_number = await get_user_number(user_id)
    
    # 获取推广次数
    share_count = user_data["daily_new_shares"] + user_data["daily_old_shares"]
    
    # 检查会员状态
    membership_status = "非会员" if lang == "zh-cn" else "Non-Member"
    membership_expiry = user_data.get("membership_expiry")
    is_member = False
    
    # 首先检查是否为永久会员
    if user_data.get("permanent_member", False):
        membership_status = "永久会员" if lang == "zh-cn" else "Permanent Member"
        is_member = True
    elif membership_expiry:
        try:
            expiry_date = datetime.fromisoformat(membership_expiry)
            if expiry_date > datetime.now():
                days_left = (expiry_date - datetime.now()).days
                membership_status = f"会员（到期：{membership_expiry[:10]}）" if lang == "zh-cn" else f"Member (Expires: {membership_expiry[:10]})"
                is_member = True
        except (ValueError, TypeError) as e:
            logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {membership_expiry}，错误: {str(e)}")
    
    # 获取狗粮信息
    dog_food_count = user_data.get("dog_food", 0)
    daily_free_count = user_data.get("daily_free_dog_food", 0)
    
    # 根据会员状态显示狗粮信息
    if is_member:
        dog_food_display = f"{dog_food_count} 🦴（会员对话不消耗🦴）" if lang == "zh-cn" else f"{dog_food_count} 🦴 (Member: no consumption🦴)"
        free_dog_food_display = f"{daily_free_count} 🦴（会员对话不消耗🦴）" if lang == "zh-cn" else f"{daily_free_count} 🦴 (Member: no consumption🦴)"
    else:
        dog_food_display = f"{dog_food_count} 🦴" if lang == "zh-cn" else f"{dog_food_count} 🦴"
        free_dog_food_display = f"{daily_free_count} 🦴" if lang == "zh-cn" else f"{daily_free_count} 🦴"
    
    message = (
        "🐕 **贱犬档案** 🐕\n"
        "───────────────────\n"
        f"犬名：{dog_name}\n"
        f"编号：{user_number}\n"
        f"会员状态：{membership_status}\n"
        f"狗粮余额：{dog_food_display}\n"
        f"免费狗粮：{free_dog_food_display}\n"
        f"推广次数：{share_count} 只\n"
        "───────────────────\n"
        "🔥 想多挨骂？选个方式！"
        if lang == "zh-cn" else
        "🐕 **Filthy Dog Profile** 🐕\n"
        "───────────────────\n"
        f"Dog name: {dog_name}\n"
        f"ID: {user_number}\n"
        f"Membership: {membership_status}\n"
        f"Dog Food: {dog_food_display}\n"
        f"Free Dog Food: {free_dog_food_display}\n"
        f"Referrals: {share_count} dogs\n"
        "───────────────────\n"
        "🔥 Want more humiliation? Pick a way!"
    )
    keyboard = [
        [InlineKeyboardButton("📢 分享赚次数" if lang == "zh-cn" else "📢 Share to Earn", callback_data=f"share_bot_{user_id}")],
        [InlineKeyboardButton("💸 滚去支付" if lang == "zh-cn" else "💸 Go Pay", callback_data=f"go_to_pay_{user_id}")],
        [InlineKeyboardButton("🏃 滚回去" if lang == "zh-cn" else "🏃 Get Lost", callback_data="back_to_main")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    try:
        await update.message.reply_text(message, reply_markup=reply_markup, parse_mode="Markdown")
        logger.info(f"用户 {user_id} 通过快捷回复进入个人中心")
    except TelegramError as e:
        logger.error(f"发送个人中心信息失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！爷弄个人中心时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on your profile, try again later!",
            reply_markup=reply_markup
        )


async def handle_free_credits_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的免费积分 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    reply_markup, hint = await get_free_credits_menu(user_id)
    try:
        await update.message.reply_text(hint, reply_markup=reply_markup, parse_mode="Markdown")
        logger.info(f"用户 {user_id} 通过快捷回复访问免费积分菜单")
    except TelegramError as e:
        logger.error(f"发送免费积分菜单失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            "贱奴！爷弄积分菜单时抽风了，稍后再试！"
            if lang == "zh-cn" else
            "Filthy dog! My system choked on the credits menu, try again later!",
            reply_markup=get_main_menu(user_id)
        )


async def handle_pay_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的会员身份 - 发送新消息版本"""
    from handlers.base import get_pay_menu
    user_id = update.effective_user.id
    logger.info(f"快捷回复菜单收到会员身份请求，用户ID: {user_id}")
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")

    try:
        reply_markup, hint = await get_pay_menu(user_id)
        await update.message.reply_text(
            text=(
                "🔥 贱奴！选择支付方式给爷进贡！🔥\n"
                "💎 **会员特权**：会员期间对话不消耗任何积分，无限次数调教！\n"
                "───────────────────\n"
                f"{hint}"
                if lang == "zh-cn" else
                "🔥 Filthy dog! Choose a payment method to tribute me! 🔥\n"
                "💎 **Membership Benefits**: No credits consumed during membership, unlimited training sessions!\n"
                "───────────────────\n"
                f"{hint}"
            ),
            reply_markup=reply_markup
        )
        logger.info(f"通过快捷回复发送支付菜单成功，用户ID: {user_id}")
    except TelegramError as e:
        logger.error(f"通过快捷回复发送支付菜单失败，用户ID: {user_id}, 错误: {str(e)}")
        await update.message.reply_text(
            text=(
                "贱奴！支付菜单发送失败，稍后再试或联系 @master_kabot！"
                if lang == "zh-cn" else
                "Filthy dog! Failed to send payment menu, try again later or contact @master_kabot!"
            ),
            reply_markup=get_main_menu(user_id)
        )
