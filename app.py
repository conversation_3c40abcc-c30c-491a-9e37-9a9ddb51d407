from telegram.ext import ApplicationBuilder
from config import BOT_TOKEN
from logging_config import logger

# 全局 Application 对象
app_instance = None

def init_app():
    global app_instance
    if app_instance is None:
        builder = ApplicationBuilder().token(BOT_TOKEN)
        app_instance = builder.build()
        app_instance.get_updates_timeout = 30.0
        logger.info("Application 实例已初始化")
    return app_instance