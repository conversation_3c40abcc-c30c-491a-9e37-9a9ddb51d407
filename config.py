import os
import asyncio
from openai import OpenAI
from elevenlabs.client import ElevenLabs
from fish_audio_sdk import Session
from logging_config import logger
import redis.asyncio as redis

# Gemini API 相关导入
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("Gemini SDK未安装，Gemini API功能将不可用。请运行: pip install google-generativeai")

BOT_TOKEN = '7900735840:AAHaYWB-0Q9X9qNlnxsi4lwBuSFWn3Sn5eU'
MASTER_ID = '554652636'
DOWNLOAD_DIR = "/botfather/bot2/downloads"
USDT_ADDRESS = "TRLziZHjSsH9YjxbMsCd1doLDNPLRn9oh9"
BOT_USERNAME = '@y_masterbot'

# Webhook 配置
WEBHOOK_URL = "https://b2.botfather.net/webhook"
SERVER_PORT = 7777
SERVER_HOST = "0.0.0.0"

# 数据库配置
DB_PATH = "/botfather/bot2/bot_data.db"
BACKUP_DIR = "/botfather/bot2/backups"  # 新增备份目录

# Redis 配置
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_POOL = redis.ConnectionPool(host=REDIS_HOST, port=REDIS_PORT, db=REDIS_DB, decode_responses=True)
redis_client = redis.Redis(connection_pool=REDIS_POOL)

# 文字对话 API 配置
AI_API_CONFIGS = [
    {
        "url": "http://127.0.0.1:8999/v1/chat/completions",
        "key": "sk-zaSy52641106",  # 请替换为您的Modelslab API密钥
        "model": "gemini-2.0-flash",
        "name": "自建",  # 添加名称便于识别
        "stream": False,  #
        "break_limit": True,  # 是否需要破限：True=需要破限，False=不需要破限
        "max_tokens": 20000,  # 最大输出token数，增加此值可避免输出被截断
        "is_multimodal": True,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
    {
        "url": "https://grokyellow.96ai.top/v1/chat/completions",
        "key": "2035614117",  # 请替换为您的Modelslab API密钥
        "model": "酒馆-Flash",
        "name": "酒馆",  # 添加名称便于识别
        "stream": True,
        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
        "is_multimodal": False,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
#    {
#        "url": "https://api.x.ai/v1/chat/completions", 
#        "key": "************************************************************************************",  # 请替换为您的Modelslab API密钥
#        "model": "grok-4",
#        "name": "claude中转",  # 添加名称便于识别
#        "stream": True,  # 
#        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
#        "is_multimodal": True  # 是否为多模态模型，支持直接处理图片/视频
#    },
    {
        "url": "https://api.groq.com/openai/v1/chat/completions",
        "key": "********************************************************",  # 请替换为您的Modelslab API密钥
        "model": "llama3-70b-8192",
        "name": "groq",  # 添加名称便于识别
        "stream": True,  # 此API不支持流式输出，只接收完整JSON格式
        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
        "is_multimodal": False,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
    {
        "url": "https://api.together.xyz/v1/chat/completions",
        "key": "bd39c93022e7adcd30656ed765e7823b76db044217a6da746bc3d994152a6f2e",
        "model": "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
        "name": "together-free",  # 添加名称便于识别
        "stream": False,  # 此API流式输出
        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
        "is_multimodal": False,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
    {
        "url": "https://openrouter.ai/api/v1/chat/completions",
        "key": "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",
        "model": "meta-llama/llama-3.3-70b-instruct:free",
        "name": "openrouter-free",  # 添加名称便于识别
        "stream": False,  # 此API流式输出
        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
        "is_multimodal": False,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
    {
        "url": "https://openrouter.ai/api/v1/chat/completions",
        "key": "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",
        "model": "meta-llama/llama-3.3-70b-instruct",
        "name": "openrouter",  # 添加名称便于识别
        "stream": False,  # 此API流式输出
        "break_limit": False,  # 是否需要破限：True=需要破限，False=不需要破限
        "is_multimodal": False,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "openai"  # API类型：openai 或 gemini
    },
    # Gemini API 示例配置 - 原生API
#    {
#        "key": "your-gemini-api-key-here",  # 请替换为您的Gemini API密钥
#        "model": "gemini-2.0-flash-exp",
#        "name": "Gemini Flash",  # 添加名称便于识别
#        "stream": False,  # Gemini API流式输出支持
#        "break_limit": True,  # 是否需要破限：True=需要破限，False=不需要破限
#        "max_tokens": 8192,  # 最大输出token数
#        "is_multimodal": True,  # 是否为多模态模型，支持直接处理图片/视频
#        "api_type": "gemini"  # API类型：openai 或 gemini
#    },
    # Gemini Balance 代理API配置示例 - OpenAI格式
#    {
#        "url": "http://localhost:8080/v1/chat/completions",  # gemini-balance代理地址
#        "key": "your-gemini-api-key-here",  # 请替换为您的Gemini API密钥
#        "model": "gemini-2.0-flash-exp",
#        "name": "Gemini Balance OpenAI",  # 添加名称便于识别
#        "stream": False,  # 流式输出支持
#        "break_limit": True,  # 是否需要破限：True=需要破限，False=不需要破限
#        "max_tokens": 8192,  # 最大输出token数
#        "is_multimodal": True,  # 是否为多模态模型，支持直接处理图片/视频
#        "api_type": "gemini_proxy",  # API类型：gemini_proxy 表示使用代理
#        "proxy_format": "openai"  # 代理格式：openai 或 gemini
#    },
    # Gemini Balance 代理API配置示例 - Gemini原生格式（支持File API）
    {
        "url": "http://localhost:8080",  # gemini-balance代理基础地址
        "key": "your-gemini-api-key-here",  # 请替换为您的Gemini API密钥
        "model": "gemini-2.0-flash-exp",
        "name": "Gemini Balance Native",  # 添加名称便于识别
        "stream": False,  # 流式输出支持
        "break_limit": True,  # 是否需要破限：True=需要破限，False=不需要破限
        "max_tokens": 8192,  # 最大输出token数
        "is_multimodal": True,  # 是否为多模态模型，支持直接处理图片/视频
        "api_type": "gemini_proxy",  # API类型：gemini_proxy 表示使用代理
        "proxy_format": "gemini",  # 代理格式：gemini 支持File API和高级功能
        "supports_file_api": True  # 支持文件API
    }
]

# 破限对话文件路径配置
BREAK_LIMIT_CONVERSATIONS_FILE = "handlers/chat-scenario.json"

# 重复惩罚配置参数
ANTI_REPETITION_CONFIG = {
    "enabled": True,  # 是否启用重复惩罚
    "frequency_penalty": 0.4,  # 频率惩罚，降低重复词汇的概率 (0.0-2.0) - 增强
    "presence_penalty": 0.3,   # 存在惩罚，鼓励引入新主题 (0.0-2.0) - 增强
    "repetition_penalty": 1.15,  # 重复惩罚，针对支持此参数的模型 (1.0-1.3) - 增强
    "top_p": 0.85,  # 核采样，增加多样性 (0.1-1.0) - 稍微降低以增加多样性
    "temperature_min": 0.8,  # 温度最小值 - 提高创造性
    "temperature_max": 1.2,  # 温度最大值 - 提高创造性
    "similarity_threshold": 0.65,  # 相似度阈值，超过此值认为重复 - 降低阈值更敏感
    "max_recent_responses": 8,  # 检查的最近回复数量 - 增加检查范围
    "structure_check": True,  # 启用结构相似度检测
    "semantic_check": True,  # 启用语义相似度检测
    "plot_progression_check": True,  # 启用剧情推进检测
}

# API保活配置
API_KEEPALIVE_CONFIG = {
    "enabled": False,  # 是否启用API保活功能
    "target_api_name": "featherless",  # 需要保活的API名称，对应AI_API_CONFIGS中的name字段
    "interval": 4 * 60,  # 保活间隔时间（秒），默认4分钟
    "check_interval": 60,  # 检查间隔（秒），默认60秒检查一次
    "timeout": 30,  # 保活请求超时时间（秒）
    "max_tokens": 10,  # 保活请求使用的最大token数
    "temperature": 0.1  # 保活请求使用的温度参数
}

# 默认文字对话 API 配置（从 AI_API_CONFIGS 中选择第一个）
AI_API_URL = AI_API_CONFIGS[0]["url"]
AI_API_KEY = AI_API_CONFIGS[0]["key"]
AI_MODEL = AI_API_CONFIGS[0]["model"]

# 默认图片识别 API 配置（从 IMAGE_API_CONFIGS 中选择第一个）
IMAGE_API_URL = IMAGE_API_CONFIGS[0]["url"]
IMAGE_API_KEY = IMAGE_API_CONFIGS[0]["key"]
IMAGE_MODEL = IMAGE_API_CONFIGS[0]["model"]

AI_API_URL = AI_API_CONFIGS[0]["url"]
AI_API_KEY = AI_API_CONFIGS[0]["key"]
AI_MODEL = AI_API_CONFIGS[0]["model"]


PAYMENT_QR_FILE_ID = "AgACAgEAAxkBAAEDT1ln-L1990Yxv_6wnIP-Er_QPYghSAACga4xG0l-yUc7u8c75Ih1JQEAAwIAA3kAAzYE"

INITIAL_AI_USAGE_LIMIT = 30
ALWAYS_USE_VOICE_RESPONSE = False
BOT_LOCK_PATH = "/botfather/bot2/bot_lock.lock"
ENABLE_TELEGRAM_PAYMENTS = True
ENABLE_API_KEY = False

# Telegram Stars 会员价格配置（单位：Stars）
MEMBERSHIP_PRICES = {
    "30_days": 350,      # 1个月会员价格（Stars）
    "365_days": 2300,    # 1年会员价格（Stars）
    "permanent": 3300    # 永久会员价格（Stars）
}

WHISPER_CONFIG = {
    "enabled": True, 
    "api_key": "sk-mUrj4In5U9fiklxHcK0pAtG46CZiOEHEVGJzz2l3yqx5qQ4u",
    "base_url": "https://api.ephone.chat/v1"
}

FISH_STT_CONFIG = {
    "enabled": True,
    "api_key": "********************************",
    "default_language": "zh"  # 默认中文，可设置为 "en" 或 None（自动检测）
}

# TTS 配置
TTS_CONFIG = {
    "openai": {"enabled": False, "api_key": "sk-TLYiyIxyyRwuwntIXPbIgouXhQGU3e01C4jWWDEzUJRIHsfI", "base_url": "https://api.gptgod.online/v1", "voice": "onyx"},
    "elevenlabs": {"enabled": False, "api_key": "***************************************************", "voice": "fQj4gJSexpu8RDE2Ii5m"},
    "fishaudio": {
        "enabled": False,
        "api_key": "********************************",
        "voices": [
            {"name": "默认", "reference_id": "bc13e344c8a24d568a085809254515f7"},
            {"name": "学生", "reference_id": "ab812d68675e4fcaa46f3262f61d3d04"},
            {"name": "男S", "reference_id": "142efee94fed4aed91469b94b7653e09"},
            {"name": "痞子小哥", "reference_id": "a9372068ed0740b48326cf9a74d7496a"},
            {"name": "大学男生", "reference_id": "24d524b57c5948f598e9b74c4dacc7ab"},
            {"name": "东北小哥", "reference_id": "aca9c50f4e834038bb04268eff0290b6"},
            {"name": "四川小哥", "reference_id": "7e472234de704a1e907d06cff3d8ad09"},
            {"name": "硬汉", "reference_id": "0a913ecc0b2a44c2a1cca4ab759d5b90"},
            {"name": "声音12", "reference_id": "6283d5eb90c546a5bf0862f20089ce76"},
            {"name": "声音14", "reference_id": "22aa2c9e5c544f9886220fbf593408c6"},
            {"name": "声音16", "reference_id": "c18619439acb4d71bee3c44a5d47c673"},
            {"name": "声音18", "reference_id": "4ba90289c54f4adba5e8c85446874a18"},
            {"name": "声音19", "reference_id": "7f92f8afb8ec43bf81429cc1c9199cb1"},
            {"name": "学姐", "reference_id": "7f92f8afb8ec43bf81429cc1c9199cb1"}
        ]
    },
    "local_tts": {
        "enabled": True,  # 是否启用本地 TTS 服务 (index-tts)
        "api_url": "http://0.0.0.0:11996",  # 本地 TTS API 地址
        "voices": [
            {"name": "默认S", "reference_id": "local_default", "reference_audio": "reference_audios/nans.mp3", "service": "local_tts"},
            {"name": "东北小哥S", "reference_id": "local_01", "reference_audio": "reference_audios/dongbeis.mp3", "service": "local_tts"},
            {"name": "学生s", "reference_id": "local_02", "reference_audio": "reference_audios/xueshengs.mp3", "service": "local_tts"},
            {"name": "四川小哥s", "reference_id": "local_04", "reference_audio": "reference_audios/sichuans.mp3", "service": "local_tts"},
            {"name": "猛s", "reference_id": "local_05", "reference_audio": "reference_audios/mengs.mp3", "service": "local_tts"},
            {"name": "温柔S", "reference_id": "local_07", "reference_audio": "reference_audios/wenrous.mp3", "service": "local_tts"},
            {"name": "女性主人", "reference_id": "local_female_master", "reference_audio": "reference_audios/nvzhuren.mp3", "service": "local_tts"}
        ]
    }
}

# 非活跃用户提醒配置
INACTIVE_USER_CONFIG = {
    "check_interval": 95,  # 检查频率（小时）
    "inactive_threshold": 72,  # 不活跃阈值（小时）
    "messages_per_minute": 10,  # 每分钟发送消息数量限制
    "use_custom_api": True,  # 是否使用自定义API
    "api_config": {  # 自定义API配置
        "url": "http://127.0.0.1:8999/v1/chat/completions", 
        "key": "sk-zaSy52641106",  # 请替换为您的Modelslab API密钥
        "model": "gemini-2.5-flash-lite-preview-06-17",
        "name": "自建",  # 添加名称便于识别
        "stream": True,  # 
        "break_limit": True,  # 是否需要破限：True=需要破限，False=不需要破限
    }
}

#    {


# 默认语音，优先选择启用的服务
DEFAULT_VOICE = next(
    (v["reference_id"] for svc in ["fishaudio", "local_tts"] if TTS_CONFIG[svc]["enabled"] for v in TTS_CONFIG[svc]["voices"]),
    TTS_CONFIG["fishaudio"]["voices"][2]["reference_id"]
)
FISH_AUDIO_SEMAPHORE = asyncio.Semaphore(15)
CHANNEL_LINK = "https://t.me/botnotice_s"
SEARCH_GROUP_LINK = "https://t.me/+lGIqMW8Mbu4wYWUx"
SEARCH_GROUP_ID = "-1002522866256"
TEXT_CODES_FILE = "/botfather/codes/text_codes.txt"
VOICE_CODES_FILE = "/botfather/codes/voice_codes.txt"
SETTINGS_CODES_FILE = "/botfather/codes/settings_codes.txt"
TEXT_50_CODES_FILE = "/botfather/codes/text_50_codes.txt"
VOICE_20_CODES_FILE = "/botfather/codes/voice_20_codes.txt"
VOICE_50_CODES_FILE = "/botfather/codes/voice_50_codes.txt"
REDEEM_CODE_PURCHASE_URL = "https://t.me/master_kabot"
MEMBERSHIP_30_DAYS_CODES_FILE = "/botfather/codes/membership_30_days.txt"
MEMBERSHIP_365_DAYS_CODES_FILE = "/botfather/codes/membership_365_days.txt"
MEMBERSHIP_PERMANENT_CODES_FILE = "/botfather/codes/membership_permanent.txt"   #永久会员兑换码

# 流式输出配置
ENABLE_STREAMING_RESPONSE = True  # 是否启用流式输出（True启用，False使用标准模式）

whisper_client = OpenAI(api_key=WHISPER_CONFIG["api_key"], base_url=WHISPER_CONFIG["base_url"]) if WHISPER_CONFIG["enabled"] else None
openai_client = OpenAI(api_key=TTS_CONFIG["openai"]["api_key"], base_url=TTS_CONFIG["openai"]["base_url"]) if TTS_CONFIG["openai"]["enabled"] else None
elevenlabs_client = ElevenLabs(api_key=TTS_CONFIG["elevenlabs"]["api_key"]) if TTS_CONFIG["elevenlabs"]["enabled"] else None
fishaudio_session = Session(TTS_CONFIG["fishaudio"]["api_key"]) if TTS_CONFIG["fishaudio"]["enabled"] else None
fishaudio_stt_session = Session(FISH_STT_CONFIG["api_key"]) if FISH_STT_CONFIG["enabled"] else None

# 初始化目录
os.makedirs(DOWNLOAD_DIR, exist_ok=True)
os.makedirs(BACKUP_DIR, exist_ok=True)  # 新增备份目录初始化
codes_dir = "/botfather/codes"
os.makedirs(codes_dir, exist_ok=True)
for file_path in [TEXT_CODES_FILE, VOICE_CODES_FILE, SETTINGS_CODES_FILE, TEXT_50_CODES_FILE, VOICE_20_CODES_FILE, VOICE_50_CODES_FILE, MEMBERSHIP_30_DAYS_CODES_FILE, MEMBERSHIP_365_DAYS_CODES_FILE, MEMBERSHIP_PERMANENT_CODES_FILE]:  #永久会员兑换码
    try:
        if not os.path.exists(file_path):
            with open(file_path, "w") as f:
                f.write("")
            logger.info(f"创建兑换码文件: {file_path}")
    except Exception as e:
        logger.error(f"无法创建兑换码文件 {file_path}: {str(e)}")
        raise
    
# 添加VLLM MiniCPM API配置
VLLM_MINICPM_CONFIG = {
    "enabled": True,
    "api_url": "http://localhost:8000/v1/chat/completions",
    "api_key": "1",
    "model": "/botfather/LLM/quantized_qwen2.5_vl_7b_nsfw",
    "temperature": 0.7,
    "max_tokens": 1000
}