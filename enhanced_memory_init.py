"""
增强记忆系统初始化脚本
确保系统正确启动并处理可能的兼容性问题
"""

import asyncio
from enhanced_memory import enhanced_memory, MemoryType
from enhanced_ai_dialogue import enhanced_ai_dialogue
from logging_config import logger

async def initialize_enhanced_memory():
    """初始化增强记忆系统"""
    try:
        # 确保增强记忆系统正确加载
        logger.info("正在初始化增强记忆系统...")
        
        # 启动自动提交任务
        await enhanced_memory.start_auto_commit()
        
        # 测试基本功能
        test_user_id = "test_init"
        test_memory_id = await enhanced_memory.add_structured_memory(
            user_id=test_user_id,
            content="测试记忆初始化",
            memory_type=MemoryType.CONVERSATION,
            tags=["测试"],
            emotional_valence=0.0
        )
        
        # 等待向量处理完成
        logger.info("等待向量处理完成...")
        await asyncio.sleep(2)  # 给向量编码一些时间
        
        # 测试搜索功能
        memories = await enhanced_memory.search_enhanced_memory(
            user_id=test_user_id,
            query="测试记忆",
            k=1
        )
        
        if memories:
            logger.info("增强记忆系统初始化成功！")
            # 清理测试数据
            if test_user_id in enhanced_memory.structured_memories:
                enhanced_memory.structured_memories[test_user_id] = []
            return True
        else:
            # 再尝试一次更简单的搜索
            memories = await enhanced_memory.search_enhanced_memory(
                user_id=test_user_id,
                query="测试",
                k=1
            )
            
            if memories:
                logger.info("增强记忆系统初始化成功（第二次尝试）！")
                # 清理测试数据
                if test_user_id in enhanced_memory.structured_memories:
                    enhanced_memory.structured_memories[test_user_id] = []
                return True
            else:
                # 检查结构化记忆是否存在
                if test_user_id in enhanced_memory.structured_memories and enhanced_memory.structured_memories[test_user_id]:
                    logger.info("增强记忆系统基本功能正常，向量搜索可能需要更多时间")
                    return True
                else:
                    logger.warning("增强记忆系统测试未找到预期结果")
                    return False
            
    except Exception as e:
        logger.error(f"增强记忆系统初始化失败: {str(e)}", exc_info=True)
        return False

async def check_compatibility():
    """检查与原有系统的兼容性"""
    try:
        # 检查必要的依赖
        import faiss
        import numpy as np
        from sentence_transformers import SentenceTransformer
        
        logger.info("所有依赖检查通过")
        return True
        
    except ImportError as e:
        logger.error(f"依赖检查失败: {str(e)}")
        logger.error("请安装缺失的依赖: pip install faiss-cpu sentence-transformers")
        return False

def print_system_status():
    """打印系统状态"""
    try:
        total_memories = 0
        for user_memories in enhanced_memory.structured_memories.values():
            total_memories += len(user_memories)
            
        # 修复pending_vectors的引用
        pending_count = 0
        if hasattr(enhanced_memory, 'memory_vectors'):
            pending_count = len(enhanced_memory.memory_vectors)
        elif hasattr(enhanced_memory, 'pending_vectors'):
            pending_count = len(enhanced_memory.pending_vectors)
            
        logger.info(f"""
=== 增强记忆系统状态 ===
- 总用户数: {len(enhanced_memory.structured_memories)}
- 总记忆数: {total_memories}
- 向量索引大小: {enhanced_memory.index.ntotal if hasattr(enhanced_memory, 'index') else 0}
- 待处理向量: {pending_count}
========================
        """)
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")

async def main():
    """主函数"""
    logger.info("开始增强记忆系统启动检查...")
    
    # 检查兼容性
    if not await check_compatibility():
        logger.error("兼容性检查失败，请解决依赖问题后重试")
        return False
    
    # 初始化增强记忆系统
    if not await initialize_enhanced_memory():
        logger.error("增强记忆系统初始化失败")
        return False
    
    # 打印系统状态
    print_system_status()
    
    logger.info("增强记忆系统启动完成！")
    return True

if __name__ == "__main__":
    asyncio.run(main()) 