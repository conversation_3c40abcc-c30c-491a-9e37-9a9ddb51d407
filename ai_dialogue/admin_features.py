"""
管理员功能模块 - 负责管理员相关功能，如广播、私聊等
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from database import get_user_data, save_user_data
from logging_config import logger
from config import MASTER_ID

# 广播状态跟踪
broadcast_data = {}


async def start_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    启动广播功能
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            await update.message.reply_text("❌ 您没有权限使用此功能")
            return
        
        # 设置广播状态
        context.user_data["broadcast_mode"] = True
        broadcast_data[update.effective_user.id] = True
        
        keyboard = [
            [InlineKeyboardButton("确认发送", callback_data="confirm_broadcast")],
            [InlineKeyboardButton("取消广播", callback_data="cancel_broadcast")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "📢 广播模式已启动\n\n"
            "请输入要广播的消息内容，然后点击确认发送。\n"
            "支持文字、图片、视频等多种消息类型。",
            reply_markup=reply_markup
        )
        
        logger.info(f"管理员 {user_id} 启动广播模式")
        
    except Exception as e:
        logger.error(f"启动广播失败: {str(e)}")
        await update.message.reply_text("❌ 启动广播失败")


async def finish_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    完成广播设置
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            return
        
        if not context.user_data.get("broadcast_mode"):
            await update.message.reply_text("❌ 广播模式未启动")
            return
        
        # 保存广播消息
        context.user_data["broadcast_message"] = update.message
        
        keyboard = [
            [InlineKeyboardButton("确认广播", callback_data="confirm_broadcast")],
            [InlineKeyboardButton("取消广播", callback_data="cancel_broadcast")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            "📢 广播消息已准备就绪\n\n"
            "请确认是否要发送给所有用户？",
            reply_markup=reply_markup
        )
        
        logger.info(f"管理员 {user_id} 准备广播消息")
        
    except Exception as e:
        logger.error(f"完成广播设置失败: {str(e)}")


async def confirm_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    确认并执行广播
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            await update.callback_query.answer("❌ 您没有权限使用此功能")
            return
        
        query = update.callback_query
        await query.answer()
        
        broadcast_message = context.user_data.get("broadcast_message")
        if not broadcast_message:
            await query.edit_message_text("❌ 没有找到广播消息")
            return
        
        # 获取所有用户
        import aiosqlite
        from config import DB_PATH
        
        success_count = 0
        fail_count = 0
        
        async with aiosqlite.connect(DB_PATH) as db:
            async with db.execute("SELECT user_id FROM users") as cursor:
                users = await cursor.fetchall()
        
        await query.edit_message_text("📢 正在广播消息...")
        
        # 向每个用户发送广播消息
        for user_row in users:
            try:
                target_user_id = user_row[0]
                
                if broadcast_message.text:
                    await context.bot.send_message(
                        chat_id=target_user_id,
                        text=f"📢 系统广播\n\n{broadcast_message.text}"
                    )
                elif broadcast_message.photo:
                    await context.bot.send_photo(
                        chat_id=target_user_id,
                        photo=broadcast_message.photo[-1].file_id,
                        caption=f"📢 系统广播\n\n{broadcast_message.caption or ''}"
                    )
                elif broadcast_message.video:
                    await context.bot.send_video(
                        chat_id=target_user_id,
                        video=broadcast_message.video.file_id,
                        caption=f"📢 系统广播\n\n{broadcast_message.caption or ''}"
                    )
                
                success_count += 1
                
            except Exception as e:
                logger.error(f"向用户 {target_user_id} 广播失败: {str(e)}")
                fail_count += 1
        
        # 清除广播状态
        context.user_data["broadcast_mode"] = False
        context.user_data["broadcast_message"] = None
        broadcast_data.pop(update.effective_user.id, None)
        
        await query.edit_message_text(
            f"📢 广播完成\n\n"
            f"✅ 成功发送: {success_count}\n"
            f"❌ 发送失败: {fail_count}"
        )
        
        logger.info(f"管理员 {user_id} 完成广播，成功: {success_count}, 失败: {fail_count}")
        
    except Exception as e:
        logger.error(f"确认广播失败: {str(e)}")
        await update.callback_query.edit_message_text("❌ 广播失败")


async def cancel_broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    取消广播
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            await update.callback_query.answer("❌ 您没有权限使用此功能")
            return
        
        query = update.callback_query
        await query.answer()
        
        # 清除广播状态
        context.user_data["broadcast_mode"] = False
        context.user_data["broadcast_message"] = None
        broadcast_data.pop(update.effective_user.id, None)
        
        await query.edit_message_text("❌ 广播已取消")
        
        logger.info(f"管理员 {user_id} 取消广播")
        
    except Exception as e:
        logger.error(f"取消广播失败: {str(e)}")


async def handle_broadcast_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    处理广播消息
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            return
        
        # 检查是否在广播模式
        if not broadcast_data.get(update.effective_user.id, False):
            return
        
        # 保存广播消息
        context.user_data["broadcast_message"] = update.message
        
        keyboard = [
            [InlineKeyboardButton("确认广播", callback_data="confirm_broadcast")],
            [InlineKeyboardButton("取消广播", callback_data="cancel_broadcast")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message_type = "文字"
        if update.message.photo:
            message_type = "图片"
        elif update.message.video:
            message_type = "视频"
        elif update.message.voice:
            message_type = "语音"
        elif update.message.document:
            message_type = "文档"
        
        await update.message.reply_text(
            f"📢 {message_type}广播消息已准备就绪\n\n"
            "请确认是否要发送给所有用户？",
            reply_markup=reply_markup
        )
        
        logger.info(f"管理员 {user_id} 设置 {message_type} 广播消息")
        
    except Exception as e:
        logger.error(f"处理广播消息失败: {str(e)}")


async def private_chat_with_user(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    与指定用户私聊
    """
    try:
        user_id = str(update.effective_user.id)
        if user_id != str(MASTER_ID):
            await update.message.reply_text("❌ 您没有权限使用此功能")
            return
        
        if not context.args:
            await update.message.reply_text("❌ 请指定用户ID\n用法: /private <用户ID> <消息内容>")
            return
        
        target_user_id = context.args[0]
        message_content = " ".join(context.args[1:])
        
        if not message_content:
            await update.message.reply_text("❌ 请输入消息内容")
            return
        
        # 发送私聊消息
        await context.bot.send_message(
            chat_id=target_user_id,
            text=f"👨‍💼 管理员消息\n\n{message_content}"
        )
        
        await update.message.reply_text(f"✅ 已向用户 {target_user_id} 发送私聊消息")
        
        logger.info(f"管理员 {user_id} 向用户 {target_user_id} 发送私聊消息")
        
    except Exception as e:
        logger.error(f"私聊失败: {str(e)}")
        await update.message.reply_text("❌ 私聊发送失败")


async def handle_private_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """
    处理私聊消息回复
    """
    try:
        user_id = str(update.effective_user.id)
        message_text = update.message.text
        
        # 检查是否是对管理员消息的回复
        if message_text and message_text.startswith("/reply"):
            # 转发给管理员
            await context.bot.send_message(
                chat_id=MASTER_ID,
                text=f"📩 用户 {user_id} 回复:\n\n{message_text[6:].strip()}"
            )
            
            await update.message.reply_text("✅ 您的回复已发送给管理员")
            
            logger.info(f"用户 {user_id} 回复管理员消息")
        
    except Exception as e:
        logger.error(f"处理私聊消息失败: {str(e)}") 