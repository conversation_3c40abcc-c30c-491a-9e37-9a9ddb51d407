"""
工具函数模块 - 包含各种通用工具函数
"""

import asyncio
import random
from logging_config import logger


async def answer_with_retry(query, max_retries=3, retry_delay=5):
    """
    带重试机制的查询函数
    """
    for attempt in range(max_retries):
        try:
            # 这里可以添加具体的查询逻辑
            # 目前作为占位符函数
            await asyncio.sleep(0.1)
            return f"查询结果: {query}"
            
        except Exception as e:
            logger.error(f"查询失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
            else:
                raise


async def extract_message_info(item):
    """
    提取消息信息
    """
    try:
        message_info = {
            "user_id": item.get("user_id"),
            "message": item.get("message", ""),
            "chat_id": item.get("chat_id"),
            "deduct_type": item.get("deduct_type", "text"),
            "username": item.get("username"),
            "use_voice": item.get("use_voice", False)
        }
        return message_info
        
    except Exception as e:
        logger.error(f"提取消息信息失败: {str(e)}")
        return None


async def filter_message(user_id, message, ai_queue):
    """
    过滤消息，检查是否需要处理
    """
    try:
        # 检查消息是否为空
        if not message or not message.strip():
            logger.warning(f"用户 {user_id} 发送空消息，跳过处理")
            return False
        
        # 检查消息长度
        if len(message) > 4000:
            logger.warning(f"用户 {user_id} 消息过长，跳过处理")
            return False
        
        # 检查队列是否已满
        if ai_queue.qsize() > 100:
            logger.warning(f"AI队列已满，跳过用户 {user_id} 的请求")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"用户 {user_id} 消息过滤失败: {str(e)}")
        return False 