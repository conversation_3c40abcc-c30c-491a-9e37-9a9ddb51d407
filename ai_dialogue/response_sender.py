"""
响应发送模块 - 负责发送AI响应、管理typing状态、处理重试等
"""

import asyncio
import random
import time
from telegram import Bo<PERSON>
from telegram.error import TelegramError, Forbidden
from logging_config import logger
from config import MASTER_ID
from utils import text_to_speech, get_main_menu, get_quick_reply_menu


async def send_with_retry(bot, method, max_retries=3, retry_delay=5, user_id=None, **kwargs):
    """
    带重试机制的发送方法
    """
    for attempt in range(max_retries):
        try:
            return await method(**kwargs)
        except Forbidden as e:
            logger.warning(f"用户 {user_id} 已屏蔽Bot，错误: {str(e)}")
            # 如果有用户ID，更新用户的is_blocked状态
            if user_id:
                try:
                    from database import get_user_data, save_user_data
                    user_data = await get_user_data(user_id)
                    user_data["is_blocked"] = 1
                    await save_user_data(user_id, user_data)
                    logger.info(f"用户 {user_id} 的is_blocked状态已设置为1")
                except Exception as db_error:
                    logger.error(f"更新用户 {user_id} 的is_blocked状态失败: {str(db_error)}")
            raise
        except TelegramError as e:
            logger.error(f"用户 {user_id} 发送失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
            else:
                raise
        except Exception as e:
            logger.error(f"用户 {user_id} 发送异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
            else:
                raise


async def keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger):
    """
    保持typing状态活跃
    """
    try:
        while not typing_event.is_set():
            try:
                if use_voice:
                    await bot.send_chat_action(chat_id=chat_id, action="record_voice")
                else:
                    await bot.send_chat_action(chat_id=chat_id, action="typing")
                
                # 等待5秒或直到事件被设置
                await asyncio.wait_for(typing_event.wait(), timeout=5)
                break
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"用户 {user_id} 发送typing状态失败: {str(e)}")
                break
    except asyncio.CancelledError:
        logger.debug(f"用户 {user_id} typing任务被取消")
    except Exception as e:
        logger.error(f"用户 {user_id} typing任务异常: {str(e)}")


async def send_response(bot, chat_id, user_id, responses, use_voice, username=None, typing_event=None, update=None):
    """
    发送AI响应
    """
    try:
        # 检查用户是否被拉黑
        from database import get_user_data
        user_data = await get_user_data(user_id)
        
        # 如果用户被拉黑，不发送消息
        if user_data.get("is_blocked", 0) == 1:
            logger.info(f"用户 {user_id} 已拉黑机器人，不发送消息")
            if typing_event:
                typing_event.set()
            return False
            
        response_list, deduct_type = responses
        
        if not response_list:
            logger.warning(f"用户 {user_id} 响应列表为空，视为对话失败")
            # 停止typing状态
            if typing_event:
                typing_event.set()
            # 这里返回但不发送消息，由调用方处理对话失败的情况
            return False
        
        # 检查是否为群组聊天
        is_group = chat_id != user_id
        formatted_user = f"@{username}" if username else str(user_id)
        
        # 获取回复标记（如果有）
        reply_markup = None
        if isinstance(deduct_type, dict) and "reply_markup" in deduct_type:
            reply_markup = deduct_type.get("reply_markup")
            deduct_type = deduct_type.get("deduct_type", "text")
        
        # 定义发送单个消息的函数
        async def send_single_message(idx, sentence):
            """发送单个消息"""
            if not sentence.strip():
                logger.debug(f"用户 {user_id} 跳过空句子，索引: {idx}")
                return
                
            try:
                if use_voice:
                    # 尝试生成并发送语音
                    try:
                        voice_file = await text_to_speech(sentence, logger, user_id)
                        if voice_file:
                            # 准备caption（限制长度）
                            caption = sentence[:1024]
                            if len(sentence) > 1024:
                                caption = sentence[:1020] + "..."
                                logger.warning(f"用户 {user_id} 的语音 caption 超长，已截断")
                            
                            # 发送语音消息
                            await send_with_retry(
                                bot,
                                bot.send_voice,
                                chat_id=chat_id,
                                voice=voice_file,
                                caption=caption,
                                reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None,
                                user_id=user_id
                            )
                            logger.info(f"用户 {user_id} 语音消息 {idx + 1} 发送成功")
                        else:
                            # 语音生成失败，回退到文字消息
                            logger.warning(f"用户 {user_id} 语音生成失败，回退到文字消息")
                            await send_with_retry(
                                bot, 
                                bot.send_message,
                                chat_id=chat_id,
                                text=sentence,
                                reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None,
                                user_id=user_id
                            )
                            logger.info(f"用户 {user_id} 回退文字消息 {idx + 1} 发送成功")
                    except Exception as voice_error:
                        logger.error(f"用户 {user_id} 语音处理失败: {str(voice_error)}")
                        # 语音处理失败，回退到文字消息
                        await send_with_retry(
                            bot, 
                            bot.send_message,
                            chat_id=chat_id,
                            text=sentence,
                            reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None,
                            user_id=user_id
                        )
                        logger.info(f"用户 {user_id} 回退文字消息 {idx + 1} 发送成功")
                else:
                    # 发送文字消息，最后一条消息时添加reply_markup（如果有）
                    is_last_message = idx == len(response_list) - 1
                    kwargs = {
                        "chat_id": chat_id,
                        "text": sentence,
                        "reply_to_message_id": update.effective_message.message_id if is_group and update and update.effective_message else None,
                        "user_id": user_id
                    }
                    
                    # 只在最后一条消息添加reply_markup
                    if is_last_message and reply_markup:
                        kwargs["reply_markup"] = reply_markup
                    
                    await send_with_retry(
                        bot, 
                        bot.send_message,
                        **kwargs
                    )
                    logger.info(f"用户 {user_id} 文字消息 {idx + 1} 发送成功")
                
            except Forbidden:
                logger.error(f"用户 {user_id} 已阻止机器人或删除对话")
                # 转发错误信息给管理员
                try:
                    await bot.send_message(
                        chat_id=MASTER_ID,
                        text=f"用户 {formatted_user} 已阻止机器人或删除对话"
                    )
                except:
                    pass
                raise
            except Exception as e:
                logger.error(f"用户 {user_id} 发送消息失败: {str(e)}")
                raise
        
        # 发送每个响应
        for idx, sentence in enumerate(response_list):
            if sentence.strip():
                await send_single_message(idx, sentence)
                
                # 句子间停顿
                if idx < len(response_list) - 1:
                    await asyncio.sleep(random.uniform(0.5, 1.5))
        
        # 停止typing状态
        if typing_event:
            typing_event.set()
        
        # 只有在没有使用充值按钮时才发送快捷回复菜单
        if not reply_markup:
            try:
                from database import get_user_data
                user_data = await get_user_data(user_id)
                
                # 检查是否启用快捷回复
                if user_data.get("enable_quick_reply", False):
                    quick_reply_menu = get_quick_reply_menu(user_data.get("preferred_language", "zh-cn"))
                    await send_with_retry(
                        bot,
                        bot.send_message,
                        chat_id=chat_id,
                        text="快捷回复：",
                        reply_markup=quick_reply_menu,
                        user_id=user_id
                    )
            except Exception as menu_error:
                logger.error(f"用户 {user_id} 发送快捷回复菜单失败: {str(menu_error)}")
        
        # 转发完整回复给管理员
        try:
            full_response = "\n".join(response_list)
            admin_message = f"用户: {formatted_user}\n类型: {'语音' if use_voice else '文字'}\n聊天: {chat_id}\n答复: {full_response}"

            # 尝试获取转发消息ID以便引用
            reply_to_message_id = None
            if update and hasattr(update, 'effective_message') and update.effective_message:
                try:
                    from utils import get_forwarded_message_id
                    original_chat_id = update.effective_message.chat.id if hasattr(update.effective_message, 'chat') else chat_id
                    original_message_id = update.effective_message.message_id
                    reply_to_message_id = await get_forwarded_message_id(original_chat_id, original_message_id)
                    if reply_to_message_id:
                        logger.debug(f"找到转发消息ID {reply_to_message_id}，将引用该消息")
                except Exception as ref_error:
                    logger.debug(f"获取转发消息ID失败，将正常发送不引用: {str(ref_error)}")

            # 发送AI答复给管理员（如果有转发消息则引用）
            await bot.send_message(
                chat_id=MASTER_ID,
                text=admin_message,
                reply_to_message_id=reply_to_message_id
            )
            logger.info(f"完整AI回复转发给管理员，用户ID: {user_id}，引用消息ID: {reply_to_message_id}")
        except Exception as e:
            logger.error(f"转发完整回复给管理员失败，用户ID: {user_id}, 错误: {str(e)}")
            # 如果引用失败，尝试不引用重新发送
            if reply_to_message_id:
                try:
                    await bot.send_message(
                        chat_id=MASTER_ID,
                        text=admin_message
                    )
                    logger.info(f"AI回复重新发送给管理员成功（无引用），用户ID: {user_id}")
                except Exception as retry_error:
                    logger.error(f"重新发送AI回复给管理员也失败，用户ID: {user_id}, 错误: {str(retry_error)}")
        
        return True
        
    except Exception as e:
        logger.error(f"发送响应失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        
        # 转发错误信息给管理员
        try:
            await bot.send_message(
                chat_id=MASTER_ID,
                text=f"AI回复发送失败 - 用户: {username or user_id}\n错误: {str(e)}"
            )
            logger.info(f"错误信息转发给管理员，用户ID: {user_id}")
        except Exception as admin_error:
            logger.error(f"转发错误信息给管理员失败: {str(admin_error)}")
        
        if typing_event:
            typing_event.set()
            
        return False 