"""
工作线程管理模块 - 负责管理异步任务和工作线程
"""

import asyncio
import time
from typing import Dict, Set
from logging_config import logger
from ai_processing_status import ai_processing_status
from config import MASTER_ID


class TaskManager:
    """
    异步任务管理器
    负责管理和监控所有异步任务的生命周期
    """
    
    def __init__(self):
        self.tasks: Set[asyncio.Task] = set()
        self.task_timeouts: Dict[asyncio.Task, float] = {}
    
    def add_task(self, coro, timeout=120):  # 延长超时时间到 120 秒
        """
        添加异步任务到管理器
        """
        task = asyncio.create_task(coro)
        self.tasks.add(task)
        self.task_timeouts[task] = time.time() + timeout
        
        # 添加完成回调
        task.add_done_callback(lambda t: self._remove_task(t, timeout))
        return task
    
    def _remove_task(self, task, timeout):
        """
        从管理器中移除已完成的任务
        """
        try:
            self.tasks.discard(task)
            self.task_timeouts.pop(task, None)
            
            if task.cancelled():
                logger.info(f"任务被取消: {task}")
            elif task.exception():
                logger.error(f"任务异常: {task.exception()}")
            else:
                logger.debug(f"任务正常完成: {task}")
                
        except Exception as e:
            logger.error(f"移除任务失败: {str(e)}")
    
    async def shutdown(self):
        """
        关闭任务管理器，取消所有未完成的任务
        """
        logger.info(f"开始关闭任务管理器，待处理任务数: {len(self.tasks)}")
        
        # 取消所有任务
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # 等待所有任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        self.tasks.clear()
        self.task_timeouts.clear()
        logger.info("任务管理器关闭完成")
    
    def get_active_tasks_count(self):
        """
        获取当前活跃任务数量
        """
        return len([task for task in self.tasks if not task.done()])


# 全局任务管理器实例
task_manager = TaskManager()


async def ai_worker(bot, ai_queue, logger, worker_id):
    """
    传统AI工作线程
    处理普通的AI对话请求
    """
    logger.info(f"AI worker {worker_id} 启动")
    
    while True:
        try:
            # 获取任务
            item = await ai_queue.get()
            
            if item is None:  # 停止信号
                logger.info(f"AI worker {worker_id} 收到停止信号")
                break
            
            # 兼容元组和字典两种格式
            if isinstance(item, tuple):
                # 元组格式: (user_id, message, chat_id, use_voice, username, [可选] message_id)
                if len(item) >= 6:
                    user_id, message, chat_id, use_voice, username, original_message_id = item[:6]
                else:
                    user_id, message, chat_id, use_voice, username = item[:5]
                    original_message_id = None
                deduct_type = "voice" if use_voice else "text"
            else:
                # 字典格式
                user_id = item["user_id"]
                message = item["message"] 
                chat_id = item["chat_id"]
                deduct_type = item.get("deduct_type", "text")
                username = item.get("username")
                use_voice = item.get("use_voice", False)
                original_message_id = item.get("message_id")
            
            logger.info(f"AI worker {worker_id} 开始处理用户 {user_id} 的请求")
            
            # 创建模拟的Update对象
            class MockUpdate:
                def __init__(self, chat_id, user_id, message_id):
                    self.effective_chat = type('obj', (object,), {'id': chat_id})
                    self.effective_user = type('obj', (object,), {'id': user_id})
                    # 创建包含chat属性的effective_message对象
                    chat_obj = type('obj', (object,), {'id': chat_id})
                    self.effective_message = type('obj', (object,), {
                        'message_id': message_id,
                        'chat': chat_obj
                    })
            
            # 如果是元组格式且长度为6，则第6个元素是原始消息ID
            original_message_id = None
            if isinstance(item, tuple) and len(item) > 5:
                original_message_id = item[5]
            
            # 使用原始消息ID（如果有）或者生成一个时间戳ID
            message_id = original_message_id if original_message_id else int(time.time())
            update = MockUpdate(chat_id, user_id, message_id)
            
            # 创建typing事件
            typing_event = asyncio.Event()
            typing_task = None
            
            try:
                # 检查用户是否正在处理中
                if await ai_processing_status.is_user_processing(user_id):
                    logger.warning(f"用户 {user_id} 正在处理中，跳过请求")
                    continue
                
                # 标记用户为处理中
                await ai_processing_status.set_user_processing(user_id)
                
                # 启动typing状态
                from .response_sender import keep_typing_alive
                typing_task = task_manager.add_task(
                    keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                )
                
                # 处理AI对话
                from .chat_processor import chat_with_ai
                responses, final_deduct_type = await chat_with_ai(
                    user_id, message, deduct_type, logger, bot
                )
                
                # 发送响应
                from .response_sender import send_response
                response_success = await send_response(
                    bot, chat_id, user_id, (responses, final_deduct_type), 
                    use_voice, username, typing_event, update
                )
                
                # 如果响应列表为空导致发送失败，尝试切换到下一个API重试
                if not response_success:
                    logger.warning(f"用户 {user_id} 响应发送失败，尝试切换到下一个API重试")
                    # 重置typing状态以便重新发送
                    if typing_event.is_set():
                        typing_event.clear()
                    if typing_task:
                        typing_task.cancel()
                        
                    typing_task = task_manager.add_task(
                        keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                    )
                    
                    # 导入API管理器并切换到下一个API
                    from .api_manager import call_ai_api_with_fallback
                    retry_responses, retry_deduct_type = await chat_with_ai(
                        user_id, message, deduct_type, logger, bot
                    )
                    
                    # 再次尝试发送响应
                    await send_response(
                        bot, chat_id, user_id, (retry_responses, retry_deduct_type),
                        use_voice, username, typing_event, update
                    )
                
            except Exception as e:
                logger.error(f"AI worker {worker_id} 处理失败: {str(e)}", exc_info=True)
                
                # 发送错误消息
                try:
                    from database import get_user_data
                    user_data = await get_user_data(user_id)
                    lang = user_data.get("preferred_language", "zh-cn")
                    error_msg = (
                        "贱奴！爷现在心情不好，处理你的请求时出了点问题，稍后再试！"
                        if lang == "zh-cn" else 
                        "Filthy dog! I'm in a bad mood, there was an issue processing your request, try again later!"
                    )
                    
                    from .response_sender import send_response
                    await send_response(bot, chat_id, user_id, ([error_msg], deduct_type), use_voice, username, typing_event, update)
                except Exception as send_error:
                    logger.error(f"AI worker {worker_id} 发送错误消息失败: {str(send_error)}")
            
            finally:
                # 清除用户处理状态
                await ai_processing_status.clear_user_processing(user_id)
                
                # 停止typing状态
                if typing_event:
                    typing_event.set()
                if typing_task:
                    typing_task.cancel()
                
                ai_queue.task_done()
                logger.debug(f"AI worker {worker_id} 处理完成，用户 {user_id}, 活跃任务数: {task_manager.get_active_tasks_count()}")

        except asyncio.CancelledError:
            logger.info(f"AI worker {worker_id} 被取消")
            break
        except Exception as e:
            logger.error(f"AI worker {worker_id} 发生未知错误: {str(e)}", exc_info=True)
            await asyncio.sleep(1)


async def ai_worker_streaming(bot, ai_queue, logger, worker_id):
    """
    流式AI工作线程
    处理流式AI对话请求
    """
    logger.info(f"流式AI worker {worker_id} 启动")
    
    while True:
        try:
            # 获取任务
            item = await ai_queue.get()
            
            if item is None:  # 停止信号
                logger.info(f"流式AI worker {worker_id} 收到停止信号")
                break
            
            # 兼容元组和字典两种格式
            if isinstance(item, tuple):
                # 元组格式: (user_id, message, chat_id, use_voice, username, [可选] message_id)
                if len(item) >= 6:
                    user_id, message, chat_id, use_voice, username, original_message_id = item[:6]
                else:
                    user_id, message, chat_id, use_voice, username = item[:5]
                    original_message_id = None
                deduct_type = "voice" if use_voice else "text"
            else:
                # 字典格式
                user_id = item["user_id"]
                message = item["message"]
                chat_id = item["chat_id"]
                deduct_type = item.get("deduct_type", "text")
                username = item.get("username")
                use_voice = item.get("use_voice", False)
                original_message_id = item.get("message_id")
            
            logger.info(f"流式AI worker {worker_id} 开始处理用户 {user_id} 的请求")
            
            # 创建模拟的Update对象
            class MockUpdate:
                def __init__(self, chat_id, user_id, message_id):
                    self.effective_chat = type('obj', (object,), {'id': chat_id})
                    self.effective_user = type('obj', (object,), {'id': user_id})
                    # 创建包含chat属性的effective_message对象
                    chat_obj = type('obj', (object,), {'id': chat_id})
                    self.effective_message = type('obj', (object,), {
                        'message_id': message_id,
                        'chat': chat_obj
                    })
            
            # 如果是元组格式且长度为6，则第6个元素是原始消息ID
            original_message_id = None
            if isinstance(item, tuple) and len(item) > 5:
                original_message_id = item[5]
            
            # 使用原始消息ID（如果有）或者生成一个时间戳ID
            message_id = original_message_id if original_message_id else int(time.time())
            update = MockUpdate(chat_id, user_id, message_id)
            
            # 创建typing事件
            typing_event = asyncio.Event()
            typing_task = None
            
            try:
                # 检查用户是否正在处理中
                if await ai_processing_status.is_user_processing(user_id):
                    logger.warning(f"用户 {user_id} 正在处理中，跳过请求")
                    continue
                
                # 标记用户为处理中
                await ai_processing_status.set_user_processing(user_id)
                
                # 启动typing状态
                from .response_sender import keep_typing_alive
                typing_task = task_manager.add_task(
                    keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                )
                
                # 处理流式AI对话
                from .chat_processor import chat_with_ai_streaming
                responses, final_deduct_type = await chat_with_ai_streaming(
                    user_id, message, deduct_type, logger, bot
                )
                
                # 发送响应
                from .response_sender import send_response
                response_success = await send_response(
                    bot, chat_id, user_id, (responses, final_deduct_type), 
                    use_voice, username, typing_event, update
                )
                
                # 如果响应列表为空导致发送失败，尝试切换到下一个API重试
                if not response_success:
                    logger.warning(f"用户 {user_id} 响应发送失败，尝试切换到下一个API重试")
                    # 重置typing状态以便重新发送
                    if typing_event.is_set():
                        typing_event.clear()
                    if typing_task:
                        typing_task.cancel()
                        
                    typing_task = task_manager.add_task(
                        keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                    )
                    
                    # 导入API管理器并切换到下一个API
                    from .api_manager import call_ai_api_with_fallback
                    retry_responses, retry_deduct_type = await chat_with_ai_streaming(
                        user_id, message, deduct_type, logger, bot
                    )
                    
                    # 再次尝试发送响应
                    await send_response(
                        bot, chat_id, user_id, (retry_responses, retry_deduct_type),
                        use_voice, username, typing_event, update
                    )
                
            except Exception as e:
                logger.error(f"流式AI worker {worker_id} 处理失败: {str(e)}", exc_info=True)
                
                # 发送错误消息
                try:
                    from database import get_user_data
                    user_data = await get_user_data(user_id)
                    lang = user_data.get("preferred_language", "zh-cn")
                    error_msg = (
                        "贱奴！爷现在心情不好，处理你的请求时出了点问题，稍后再试！"
                        if lang == "zh-cn" else 
                        "Filthy dog! I'm in a bad mood, there was an issue processing your request, try again later!"
                    )
                    
                    from .response_sender import send_response
                    await send_response(bot, chat_id, user_id, ([error_msg], deduct_type), use_voice, username, typing_event, update)
                except Exception as send_error:
                    logger.error(f"流式AI worker {worker_id} 发送错误消息失败: {str(send_error)}")
            
            finally:
                # 清除用户处理状态
                await ai_processing_status.clear_user_processing(user_id)
                
                # 停止typing状态
                if typing_event:
                    typing_event.set()
                if typing_task:
                    typing_task.cancel()
                
                ai_queue.task_done()
                logger.debug(f"流式AI worker {worker_id} 处理完成，用户 {user_id}, 活跃任务数: {task_manager.get_active_tasks_count()}")

        except asyncio.CancelledError:
            logger.info(f"流式AI worker {worker_id} 被取消")
            break
        except Exception as e:
            logger.error(f"流式AI worker {worker_id} 发生未知错误: {str(e)}", exc_info=True)
            await asyncio.sleep(1)


async def ai_worker_unified(bot, ai_queue, logger, worker_id):
    """
    统一AI工作线程
    根据配置自动选择处理方式
    """
    logger.info(f"统一AI worker {worker_id} 启动")
    
    while True:
        try:
            # 获取任务
            item = await ai_queue.get()
            
            if item is None:  # 停止信号
                logger.info(f"统一AI worker {worker_id} 收到停止信号")
                break
            
            # 兼容元组和字典两种格式
            if isinstance(item, tuple):
                # 元组格式: (user_id, message, chat_id, use_voice, username, [可选] message_id)
                if len(item) >= 6:
                    user_id, message, chat_id, use_voice, username, original_message_id = item[:6]
                else:
                    user_id, message, chat_id, use_voice, username = item[:5]
                    original_message_id = None
                deduct_type = "voice" if use_voice else "text"
            else:
                # 字典格式
                user_id = item["user_id"]
                message = item["message"]
                chat_id = item["chat_id"]
                deduct_type = item.get("deduct_type", "text")
                username = item.get("username")
                use_voice = item.get("use_voice", False)
                original_message_id = item.get("message_id")
            
            logger.info(f"统一AI worker {worker_id} 开始处理用户 {user_id} 的请求")
            
            # 创建模拟的Update对象
            class MockUpdate:
                def __init__(self, chat_id, user_id, message_id):
                    self.effective_chat = type('obj', (object,), {'id': chat_id})
                    self.effective_user = type('obj', (object,), {'id': user_id})
                    # 创建包含chat属性的effective_message对象
                    chat_obj = type('obj', (object,), {'id': chat_id})
                    self.effective_message = type('obj', (object,), {
                        'message_id': message_id,
                        'chat': chat_obj
                    })
            
            # 如果是元组格式且长度为6，则第6个元素是原始消息ID
            original_message_id = None
            if isinstance(item, tuple) and len(item) > 5:
                original_message_id = item[5]
            
            # 使用原始消息ID（如果有）或者生成一个时间戳ID
            message_id = original_message_id if original_message_id else int(time.time())
            update = MockUpdate(chat_id, user_id, message_id)
            
            # 创建typing事件
            typing_event = asyncio.Event()
            typing_task = None
            
            try:
                # 检查用户是否正在处理中
                if await ai_processing_status.is_user_processing(user_id):
                    logger.warning(f"用户 {user_id} 正在处理中，跳过请求")
                    continue
                
                # 标记用户为处理中
                await ai_processing_status.set_user_processing(user_id)
                
                # 启动typing状态
                from .response_sender import keep_typing_alive
                typing_task = task_manager.add_task(
                    keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                )
                
                # 处理统一AI对话
                from .chat_processor import chat_with_ai_unified
                responses, final_deduct_type = await chat_with_ai_unified(
                    user_id, message, deduct_type, logger, bot
                )
                
                # 发送响应
                from .response_sender import send_response
                response_success = await send_response(
                    bot, chat_id, user_id, (responses, final_deduct_type), 
                    use_voice, username, typing_event, update
                )
                
                # 如果响应列表为空导致发送失败，尝试切换到下一个API重试
                if not response_success:
                    # 检查用户是否已拉黑Bot
                    from database import get_user_data
                    user_data = await get_user_data(user_id)
                    
                    # 如果用户已拉黑Bot，不进行API重试
                    if user_data.get("is_blocked", 0) == 1:
                        logger.info(f"用户 {user_id} 已拉黑Bot，不进行API重试")
                        return
                        
                    logger.warning(f"用户 {user_id} 响应发送失败，尝试切换到下一个API重试")
                    # 重置typing状态以便重新发送
                    if typing_event.is_set():
                        typing_event.clear()
                    if typing_task:
                        typing_task.cancel()
                        
                    typing_task = task_manager.add_task(
                        keep_typing_alive(bot, chat_id, user_id, use_voice, typing_event, logger)
                    )
                    
                    # 导入API管理器并确保切换到下一个API
                    from .api_manager import call_ai_api_streaming_with_fallback, call_ai_api_with_fallback
                    
                    # 手动切换到下一个API
                    # 获取API配置列表
                    from config import AI_API_CONFIGS
                    
                    # 添加调试日志，记录尝试使用下一个API
                    logger.info(f"用户 {user_id} 尝试使用下一个API重试对话")
                    
                    # 重试对话
                    retry_responses, retry_deduct_type = await chat_with_ai_unified(
                        user_id, message, deduct_type, logger, bot
                    )
                    
                    # 再次尝试发送响应
                    await send_response(
                        bot, chat_id, user_id, (retry_responses, retry_deduct_type),
                        use_voice, username, typing_event, update
                    )
                
            except Exception as e:
                logger.error(f"统一AI worker {worker_id} 处理失败: {str(e)}", exc_info=True)
                
                # 发送错误消息
                try:
                    from database import get_user_data
                    user_data = await get_user_data(user_id)
                    lang = user_data.get("preferred_language", "zh-cn")
                    error_msg = (
                        "贱奴！爷现在心情不好，处理你的请求时出了点问题，稍后再试！"
                        if lang == "zh-cn" else 
                        "Filthy dog! I'm in a bad mood, there was an issue processing your request, try again later!"
                    )
                    
                    from .response_sender import send_response
                    await send_response(bot, chat_id, user_id, ([error_msg], deduct_type), use_voice, username, typing_event, update)
                except Exception as send_error:
                    logger.error(f"统一AI worker {worker_id} 发送错误消息失败: {str(send_error)}")
            
            finally:
                # 清除用户处理状态
                await ai_processing_status.clear_user_processing(user_id)
                
                # 停止typing状态
                if typing_event:
                    typing_event.set()
                if typing_task:
                    typing_task.cancel()
                
                ai_queue.task_done()
                logger.debug(f"统一AI worker {worker_id} 处理完成，用户 {user_id}, 活跃任务数: {task_manager.get_active_tasks_count()}")

        except asyncio.CancelledError:
            logger.info(f"统一AI worker {worker_id} 被取消")
            break
        except Exception as e:
            logger.error(f"统一AI worker {worker_id} 发生未知错误: {str(e)}", exc_info=True)
            await asyncio.sleep(1) 