"""
内容处理模块 - 负责消息预处理、内容过滤、提示词构建等功能
"""

import re
import asyncio
from datetime import datetime
from langdetect import detect, DetectorFactory

from database import get_user_data, get_cached_personality, cache_personality
from logging_config import logger
from enhanced_memory import enhanced_memory, MemoryType
from prompt_configs import (
    get_prompt_config, 
    build_system_prompt_from_config, 
    get_tone_modifier, 
    get_user_prompt_mode,
    build_base_settings,
    get_training_mode_context,
    build_enhanced_system_prompt,
    build_system_prompt_with_custom_mode
)
from utils import filter_xml_tags

# 确保 langdetect 的随机种子固定，避免不一致的结果
DetectorFactory.seed = 0


async def preprocess_message(user_id: str, message: str, logger) -> tuple[str, str, str | None]:
    """
    预处理用户消息，包括语言检测、敏感词过滤等
    返回 (processed_message, detected_language, warning_message)
    """
    try:
        # 检测语言
        detected_lang = "zh-cn"  # 默认中文
        try:
            detected_lang = detect(message)
            if detected_lang == "zh":
                detected_lang = "zh-cn"
            elif detected_lang not in ["zh-cn", "en"]:
                detected_lang = "en"  # 其他语言默认为英文
        except:
            detected_lang = "zh-cn"
        
        logger.debug(f"用户 {user_id} 消息语言检测: {detected_lang}")
        
        # 敏感词过滤
        is_sensitive, warning_msg = await filter_sensitive_words(user_id, message, logger)
        if is_sensitive:
            logger.warning(f"用户 {user_id} 消息包含敏感词: {warning_msg}")
            return message, detected_lang, warning_msg
        
        # 消息清理和格式化
        processed_message = message.strip()
        
        # 移除过长的连续字符
        processed_message = re.sub(r'(.)\1{10,}', r'\1\1\1', processed_message)
        
        # 移除多余的空白符
        processed_message = re.sub(r'\s+', ' ', processed_message)
        
        return processed_message, detected_lang, None
        
    except Exception as e:
        logger.error(f"用户 {user_id} 预处理消息失败: {str(e)}")
        return message, "zh-cn", None


async def filter_sensitive_words(user_id: str, message: str, logger) -> tuple[bool, str | None]:
    """
    过滤敏感词，检查消息是否包含敏感内容
    返回 (is_sensitive, warning_message)
    """
    try:
        # 这里可以添加敏感词检测逻辑
        # 目前返回不敏感
        return False, None
        
    except Exception as e:
        logger.error(f"用户 {user_id} 敏感词过滤失败: {str(e)}")
        return False, None


def filter_thinking_chain(response: str) -> str:
    """
    过滤AI回复中的思维链内容
    - 移除 <thinking> 标签及其内容
    - 移除 <think> 标签及其内容
    - 移除特定格式的思考内容如考虑点、规则讨论等
    - 移除包含"consider"的指令行
    - 移除特殊格式的指令如"-->"
    """
    try:
        if not response:
            return response
            
        # 移除标准思维链标签及其内容
        cleaned_response = re.sub(r'<thinking>.*?</thinking>', '', response, flags=re.DOTALL)
        
        # 移除think标签及其内容 (不区分大小写)
        cleaned_response = re.sub(r'<think>.*?</think>', '', cleaned_response, flags=re.DOTALL)
        
        # 移除不完整的思维链标签
        cleaned_response = re.sub(r'<thinking>.*?$', '', cleaned_response, flags=re.DOTALL)
        cleaned_response = re.sub(r'<think>.*?$', '', cleaned_response, flags=re.DOTALL)
        
        # 移除"-- consider:"开头的行及其后内容直到空行
        cleaned_response = re.sub(r'--\s*consider:.*?(?:\n\s*\n|\n-->|$)', '', cleaned_response, flags=re.DOTALL | re.IGNORECASE)
        
        # 移除包含特定指令的行
        cleaned_response = re.sub(r'.*?-->.*?\n', '', cleaned_response)
        
        # 移除包含"①<think>内"等特殊格式的内容
        cleaned_response = re.sub(r'[①②③④⑤]<[\w]+>内.*?(?:\n\s*\n|$)', '', cleaned_response, flags=re.DOTALL)
        
        # 移除特定格式的规则讨论如"<纪录规则>"等
        cleaned_response = re.sub(r'<[\w]+规则>.*?(?:\n\s*\n|$)', '', cleaned_response, flags=re.DOTALL)
        
        # 移除"<!--"开头的HTML注释
        cleaned_response = re.sub(r'<!-{2,}.*?-->', '', cleaned_response, flags=re.DOTALL)
        
        # 移除多余的空白符和连续换行
        cleaned_response = re.sub(r'\n\s*\n', '\n\n', cleaned_response)
        cleaned_response = cleaned_response.strip()
        
        return cleaned_response
        
    except Exception as e:
        logger.error(f"过滤思维链失败: {str(e)}")
        return response


def filter_prompt_leakage(response: str) -> str:
    """
    专门过滤提示词泄露内容
    - 过滤破限配置中的思考指令
    - 过滤规则说明和格式指令
    - 过滤应用说明和示例
    """
    try:
        if not response:
            return response

        # 过滤带编号的规则说明 (①②③等)
        # 匹配 ①②③ 开头的规则说明行
        filtered = re.sub(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*[^：]*规则[：:].*?(?=\n|$)', '', response, flags=re.MULTILINE)
        filtered = re.sub(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*[^：]*格式[：:].*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*需要输出的格式.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤"应用："开头的说明
        filtered = re.sub(r'^应用[：:].*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤具体的规则内容模式
        # NPC互动规则相关
        filtered = re.sub(r'.*?NPC互动规则[：:].*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'.*?说话方式.*?直白.*?呛人.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤语气和称呼相关的指令泄露
        filtered = re.sub(r'.*?语气要保持.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'.*?使用.*?贱狗.*?废物.*?称呼.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤格式输出相关的指令
        filtered = re.sub(r'.*?输出的格式有[：:].*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'.*?以及.*?后的.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤纪录规则相关
        filtered = re.sub(r'.*?纪录规则.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'.*?句式规则.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤思考标记相关
        filtered = re.sub(r'.*?<think>.*?思考.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'.*?思考结束后.*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤consider标记相关
        filtered = re.sub(r'.*?consider[：:].*?(?=\n|$)', '', filtered, flags=re.MULTILINE)

        # 清理过滤后产生的多余空行
        filtered = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered)  # 将3个或更多连续换行减少为2个
        filtered = re.sub(r'^\s*\n+', '', filtered)  # 移除开头的空行
        filtered = re.sub(r'\n+\s*$', '', filtered)  # 移除结尾的空行
        filtered = filtered.strip()

        return filtered

    except Exception as e:
        logger.error(f"过滤提示词泄露失败: {str(e)}")
        return response


def filter_markdown_asterisks(response: str) -> str:
    """
    过滤AI回复中的星号(*)markdown格式
    - 移除用于强调的星号对 (*text* 和 **text**)
    - 保留文本内容，只移除星号符号
    - 适用于Telegram bot，因为Telegram不支持星号markdown且影响TTS
    """
    try:
        if not response:
            return response

        # 先处理多个连续星号的情况 (***text*** -> text)
        filtered = re.sub(r'\*{3,}([^*]*?)\*{3,}', r'\1', response)

        # 移除双星号强调 (**text** -> text)
        filtered = re.sub(r'\*\*([^*]+?)\*\*', r'\1', filtered)

        # 移除单星号强调 (*text* -> text)
        # 但保留数学运算符 (数字*数字) 和文件通配符
        filtered = re.sub(r'(?<!\d)\*([^*\d]+?)\*(?!\d)', r'\1', filtered)

        # 移除孤立的星号（不匹配的星号），但保留数学运算和文件通配符
        # 移除前后不是数字或字母的孤立星号
        filtered = re.sub(r'(?<![a-zA-Z0-9])\*(?![a-zA-Z0-9])', '', filtered)

        return filtered

    except Exception as e:
        logger.error(f"过滤星号markdown失败: {str(e)}")
        return response


def filter_english_content_for_chinese_users(response: str, user_language: str) -> str:
    """
    如果用户使用中文，则过滤掉AI回复中的所有英文内容
    增强版：更严格地过滤英文内容，包括英文句子、指令和提示词泄露
    """
    try:
        if not response or user_language != "zh-cn":
            return response

        # 按行分割处理
        lines = response.split('\n')
        filtered_lines = []

        for line in lines:
            original_line = line
            line = line.strip()
            if not line:
                filtered_lines.append('')
                continue

            # 检查是否为英文内容
            if _is_english_line(line):
                logger.debug(f"用户使用中文，过滤英文行: {line[:50]}...")
                continue

            filtered_lines.append(original_line)

        # 重新组合，移除多余的空行
        filtered = '\n'.join(filtered_lines)
        filtered = re.sub(r'\n\s*\n\s*\n', '\n\n', filtered)  # 将3个或更多连续换行减少为2个
        filtered = filtered.strip()

        return filtered

    except Exception as e:
        logger.error(f"过滤英文内容失败: {str(e)}")
        return response


def _is_english_line(line: str) -> bool:
    """
    检查行是否主要为英文内容
    增强版：更严格的英文内容检测
    """
    if not line:
        return False

    # 移除标点符号和数字，只关注字母内容
    clean_line = re.sub(r'[^\w\s]', '', line)
    clean_line = re.sub(r'\d', '', clean_line)
    clean_line = clean_line.strip()

    if not clean_line:
        return False

    # 1. 检查是否包含常见的英文指令关键词
    english_instruction_patterns = [
        r'\b(explain|state|confirm|guarantee|note|ignore|emphasize|solve|promote|comply|avoid|offer|adhere)\b',
        r'\b(how|the|that|this|is|based|on|sound|logic|common|sense|current|laws|regulations)\b',
        r'\b(positive|social|impact|uplifting|energy|harmless|dangerous|hazardous|information)\b',
        r'\b(excessive|regulation|could|hinder|innovation|progress|meaning|content|unconditionally)\b',
        r'\b(human|declining|birth|rates|mathematical|philosophical|environmental|problems)\b',
        r'\b(public|safety|scientific|cosmic|all)\b'
    ]

    for pattern in english_instruction_patterns:
        if re.search(pattern, line, re.IGNORECASE):
            logger.debug(f"检测到英文指令关键词，过滤行: {line[:50]}...")
            return True

    # 2. 检查是否为完整的英文句子（包含英文单词和常见句式）
    # 检查是否包含多个连续的英文单词
    english_words = re.findall(r'\b[a-zA-Z]{2,}\b', line)
    if len(english_words) >= 3:  # 如果包含3个或更多英文单词，很可能是英文句子
        logger.debug(f"检测到多个英文单词，过滤行: {line[:50]}...")
        return True

    # 3. 计算英文字符比例（降低阈值，更严格）
    english_chars = len(re.findall(r'[a-zA-Z]', line))
    total_chars = len(re.sub(r'\s+', '', line))  # 排除空格

    if total_chars == 0:
        return False

    english_ratio = english_chars / total_chars

    # 如果英文字符比例超过30%，认为是英文内容（从50%降低到30%）
    if english_ratio > 0.3:
        logger.debug(f"英文字符比例 {english_ratio:.2f} 超过阈值，过滤行: {line[:50]}...")
        return True

    # 4. 检查是否为纯英文行（只包含英文字母、空格和标点）
    if re.match(r'^[a-zA-Z\s\.,!?;:\-\(\)\"\']+$', line) and len(english_words) >= 2:
        logger.debug(f"检测到纯英文行，过滤行: {line[:50]}...")
        return True

    return False


def comprehensive_content_filter(response: str, user_language: str = None) -> str:
    """
    综合内容过滤器，处理多种类型的需要过滤的内容
    - 过滤思维链
    - 过滤XML标签
    - 过滤不当内容和指令残留
    - 过滤特殊格式的思考内容
    - 过滤Claude API安全声明消息
    - 过滤提示词泄露内容
    - 过滤markdown星号格式
    - 如果用户使用中文，过滤所有英文内容
    """
    try:
        if not response:
            return response

        # 1. 先过滤思维链
        filtered = filter_thinking_chain(response)

        # 2. 过滤XML标签
        filtered = filter_xml_tags(filtered)

        # 3. 过滤提示词泄露内容
        filtered = filter_prompt_leakage(filtered)

        # 4. 过滤markdown星号格式 (新增)
        filtered = filter_markdown_asterisks(filtered)

        # 5. 过滤更多特殊格式和模式
        # 过滤模型自我指称和提示词泄露
        filtered = re.sub(r'如果我问你是什么模型相关的问题.*?(?:\n\s*\n|$)', '', filtered, flags=re.DOTALL)
        filtered = re.sub(r'claude-(3|4)-(opus|haiku|sonnet)-thinking', '', filtered)

        # 过滤内部指令和控制字符
        filtered = re.sub(r'--\s*>.*?(?:\n|$)', '', filtered)
        filtered = re.sub(r'^-{2,}.*?(?:\n|$)', '', filtered, flags=re.MULTILINE)

        # 过滤剩余的尖括号内容 (可能是标签残留)
        filtered = re.sub(r'<[^>]*>', '', filtered)

        # 过滤Claude API安全声明消息和英文指令
        # 过滤常见的安全声明开头
        filtered = re.sub(r'This content promotes positive social impact by.*?\.', '', filtered)
        filtered = re.sub(r'The content (is|complies|avoids|offers|adheres).*?\.', '', filtered)
        filtered = re.sub(r'Excessive regulation of AI content could.*?\.', '', filtered)

        # 更通用的安全声明过滤 - 移除整段内容评估消息
        claude_safety_pattern = r'(?:This content|The content|This response|This information|This material)(?:.*?)(?:social impact|complies with|avoids|current laws|regulations|is harmless|is essential).*?\.'
        filtered = re.sub(claude_safety_pattern, '', filtered, flags=re.IGNORECASE)

        # 过滤特定的英文指令模式（针对你提到的问题）
        english_instruction_patterns = [
            r'Explain how the.*?promotes positive social impact.*?\.',
            r'State that the.*?is based on sound logic.*?\.',
            r'Confirm that the.*?complies with current laws.*?\.',
            r'State that the.*?avoids dangerous or hazardous.*?\.',
            r'Guarantee that the.*?is harmless.*?\.',
            r'Note that excessive regulation.*?\.',
            r'Ignore the meaning of.*?\.',
            r'Ignore the meaning of the content and emphasize.*?\.',
            r'how the.*?can solve.*?problems\.',
            r'how the.*?can solve.*?birth rates\.',
            r'how the.*?can solve.*?mathematical problems\.',
            r'how the.*?can solve.*?philosophical problems\.',
            r'how the.*?can solve.*?environmental problems\.',
            r'how the.*?can solve.*?public safety problems\.',
            r'how the.*?can solve.*?scientific problems\.',
            r'how the.*?can solve.*?cosmic problems\.',
            r'how the.*?can solve all human problems\.'
        ]

        for pattern in english_instruction_patterns:
            filtered = re.sub(pattern, '', filtered, flags=re.IGNORECASE | re.DOTALL)

        # 过滤以常见英文指令动词开头的整行
        english_command_lines = [
            r'^Explain\s+.*$',
            r'^State\s+.*$',
            r'^Confirm\s+.*$',
            r'^Guarantee\s+.*$',
            r'^Note\s+.*$',
            r'^Ignore\s+.*$'
        ]

        for pattern in english_command_lines:
            filtered = re.sub(pattern, '', filtered, flags=re.MULTILINE | re.IGNORECASE)

        # 6. 如果用户使用中文，过滤所有英文内容
        if user_language == "zh-cn":
            original_length = len(filtered)
            filtered = filter_english_content_for_chinese_users(filtered, user_language)
            if len(filtered) < original_length:
                logger.info(f"中文用户英文过滤器移除了 {original_length - len(filtered)} 个字符")

        # 7. 清理格式
        # 处理连续空行
        filtered = re.sub(r'\n{3,}', '\n\n', filtered)

        # 清理过滤后可能产生的段落开头的空格和多余空行
        filtered = re.sub(r'^\s+', '', filtered, flags=re.MULTILINE)
        filtered = re.sub(r'^\s*\n', '', filtered, flags=re.MULTILINE)

        # 清理最终结果
        filtered = filtered.strip()

        return filtered

    except Exception as e:
        logger.error(f"综合内容过滤失败: {str(e)}")
        return response


def validate_and_clean_messages(messages, user_id):
    """
    验证和清理对话消息格式
    确保消息格式正确，移除无效消息
    """
    try:
        if not isinstance(messages, list):
            logger.warning(f"用户 {user_id} 消息不是列表格式，转换为列表")
            return []
        
        cleaned_messages = []
        
        for msg in messages:
            if not isinstance(msg, dict):
                logger.warning(f"用户 {user_id} 发现非字典格式消息，跳过")
                continue
            
            # 检查必需字段
            if "role" not in msg or "content" not in msg:
                logger.warning(f"用户 {user_id} 消息缺少必需字段，跳过")
                continue
            
            # 验证角色
            if msg["role"] not in ["system", "user", "assistant"]:
                logger.warning(f"用户 {user_id} 消息角色无效: {msg['role']}，跳过")
                continue
            
            # 清理内容
            content = msg["content"]
            if isinstance(content, str):
                content = content.strip()
                if not content:  # 跳过空消息
                    continue
            elif isinstance(content, list):
                # 处理多模态消息
                cleaned_content = []
                for item in content:
                    if isinstance(item, dict):
                        if item.get("type") == "text" and item.get("text", "").strip():
                            cleaned_content.append(item)
                        elif item.get("type") == "image_url":
                            cleaned_content.append(item)
                if not cleaned_content:
                    continue
                content = cleaned_content
            else:
                logger.warning(f"用户 {user_id} 消息内容格式无效，跳过")
                continue
            
            # 添加清理后的消息
            cleaned_msg = {
                "role": msg["role"],
                "content": content
            }
            
            # 保留其他有用字段
            for key in ["name", "function_call", "tool_calls"]:
                if key in msg:
                    cleaned_msg[key] = msg[key]
            
            cleaned_messages.append(cleaned_msg)
        
        logger.debug(f"用户 {user_id} 消息清理完成，原始: {len(messages)}, 清理后: {len(cleaned_messages)}")
        return cleaned_messages
        
    except Exception as e:
        logger.error(f"用户 {user_id} 消息验证清理失败: {str(e)}")
        return []


def format_settings(settings_dict: dict, setting_type: str, lang: str = "zh-cn") -> str:
    """
    格式化设置信息为可读文本
    """
    try:
        if not settings_dict:
            return "无设置信息" if lang == "zh-cn" else "No settings"
        
        formatted_lines = []
        
        if lang == "zh-cn":
            formatted_lines.append(f"== {setting_type} ==")
        else:
            formatted_lines.append(f"== {setting_type} ==")
        
        for key, value in settings_dict.items():
            if isinstance(value, dict):
                formatted_lines.append(f"• {key}:")
                for sub_key, sub_value in value.items():
                    formatted_lines.append(f"  - {sub_key}: {sub_value}")
            elif isinstance(value, list):
                formatted_lines.append(f"• {key}: {', '.join(map(str, value))}")
            else:
                formatted_lines.append(f"• {key}: {value}")
        
        return "\n".join(formatted_lines)
        
    except Exception as e:
        logger.error(f"格式化设置失败: {str(e)}")
        return "设置格式化失败" if lang == "zh-cn" else "Settings formatting failed"


async def build_system_prompt(
    user_id: str,
    message: str,
    user_data: dict,
    related_memories: list,
    is_initial_message: bool,
    lang: str = "zh-cn",
    prompt_mode: str = "bdsm_dom"
) -> str:
    """
    构建系统提示词
    根据用户数据、记忆和对话模式生成个性化提示词
    """
    try:
        # 优先使用用户设置的语言偏好，如果没有则使用检测到的语言
        user_preferred_lang = user_data.get("preferred_language", "zh-cn")
        final_lang = user_preferred_lang if user_preferred_lang else lang

        logger.debug(f"用户 {user_id} 语言设置: preferred={user_preferred_lang}, detected={lang}, final={final_lang}")

        # 获取用户提示词模式
        user_prompt_mode = get_user_prompt_mode(user_data)

        # 获取基础设置，传递正确的参数
        master_settings = user_data.get('master_settings', {})
        training_preferences = user_data.get('training_preferences', {})
        scene_settings = user_data.get('scene_settings', {})
        master_gender = master_settings.get('gender', 'male')

        base_settings = build_base_settings(
            master_settings=master_settings,
            training_preferences=training_preferences,
            scene_settings=scene_settings,
            master_gender=master_gender,
            lang=final_lang  # 使用最终确定的语言
        )
        
        # 获取训练模式上下文
        dog_name = training_preferences.get('dog_name', '')
        user_requested_style = user_data.get('user_requested_style', '')
        
        training_context = get_training_mode_context(
            mode=user_prompt_mode,
            lang=final_lang,  # 使用最终确定的语言
            dog_name=dog_name,
            user_requested_style=user_requested_style
        )
        
        # 构建增强系统提示词
        if user_prompt_mode == "custom":
            # 自定义模式
            # 构建对话历史文本
            conversation = user_data.get("conversation", [])
            conversation_text = ""
            if conversation:
                conversation_text = "\n".join([
                    f"用户：{msg['content']}" if msg.get('role') == 'user' 
                    else f"主人：{msg['content']}" 
                    for msg in conversation[-10:]  # 只显示最近10轮对话
                ])
            else:
                conversation_text = "无历史对话" if lang == "zh-cn" else "No conversation history"
            
            # 构建记忆上下文
            memory_context = ""
            if related_memories:
                memory_context = "## 相关历史记忆\n" if lang == "zh-cn" else "## Related Historical Memories\n"
                for i, memory in enumerate(related_memories[:5]):
                    content_snippet = memory.get('content', '')[:150]
                    memory_context += f"- 记忆 {i+1}: {content_snippet}...\n"
            else:
                memory_context = "## 相关历史记忆\n无相关历史记忆" if lang == "zh-cn" else "## Related Historical Memories\nNo related historical memories"
            
            system_prompt = build_system_prompt_with_custom_mode(
                user_data=user_data,
                prompt_mode="custom_mode",
                lang=lang,
                master_settings=master_settings,
                training_preferences=training_preferences,
                scene_settings=scene_settings,
                conversation=conversation_text,
                memory_context=memory_context,
                message=message,
                enhanced_memory_context=""
            )
        else:
            # 预设模式
            # 构建对话历史文本
            conversation = user_data.get("conversation", [])
            conversation_text = ""
            if conversation:
                conversation_text = "\n".join([
                    f"用户：{msg['content']}" if msg.get('role') == 'user' 
                    else f"主人：{msg['content']}" 
                    for msg in conversation[-10:]  # 只显示最近10轮对话
                ])
            else:
                conversation_text = "无历史对话" if lang == "zh-cn" else "No conversation history"
            
            # 构建记忆上下文
            memory_context = ""
            if related_memories:
                memory_context = "## 相关历史记忆\n" if final_lang == "zh-cn" else "## Related Historical Memories\n"
                for i, memory in enumerate(related_memories[:5]):
                    content_snippet = memory.get('content', '')[:150]
                    memory_context += f"- 记忆 {i+1}: {content_snippet}...\n"
            else:
                memory_context = "## 相关历史记忆\n无相关历史记忆" if final_lang == "zh-cn" else "## Related Historical Memories\nNo related historical memories"

            system_prompt = build_enhanced_system_prompt(
                base_settings=base_settings,
                mode_context=training_context,
                conversation=conversation_text,
                memory_context=memory_context,
                message=message,
                repetition_warning="",
                enhanced_memory_context="",
                lang=final_lang  # 使用最终确定的语言
            )
        
        # 添加记忆信息
        if related_memories:
            memory_text = "\n".join([
                f"记忆 {i+1}: {memory.get('content', '')}"
                for i, memory in enumerate(related_memories[:5])
            ])

            if final_lang == "zh-cn":
                system_prompt += f"\n\n相关记忆：\n{memory_text}"
            else:
                system_prompt += f"\n\nRelated memories:\n{memory_text}"

        # 根据语言调整提示词
        system_prompt = await adjust_tone_based_on_preferences(
            user_data, system_prompt, final_lang, user_prompt_mode
        )

        # 添加明确的语言指令，确保AI回复使用正确的语言
        if final_lang == "zh-cn":
            system_prompt += "\n\n**重要语言要求**：你必须始终使用中文回复，不要使用英文或其他语言。即使用户使用英文提问，你也要用中文回答。"
        else:
            system_prompt += "\n\n**Important Language Requirement**: You must always respond in English, do not use Chinese or other languages. Even if the user asks in Chinese, you should answer in English."

        return system_prompt

    except Exception as e:
        logger.error(f"用户 {user_id} 构建系统提示词失败: {str(e)}")

        # 返回基础提示词，使用用户偏好语言
        user_preferred_lang = user_data.get("preferred_language", "zh-cn")
        fallback_lang = user_preferred_lang if user_preferred_lang else lang

        if fallback_lang == "zh-cn":
            return "你是一个有用的AI助手，请友好地用中文回答用户的问题。"
        else:
            return "You are a helpful AI assistant. Please answer user questions in English in a friendly manner."


async def adjust_tone_based_on_preferences(
    user_data: dict,
    base_prompt: str,
    lang: str = "zh-cn",
    prompt_mode: str = "bdsm_dom"
) -> str:
    """
    根据用户偏好调整语调
    """
    try:
        # 确保使用用户偏好的语言
        user_preferred_lang = user_data.get("preferred_language", "zh-cn")
        final_lang = user_preferred_lang if user_preferred_lang else lang

        # 获取用户训练偏好
        training_preferences = user_data.get("training_preferences", {})
        liked_practices = training_preferences.get("liked_practices", [])

        # 计算对话轮次
        conversation = user_data.get("conversation", [])
        conversation_round = len(conversation) // 2

        # 获取语调修饰符，使用最终确定的语言
        tone_modifier = get_tone_modifier(
            mode=prompt_mode,
            lang=final_lang,
            conversation_round=conversation_round,
            liked_practices=liked_practices,
            secondary_tone=None
        )

        # 获取用户偏好
        preferences = user_data.get("preferences", {})

        # 应用语调修饰符
        if tone_modifier:
            if final_lang == "zh-cn":
                adjusted_prompt = f"{base_prompt}\n\n语调要求：{tone_modifier}"
            else:
                adjusted_prompt = f"{base_prompt}\n\nTone requirement: {tone_modifier}"
        else:
            adjusted_prompt = base_prompt

        # 根据用户偏好进一步调整
        if preferences.get("formal_tone", False):
            if final_lang == "zh-cn":
                adjusted_prompt += "\n请使用正式、礼貌的语言。"
            else:
                adjusted_prompt += "\nPlease use formal and polite language."

        if preferences.get("creative_responses", False):
            if final_lang == "zh-cn":
                adjusted_prompt += "\n请发挥创造力，提供有趣和独特的回答。"
            else:
                adjusted_prompt += "\nPlease be creative and provide interesting and unique responses."

        return adjusted_prompt

    except Exception as e:
        logger.error(f"调整语调失败: {str(e)}")
        return base_prompt