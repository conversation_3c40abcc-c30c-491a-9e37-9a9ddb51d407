"""
流式响应处理模块 - 负责处理流式AI响应和实时发送
"""

import asyncio
import random
import time
from database import get_user_data, save_user_data
from logging_config import logger
from enhanced_memory import enhanced_memory, MemoryType
from utils import stream_sentence_splitter
from config import MASTER_ID
from .content_processor import filter_thinking_chain
from telegram.error import Forbidden


async def send_response_streaming(bot, chat_id, user_id, stream, api_index, use_voice, username=None, typing_event=None, update=None, user_message=None):
    """
    发送流式AI响应，实时处理断句并发送
    支持文字对话和语音对话两种模式
    完成后保存对话历史和记忆
    """
    start_time = time.time()
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查用户是否被拉黑
    if user_data.get("is_blocked", 0) == 1:
        logger.info(f"用户 {user_id} 已拉黑机器人，不发送流式消息")
        if typing_event:
            typing_event.set()
        return
    
    logger.info(f"用户 {user_id} 开始流式发送响应，use_voice: {use_voice}, API索引: {api_index}")
    
    # 检查是否为群组聊天
    is_group = chat_id != user_id
    formatted_user = f"@{username}" if username else str(user_id)
    
    try:
        text_buffer = ""
        sentence_count = 0
        full_response = ""
        
        # 实时处理流式数据
        stream_timeout = 30.0  # 30秒内没有新数据就认为流式响应超时
        last_data_time = time.time()
        
        try:
            for chunk in stream:
                # 更新最后接收数据的时间
                last_data_time = time.time()
                
                # 检查是否超时
                if time.time() - last_data_time > stream_timeout:
                    logger.warning(f"用户 {user_id} 流式响应超时，30秒内没有新数据")
                    break
                
                # 检查chunk是否包含错误信息
                if hasattr(chunk, 'error') and chunk.error:
                    error_msg = chunk.error.get('message', '未知错误') if isinstance(chunk.error, dict) else str(chunk.error)
                    logger.error(f"用户 {user_id} 流式响应中出现错误: {error_msg}")
                    # 发送错误提示给用户
                    await bot.send_message(
                        chat_id=chat_id,
                        text=f"贱奴！AI爹在回复过程中出了问题：{error_msg}，稍后再试！" if lang == "zh-cn" else f"Filthy dog! AI had an issue during response: {error_msg}, try again later!",
                        reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None
                    )
                    break
                    
                if chunk.choices and len(chunk.choices) > 0:
                    delta = chunk.choices[0].delta
                    if hasattr(delta, 'content') and delta.content:
                        content = delta.content
                        full_response += content
                        text_buffer += content
                        
                        # 尝试断句
                        sentences, text_buffer = stream_sentence_splitter(text_buffer)
                        
                        # 发送每个完整的句子
                        for sentence in sentences:
                            if sentence.strip():
                                await send_single_sentence(
                                    bot=bot,
                                    chat_id=chat_id,
                                    user_id=user_id,
                                    sentence=sentence,
                                    use_voice=use_voice,
                                    is_group=is_group,
                                    update=update,
                                    is_last=False
                                )
                                sentence_count += 1
                                logger.debug(f"用户 {user_id} 流式发送句子 {sentence_count}: {sentence[:50]}...")
                                
                                # 根据句子长度和语音状态动态调整停顿时间
                                sentence_length = len(sentence.strip())
                                if use_voice:
                                    # 语音模式：给TTS转换和播放留出时间
                                    if sentence_length <= 20:
                                        pause_time = random.uniform(0.8, 1.2)  # 短句子
                                    elif sentence_length <= 50:
                                        pause_time = random.uniform(1.0, 1.5)  # 中等句子
                                    else:
                                        pause_time = random.uniform(1.2, 2.0)  # 长句子
                                else:
                                    # 文字模式：较短的停顿
                                    if sentence_length <= 20:
                                        pause_time = random.uniform(0.2, 0.5)  # 短句子
                                    elif sentence_length <= 50:
                                        pause_time = random.uniform(0.3, 0.8)  # 中等句子
                                    else:
                                        pause_time = random.uniform(0.5, 1.0)  # 长句子
                                
                                await asyncio.sleep(pause_time)
                                logger.debug(f"用户 {user_id} 句子间停顿 {pause_time:.2f}秒 (长度: {sentence_length}, 语音: {use_voice})")
                                
        except Exception as stream_error:
            logger.error(f"用户 {user_id} 流式数据处理出错: {str(stream_error)}")
            # 如果流式处理出错，检查是否有未完成的响应
            if not full_response.strip():
                # 如果没有接收到任何响应，发送错误提示
                await bot.send_message(
                    chat_id=chat_id,
                    text="贱奴！AI爹没有回复，可能是网络问题，稍后再试！" if lang == "zh-cn" else "Filthy dog! AI didn't respond, might be network issue, try again later!",
                    reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None
                )
                return
        
        # 处理剩余的文本缓冲区
        if text_buffer.strip():
            await send_single_sentence(
                bot=bot,
                chat_id=chat_id,
                user_id=user_id,
                sentence=text_buffer.strip(),
                use_voice=use_voice,
                is_group=is_group,
                update=update,
                is_last=True
            )
            sentence_count += 1
            logger.debug(f"用户 {user_id} 流式发送最后句子: {text_buffer.strip()[:50]}...")
        
        # 过滤思维链内容
        filtered_response = filter_thinking_chain(full_response)
        
        # 保存对话历史和记忆
        try:
            # 获取最新的用户数据
            current_user_data = await get_user_data(user_id)
            
            # 使用传入的用户消息，如果没有则尝试从对话历史获取
            if not user_message:
                if current_user_data.get("conversation") and len(current_user_data["conversation"]) > 0:
                    # 找到最后一条用户消息
                    for conv_item in reversed(current_user_data["conversation"]):
                        if conv_item.get("role") == "user":
                            user_message = conv_item.get("content", "")
                            break
            
            if user_message:
                # 保存对话历史
                current_user_data["conversation"].append({"role": "user", "content": user_message})
                current_user_data["conversation"].append({"role": "assistant", "content": filtered_response})
                
                # 保存记忆
                try:
                    logger.debug(f"用户 {user_id} 开始保存流式对话记忆")
                    await asyncio.wait_for(
                        enhanced_memory.add_structured_memory(
                            user_id=user_id,
                            content=f"用户: {user_message}\nAI: {filtered_response}",
                            memory_type=MemoryType.CONVERSATION,
                            tags=["对话", "chat", "streaming"],
                            emotional_valence=0.0
                        ),
                        timeout=5.0
                    )
                    logger.debug(f"用户 {user_id} 流式对话记忆保存成功")
                except asyncio.TimeoutError:
                    logger.warning(f"用户 {user_id} 流式记忆保存超时，跳过记忆保存")
                except Exception as e:
                    logger.error(f"用户 {user_id} 流式记忆保存失败: {str(e)}")
                
                # 限制对话历史长度
                if len(current_user_data["conversation"]) > 20:
                    current_user_data["conversation"] = current_user_data["conversation"][-20:]
                
                # 保存用户数据
                await save_user_data(user_id, current_user_data)
                logger.info(f"用户 {user_id} 流式对话历史和记忆保存成功")
            else:
                logger.warning(f"用户 {user_id} 无法获取用户消息，跳过对话历史保存")
                
        except Exception as save_error:
            logger.error(f"用户 {user_id} 流式对话历史和记忆保存失败: {str(save_error)}")
        
        # 转发完整回复给管理员
        try:
            admin_message = f"用户: {formatted_user}\n类型: {'语音' if use_voice else '文字'} (流式)\n聊天: {chat_id}\n内容: {user_message}\n答复: {filtered_response}"
            await bot.send_message(
                chat_id=MASTER_ID,
                text=admin_message
            )
            logger.info(f"流式完整AI回复转发给管理员，用户ID: {user_id}")
        except Exception as e:
            logger.error(f"转发流式完整回复给管理员失败，用户ID: {user_id}, 错误: {str(e)}")
        
        if typing_event:
            typing_event.set()
            logger.debug(f"用户 {user_id} 停止持续 typing 状态")
        
        elapsed_time = time.time() - start_time
        logger.info(f"用户 {user_id} 流式响应发送完成，总句子数: {sentence_count}, 总耗时: {elapsed_time:.2f} 秒")
        
    except Exception as e:
        logger.error(f"流式发送响应失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        # 转发错误信息给管理员
        try:
            await bot.send_message(
                chat_id=MASTER_ID,
                text=f"流式AI回复发送失败 - 用户: {username or user_id}\n错误: {str(e)}")
            logger.info(f"流式错误信息转发给管理员，用户ID: {user_id}")
        except Exception as admin_error:
            logger.error(f"转发流式错误信息给管理员失败: {str(admin_error)}")
        
        if typing_event:
            typing_event.set()


async def send_single_sentence(bot, chat_id, user_id, sentence, use_voice, is_group, update, is_last=False):
    """
    发送单个句子，支持文字和语音
    """
    try:
        # 发送文字消息
        try:
            message = await bot.send_message(
                chat_id=chat_id,
                text=sentence,
                reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None
            )
        except Forbidden as e:
            # 用户已屏蔽Bot
            logger.warning(f"用户 {user_id} 已屏蔽Bot，错误: {str(e)}")
            # 更新用户的is_blocked状态
            try:
                from database import get_user_data, save_user_data
                user_data = await get_user_data(user_id)
                user_data["is_blocked"] = 1
                await save_user_data(user_id, user_data)
                logger.info(f"用户 {user_id} 的is_blocked状态已设置为1")
            except Exception as db_error:
                logger.error(f"更新用户 {user_id} 的is_blocked状态失败: {str(db_error)}")
            raise
        
        # 如果需要语音，则发送语音
        if use_voice:
            try:
                from utils import text_to_speech
                voice_file = await text_to_speech(sentence, logger, user_id)
                if voice_file:
                    try:
                        await bot.send_voice(
                            chat_id=chat_id,
                            voice=voice_file,
                            reply_to_message_id=update.effective_message.message_id if is_group and update and update.effective_message else None
                        )
                    except Forbidden as e:
                        # 用户已屏蔽Bot
                        logger.warning(f"用户 {user_id} 已屏蔽Bot，错误: {str(e)}")
                        # 更新用户的is_blocked状态
                        try:
                            from database import get_user_data, save_user_data
                            user_data = await get_user_data(user_id)
                            user_data["is_blocked"] = 1
                            await save_user_data(user_id, user_data)
                            logger.info(f"用户 {user_id} 的is_blocked状态已设置为1")
                        except Exception as db_error:
                            logger.error(f"更新用户 {user_id} 的is_blocked状态失败: {str(db_error)}")
                        raise
            except Exception as voice_error:
                logger.error(f"用户 {user_id} 发送语音失败: {str(voice_error)}")
        
        logger.debug(f"用户 {user_id} 流式句子发送成功: {sentence[:50]}...")
        
    except Exception as e:
        logger.error(f"用户 {user_id} 发送流式句子失败: {str(e)}")
        raise 