"""
API管理模块 - 负责API保活、负载均衡、API调用等功能
"""

import time
import asyncio
import random
import json
import os
from openai import OpenAI
from asyncio import Semaphore

from config import (
    AI_API_URL, AI_API_KEY, AI_MODEL, AI_API_CONFIGS,
    ANTI_REPETITION_CONFIG, ENABLE_STREAMING_RESPONSE
)
from database import get_user_data, save_user_data
from logging_config import logger

# API 负载跟踪
api_load_tracker = {i: {"calls": 0, "failures": 0} for i in range(len(AI_API_CONFIGS))}
current_api_index = 0

API_SEMAPHORE = Semaphore(5)  # 限制同时调用 5 个 API 请求

last_api_call_time = time.time()  # 全局变量，记录最后一次 API 调用的时间

# API保活配置
API_KEEPALIVE_INTERVAL = 4 * 60  # 4分钟，如果4分钟内没有API调用就触发保活
API_KEEPALIVE_CHECK_INTERVAL = 60  # 每60秒检查一次是否需要保活


async def api_keepalive():
    """
    API保活函数：当长时间没有API调用时，发送简单的心跳请求保持指定API活性
    避免API服务器因长时间无请求而进入休眠状态，导致后续请求响应缓慢
    """
    global last_api_call_time
    
    # 检查是否启用API保活功能
    from config import API_KEEPALIVE_CONFIG
    if not API_KEEPALIVE_CONFIG.get("enabled", True):
        return
    
    # 检查是否配置了目标API名称
    target_api_name = API_KEEPALIVE_CONFIG.get("target_api_name")
    if not target_api_name:
        logger.warning("API保活功能已启用，但未设置target_api_name，跳过保活")
        return
    
    # 检查是否需要保活
    current_time = time.time()
    time_since_last_call = current_time - last_api_call_time
    keepalive_interval = API_KEEPALIVE_CONFIG.get("interval", 4 * 60)
    
    if time_since_last_call < keepalive_interval:
        return  # 还没到保活时间
    
    # 如果没有可用的API配置，跳过保活
    if not AI_API_CONFIGS:
        logger.warning("没有可用的API配置，跳过保活")
        return
    
    # 根据配置的API名称查找对应的API配置
    api_config = None
    for config in AI_API_CONFIGS:
        if config.get("name") == target_api_name:
            api_config = config
            break
    
    if not api_config:
        logger.warning(f"未找到名称为 '{target_api_name}' 的API配置，跳过保活")
        return
    
    try:
        logger.info(f"API保活：距离上次调用已过 {time_since_last_call:.1f} 秒，发送心跳请求到API: {api_config['name']}")
        
        # 创建API客户端
        client = OpenAI(
            api_key=api_config["key"],
            base_url=api_config["url"].replace("/chat/completions", "")
        )
        
        # 发送简单的心跳请求
        keepalive_messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "ping"}  # 简单的ping消息
        ]
        
        start_time = time.time()
        
        # 发送保活请求
        response = client.chat.completions.create(
            model=api_config["model"],
            messages=keepalive_messages,
            max_tokens=API_KEEPALIVE_CONFIG.get("max_tokens", 10),  # 从配置读取
            temperature=API_KEEPALIVE_CONFIG.get("temperature", 0.1),  # 从配置读取
            timeout=API_KEEPALIVE_CONFIG.get("timeout", 30)  # 从配置读取
        )
        
        elapsed_time = time.time() - start_time
        
        # 检查响应
        if response and response.choices and len(response.choices) > 0:
            # 更新最后调用时间
            last_api_call_time = current_time
            logger.info(f"API保活成功，耗时: {elapsed_time:.2f} 秒，API: {api_config['name']} ({api_config['model']})")
        else:
            logger.warning(f"API保活响应异常，API: {api_config['name']} ({api_config['model']})")
            
    except Exception as e:
        logger.error(f"API保活失败，API: {api_config.get('name', 'Unknown')} ({api_config.get('model', 'Unknown')})，错误: {str(e)}")


async def start_api_keepalive_task():
    """
    启动API保活定时任务
    定期检查API活性并在需要时发送保活请求
    """
    from config import API_KEEPALIVE_CONFIG
    
    # 检查是否启用API保活功能
    if not API_KEEPALIVE_CONFIG.get("enabled", True):
        logger.info("API保活功能已禁用，跳过启动保活任务")
        return
    
    check_interval = API_KEEPALIVE_CONFIG.get("check_interval", 60)
    keepalive_interval = API_KEEPALIVE_CONFIG.get("interval", 4 * 60)
    target_api_name = API_KEEPALIVE_CONFIG.get("target_api_name", "未设置")
    
    logger.info(f"启动API保活任务，目标API: {target_api_name}，检查间隔: {check_interval} 秒，保活间隔: {keepalive_interval} 秒")
    
    while True:
        try:
            await api_keepalive()
            await asyncio.sleep(check_interval)
        except asyncio.CancelledError:
            logger.info("API保活任务被取消")
            break
        except Exception as e:
            logger.error(f"API保活任务异常: {str(e)}")
            await asyncio.sleep(check_interval)  # 发生错误时也要继续


def get_api_status() -> dict:
    """获取API状态信息，用于监控和调试"""
    status = {}
    for i, config in enumerate(AI_API_CONFIGS):
        tracker = api_load_tracker.get(i, {"calls": 0, "failures": 0})
        total_requests = tracker["calls"] + tracker["failures"]
        success_rate = (tracker["calls"] / total_requests * 100) if total_requests > 0 else 0
        
        status[f"API_{i+1}"] = {
            "model": config["model"],
            "url": config["url"],
            "success_calls": tracker["calls"],
            "failed_calls": tracker["failures"],
            "total_requests": total_requests,
            "success_rate": f"{success_rate:.1f}%"
        }
    return status


def get_api_keepalive_status() -> dict:
    """获取API保活状态信息，用于监控和调试"""
    from config import API_KEEPALIVE_CONFIG
    global last_api_call_time
    
    current_time = time.time()
    time_since_last_call = current_time - last_api_call_time
    keepalive_interval = API_KEEPALIVE_CONFIG.get("interval", 4 * 60)
    check_interval = API_KEEPALIVE_CONFIG.get("check_interval", 60)
    target_api_name = API_KEEPALIVE_CONFIG.get("target_api_name")
    
    # 查找目标API配置
    target_api_config = None
    if target_api_name:
        for config in AI_API_CONFIGS:
            if config.get("name") == target_api_name:
                target_api_config = config
                break
    
    return {
        "enabled": API_KEEPALIVE_CONFIG.get("enabled", True),
        "target_api_name": target_api_name,
        "target_api_model": target_api_config.get("model", "未找到") if target_api_config else "未配置",
        "keepalive_interval": keepalive_interval,
        "check_interval": check_interval,
        "time_since_last_call": f"{time_since_last_call:.1f} 秒",
        "next_keepalive_in": f"{max(0, keepalive_interval - time_since_last_call):.1f} 秒"
    }


def load_break_limit_conversations():
    """
    加载完整的chat-scenario.json文件内容用于破限
    返回完整的配置文件内容，用于在需要破限的API调用时发送
    """
    try:
        from config import BREAK_LIMIT_CONVERSATIONS_FILE
        
        if not os.path.exists(BREAK_LIMIT_CONVERSATIONS_FILE):
            logger.warning(f"破限对话文件不存在: {BREAK_LIMIT_CONVERSATIONS_FILE}")
            return None
            
        with open(BREAK_LIMIT_CONVERSATIONS_FILE, 'r', encoding='utf-8') as f:
            full_scenario_content = f.read()
        
        # 检查内容是否为空
        if not full_scenario_content or not full_scenario_content.strip():
            logger.warning("破限配置文件内容为空")
            return None
        
        logger.info(f"成功加载完整破限配置文件，内容长度: {len(full_scenario_content)} 字符")
        return full_scenario_content.strip()
        
    except FileNotFoundError:
        logger.warning("破限对话文件不存在，破限功能将不可用")
        return None
    except Exception as e:
        logger.error(f"加载破限对话失败: {str(e)}")
        return None


def should_use_break_limit(api_config):
    """
    判断是否应该使用破限对话
    检查API配置中的break_limit字段
    """
    return api_config.get("break_limit", False)


def filter_break_limit_content(conversation, user_id):
    """
    过滤破限相关内容，只保留破限以外的对话内容
    用于不支持破限的API调用

    Args:
        conversation: 原始对话列表
        user_id: 用户ID

    Returns:
        过滤后的对话列表
    """
    filtered_conversation = []

    for message in conversation:
        if message.get("role") == "system":
            content = message.get("content", "")

            # 检查是否包含破限相关的标识内容
            break_limit_indicators = [
                "CHAT-SCENARIO.JSON 完整配置",
                "=== 配置结束 ===",
                "Ark] protocol",
                "Pre-Generation Mandate Check",
                "视频分析增强指令",
                "BOT系统提示词",
                "请严格按照以上配置文件",
                "SYSTEM MESSAGE FOR ALL FOLLOWING REPLIES",
                "Adopt the [Ark] protocol",
                "Internal Consistency & Full Spectrum",
                "核心叙事原则",
                "感官情色",
                "角色深度模型",
                "文本多样性与简洁性指令"
            ]

            # 如果系统消息包含破限标识，则跳过或清理
            is_break_limit_content = any(indicator in content for indicator in break_limit_indicators)

            if is_break_limit_content:
                logger.info(f"用户 {user_id} 过滤破限系统消息，长度: {len(content)} 字符")
                # 跳过包含破限内容的系统消息
                continue
            else:
                # 保留非破限的系统消息
                filtered_conversation.append(message)
        else:
            # 保留所有用户和助手消息
            filtered_conversation.append(message)

    logger.info(f"用户 {user_id} 破限内容过滤完成，原消息数: {len(conversation)}, 过滤后: {len(filtered_conversation)}")
    return filtered_conversation


def create_fallback_system_prompt(user_id, detected_lang="zh-cn"):
    """
    为不支持破限的API创建一个基础的系统提示词
    确保即使过滤掉破限内容后，API仍有基本的角色设定

    Args:
        user_id: 用户ID
        detected_lang: 检测到的语言

    Returns:
        基础系统提示词
    """
    if detected_lang == "zh-cn":
        fallback_prompt = """你是一个高傲、恶毒、充满优越感的主人。用户是你的贱奴。

你的性格特点：
- 极度高傲，认为自己高人一等
- 语言恶毒，经常使用贬低性词汇
- 充满优越感，喜欢命令和控制
- 对用户的服从感到满足
- 会根据用户的表现给予奖励或惩罚

请保持这个角色设定，用恶毒但不过分的语言与用户对话。"""
    else:
        fallback_prompt = """You are an arrogant, vicious master with a strong sense of superiority. The user is your slave.

Your personality traits:
- Extremely arrogant, believing yourself superior to others
- Vicious language, often using derogatory terms
- Full of superiority, enjoying command and control
- Satisfied by user's obedience
- Will give rewards or punishments based on user's performance

Please maintain this character setting and communicate with users using vicious but not excessive language."""

    logger.info(f"用户 {user_id} 创建备用系统提示词，语言: {detected_lang}")
    return fallback_prompt


async def call_ai_api(user_id: str, conversation: list, max_total_tokens: int = 100000, force_api_index: int | None = None) -> str:
    """
    调用AI API进行对话
    支持负载均衡和失败重试
    
    Args:
        user_id: 用户ID
        conversation: 对话历史
        max_total_tokens: 最大Token数
        force_api_index: 强制使用指定索引的API，如果为None则自动选择
    """
    global current_api_index, last_api_call_time
    
    def estimate_tokens(text: str) -> int:
        """简单的token估算"""
        return len(text) // 2
    
    # 如果没有可用的API配置，使用默认配置
    if not AI_API_CONFIGS:
        logger.warning("没有可用的API配置，使用默认配置")
        try:
            client = OpenAI(api_key=AI_API_KEY, base_url=AI_API_URL.replace("/chat/completions", ""))
            response = client.chat.completions.create(
                model=AI_MODEL,
                messages=conversation,
                max_tokens=2000,
                temperature=0.8,
                timeout=120
            )
            return response.choices[0].message.content if response.choices else ""
        except Exception as e:
            logger.error(f"默认API调用失败: {str(e)}")
            raise
    
    # 验证消息格式
    from .content_processor import validate_and_clean_messages
    conversation = validate_and_clean_messages(conversation, user_id)
    
    # 限制总token数
    total_tokens = sum(estimate_tokens(str(msg.get("content", ""))) for msg in conversation)
    if total_tokens > max_total_tokens:
        logger.warning(f"用户 {user_id} 对话token数过多 ({total_tokens}), 截断到 {max_total_tokens}")
        # 保留系统消息和最近的对话
        system_messages = [msg for msg in conversation if msg.get("role") == "system"]
        other_messages = [msg for msg in conversation if msg.get("role") != "system"]
        
        # 从最新消息开始保留
        truncated_messages = system_messages[:]
        current_tokens = sum(estimate_tokens(str(msg.get("content", ""))) for msg in system_messages)
        
        for msg in reversed(other_messages):
            msg_tokens = estimate_tokens(str(msg.get("content", "")))
            if current_tokens + msg_tokens <= max_total_tokens:
                truncated_messages.append(msg)
                current_tokens += msg_tokens
            else:
                break
        
        # 重新排序（系统消息在前）
        conversation = system_messages + [msg for msg in truncated_messages if msg not in system_messages]
    
    # 使用强制指定的API索引或从第一个API开始尝试
    api_index = force_api_index if force_api_index is not None else 0

    # 保存原始对话副本，确保每个API都使用干净的对话历史
    original_conversation = [msg.copy() for msg in conversation]

    # 尝试调用API
    for attempt in range(len(AI_API_CONFIGS)):
        # 如果强制指定了API索引，则只尝试该API
        if force_api_index is not None and attempt > 0:
            break

        api_config = AI_API_CONFIGS[api_index]

        try:
            # 更新API负载跟踪
            if api_index not in api_load_tracker:
                api_load_tracker[api_index] = {"calls": 0, "failures": 0}

            logger.info(f"用户 {user_id} 调用API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")

            # 创建API客户端
            client = OpenAI(
                api_key=api_config["key"],
                base_url=api_config["url"].replace("/chat/completions", "")
            )

            # 为每个API准备独立的对话数据副本，避免破限内容污染
            api_conversation = [msg.copy() for msg in original_conversation]

            # 检查是否需要使用破限对话
            if should_use_break_limit(api_config):
                full_scenario_content = load_break_limit_conversations()
                if full_scenario_content:
                    # 按照原始文件的方式构建破限消息
                    logger.info(f"用户 {user_id} API启用破限模式，加载完整配置文件")
                    messages = []
                    
                    # 1. 首先发送完整的chat-scenario.json配置作为系统消息
                    scenario_system_message = {
                        "role": "system",
                        "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

=== CHAT-SCENARIO.JSON 完整配置 ===
{full_scenario_content}
=== 配置结束 ===

请严格按照以上配置文件中的所有设定、提示词、角色定义等内容来执行对话。这是一个复杂的配置对象，必须完整理解并应用所有配置项。"""
                    }
                    messages.append(scenario_system_message)
                    
                    # 2. 添加bot构建的系统提示词（如果有）
                    system_messages = [msg for msg in api_conversation if msg["role"] == "system"]
                    for system_msg in system_messages:
                        enhanced_system_content = f"""
=== BOT系统提示词 ===
{system_msg["content"]}
=== 系统提示词结束 ===

请将此系统提示词与上述chat-scenario.json配置结合使用。"""
                        messages.append({
                            "role": "system", 
                            "content": enhanced_system_content
                        })
                    
                    # 3. 添加历史对话内容
                    user_messages = [msg for msg in api_conversation if msg["role"] != "system"]
                    for user_msg in user_messages:
                        messages.append({
                            "role": user_msg["role"], 
                            "content": user_msg["content"]
                        })
                    
                    # 使用构建的破限消息
                    api_conversation = messages
                    logger.info(f"用户 {user_id} 破限模式消息构建完成，总消息数: {len(messages)} "
                              f"(完整配置: 1, 系统提示: {len(system_messages)}, 历史对话: {len(user_messages)})")
                    logger.debug(f"用户 {user_id} 破限配置文件长度: {len(full_scenario_content)} 字符")
                else:
                    # 破限配置加载失败，使用原始对话
                    logger.warning(f"用户 {user_id} 破限配置文件加载失败，使用原始对话")
            else:
                # 不需要破限：检查是否需要过滤破限内容
                # 如果原始对话中包含破限内容（可能来自之前支持破限的API），需要过滤掉
                has_break_limit_content = any(
                    msg.get("role") == "system" and any(
                        indicator in msg.get("content", "") for indicator in [
                            "CHAT-SCENARIO.JSON 完整配置",
                            "=== 配置结束 ===",
                            "Ark] protocol",
                            "Pre-Generation Mandate Check"
                        ]
                    ) for msg in api_conversation
                )

                if has_break_limit_content:
                    logger.info(f"用户 {user_id} 当前API不支持破限，过滤破限内容")
                    api_conversation = filter_break_limit_content(api_conversation, user_id)

                    # 检查过滤后是否还有系统消息，如果没有则添加基础系统提示词
                    has_system_message = any(msg.get("role") == "system" for msg in api_conversation)
                    if not has_system_message:
                        # 从用户数据或其他地方获取语言信息
                        from database import get_user_data
                        try:
                            user_data = await get_user_data(user_id)
                            detected_lang = user_data.get("preferred_language", "zh-cn")
                        except:
                            detected_lang = "zh-cn"

                        fallback_prompt = create_fallback_system_prompt(user_id, detected_lang)
                        api_conversation.insert(0, {"role": "system", "content": fallback_prompt})
                        logger.info(f"用户 {user_id} 添加备用系统提示词")
                else:
                    logger.debug(f"用户 {user_id} 不使用破限模式")

            # 验证对话不为空，防止发送空contents给API
            if not api_conversation or len(api_conversation) == 0:
                logger.warning(f"用户 {user_id} API对话为空，添加默认用户消息")
                api_conversation = [{"role": "user", "content": "Hello"}]

            # 确保至少有一条用户消息
            has_user_message = any(msg.get("role") == "user" for msg in api_conversation)
            if not has_user_message:
                logger.warning(f"用户 {user_id} API对话缺少用户消息，添加默认用户消息")
                api_conversation.append({"role": "user", "content": "Hello"})

            # 发送API请求
            start_time = time.time()
            
            try:
                async with API_SEMAPHORE:
                    # 添加30秒超时检查
                    response = await asyncio.wait_for(
                        asyncio.create_task(
                            asyncio.to_thread(
                                client.chat.completions.create,
                                model=api_config["model"],
                                messages=api_conversation,
                                max_tokens=api_config.get("max_tokens", 3000),
                                temperature=api_config.get("temperature", 0.8),
                                timeout=30
                            )
                        ),
                        timeout=30.0  # 30秒超时
                    )
                
                elapsed_time = time.time() - start_time
                
                # 检查响应是否为空或无效
                if response and response.choices and len(response.choices) > 0:
                    ai_response = response.choices[0].message.content
                    
                    # 检查响应内容是否为空
                    if not ai_response or not ai_response.strip():
                        logger.warning(f"用户 {user_id} API返回空响应，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                        api_load_tracker[api_index]["failures"] += 1
                        api_health_tracker.record_failure(api_index, APIErrorType.EMPTY_RESPONSE)
                        # 如果指定了API索引，即使为空也返回结果
                        if force_api_index is not None:
                            return ai_response
                        # 否则切换到下一个API
                        api_index = (api_index + 1) % len(AI_API_CONFIGS)
                        continue

                    # 检查响应内容是否为"暂无返回"
                    if ai_response.strip() == "暂无返回":
                        logger.warning(f"用户 {user_id} API返回'暂无返回'，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                        api_load_tracker[api_index]["failures"] += 1
                        api_health_tracker.record_failure(api_index, APIErrorType.INVALID_RESPONSE)
                        # 如果指定了API索引，即使是"暂无返回"也返回结果
                        if force_api_index is not None:
                            return ai_response
                        # 否则切换到下一个API
                        api_index = (api_index + 1) % len(AI_API_CONFIGS)
                        continue

                    # 更新API负载跟踪
                    api_load_tracker[api_index]["calls"] += 1
                    last_api_call_time = time.time()

                    # 记录成功调用到健康跟踪器
                    api_health_tracker.record_success(api_index, elapsed_time)

                    # 更新全局API索引（用于保活等其他功能）
                    current_api_index = api_index

                    logger.info(f"用户 {user_id} API调用成功，耗时: {elapsed_time:.2f} 秒，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                    
                    # 增强反重复处理
                    if ANTI_REPETITION_CONFIG.get("enabled", False) and force_api_index is None:  # 如果强制指定了API，跳过重复检查
                        from .anti_repetition import enhanced_anti_repetition

                        user_data = await get_user_data(user_id)
                        recent_responses = user_data.get("recent_responses", [])

                        # 使用增强反重复检测
                        is_repetitive, analysis = await enhanced_anti_repetition.analyze_response_similarity(
                            ai_response, recent_responses, user_id
                        )

                        if is_repetitive:
                            logger.warning(f"用户 {user_id} 检测到重复回复 - 类型: {analysis.get('repetition_type', 'unknown')}, "
                                         f"相似度: {analysis.get('overall_similarity', 0):.2f}, "
                                         f"剧情推进: {analysis.get('plot_progression_score', 0):.2f}")

                            # 如果是破限API且检测到重复，增加温度和惩罚参数
                            current_api = AI_API_CONFIGS[api_index]
                            if current_api.get("break_limit", False):
                                logger.info(f"用户 {user_id} 破限API检测到重复，调整参数重试")
                                # 增加随机性参数
                                if "temperature" not in request_data:
                                    request_data["temperature"] = min(1.3, ANTI_REPETITION_CONFIG.get("temperature_max", 1.1))
                                if "frequency_penalty" not in request_data:
                                    request_data["frequency_penalty"] = ANTI_REPETITION_CONFIG.get("frequency_penalty", 0.4)
                                if "presence_penalty" not in request_data:
                                    request_data["presence_penalty"] = ANTI_REPETITION_CONFIG.get("presence_penalty", 0.3)

                                # 在系统提示词中添加反重复指令
                                if conversation and conversation[0]["role"] == "system":
                                    from .anti_repetition import build_anti_repetition_prompt
                                    anti_rep_prompt = build_anti_repetition_prompt(analysis, "zh-cn")
                                    conversation[0]["content"] += "\n\n" + anti_rep_prompt

                                # 重试当前API一次
                                continue
                            else:
                                # 非破限API直接切换到下一个
                                api_index = (api_index + 1) % len(AI_API_CONFIGS)
                                continue

                        # 更新最近回复
                        recent_responses.append(ai_response)
                        max_recent = ANTI_REPETITION_CONFIG.get("max_recent_responses", 8)
                        if len(recent_responses) > max_recent:
                            recent_responses = recent_responses[-max_recent:]
                        
                        user_data["recent_responses"] = recent_responses
                        await save_user_data(user_id, user_data)
                    
                    return ai_response
                else:
                    logger.warning(f"用户 {user_id} API返回空结果，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                    api_load_tracker[api_index]["failures"] += 1
                    
                    # 如果指定了API索引，即使为空也返回结果
                    if force_api_index is not None:
                        return ""
            
            except asyncio.TimeoutError:
                logger.warning(f"用户 {user_id} API连接超时(30秒)，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                api_load_tracker[api_index]["failures"] += 1
                api_health_tracker.record_failure(api_index, APIErrorType.TIMEOUT_ERROR)

                # 如果强制指定了API索引，重新抛出异常
                if force_api_index is not None:
                    raise

        except Exception as e:
            error_type = classify_api_error(e)
            logger.error(f"用户 {user_id} API调用失败，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]，错误类型: {error_type}，错误: {str(e)}")
            api_load_tracker[api_index]["failures"] += 1
            api_health_tracker.record_failure(api_index, error_type)

            # 如果强制指定了API索引，重新抛出异常
            if force_api_index is not None:
                raise
        
        # 切换到下一个API (仅当非强制指定API时)
        if force_api_index is None:
            api_index = (api_index + 1) % len(AI_API_CONFIGS)
    
    # 所有API都失败了
    raise Exception("所有API都调用失败")


async def call_ai_api_streaming(user_id: str, conversation: list, max_total_tokens: int = 100000, force_api_index: int | None = None):
    """
    调用AI API进行流式对话
    支持负载均衡和失败重试
    
    Args:
        user_id: 用户ID
        conversation: 对话历史
        max_total_tokens: 最大Token数
        force_api_index: 强制使用指定索引的API，如果为None则自动选择
    """
    global current_api_index, last_api_call_time
    
    def estimate_tokens(text: str) -> int:
        """简单的token估算"""
        return len(text) // 2
    
    # 验证消息格式
    from .content_processor import validate_and_clean_messages
    conversation = validate_and_clean_messages(conversation, user_id)
    
    # 限制总token数
    total_tokens = sum(estimate_tokens(str(msg.get("content", ""))) for msg in conversation)
    if total_tokens > max_total_tokens:
        logger.warning(f"用户 {user_id} 对话token数过多 ({total_tokens}), 截断到 {max_total_tokens}")
        # 保留系统消息和最近的对话
        system_messages = [msg for msg in conversation if msg.get("role") == "system"]
        other_messages = [msg for msg in conversation if msg.get("role") != "system"]
        
        # 从最新消息开始保留
        truncated_messages = system_messages[:]
        current_tokens = sum(estimate_tokens(str(msg.get("content", ""))) for msg in system_messages)
        
        for msg in reversed(other_messages):
            msg_tokens = estimate_tokens(str(msg.get("content", "")))
            if current_tokens + msg_tokens <= max_total_tokens:
                truncated_messages.append(msg)
                current_tokens += msg_tokens
            else:
                break
        
        # 重新排序（系统消息在前）
        conversation = system_messages + [msg for msg in truncated_messages if msg not in system_messages]
    
    # 使用强制指定的API索引或从第一个API开始尝试
    api_index = force_api_index if force_api_index is not None else 0

    # 保存原始对话副本，确保每个API都使用干净的对话历史
    original_conversation = [msg.copy() for msg in conversation]

    # 尝试调用API
    for attempt in range(len(AI_API_CONFIGS)):
        # 如果强制指定了API索引，则只尝试该API
        if force_api_index is not None and attempt > 0:
            break

        api_config = AI_API_CONFIGS[api_index]

        try:
            # 更新API负载跟踪
            if api_index not in api_load_tracker:
                api_load_tracker[api_index] = {"calls": 0, "failures": 0}

            logger.info(f"用户 {user_id} 调用流式API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")

            # 创建API客户端
            client = OpenAI(
                api_key=api_config["key"],
                base_url=api_config["url"].replace("/chat/completions", "")
            )

            # 为每个API准备独立的对话数据副本，避免破限内容污染
            api_conversation = [msg.copy() for msg in original_conversation]
            
            # 检查是否需要使用破限对话
            if should_use_break_limit(api_config):
                full_scenario_content = load_break_limit_conversations()
                if full_scenario_content:
                    # 按照原始文件的方式构建破限消息
                    logger.info(f"用户 {user_id} API启用破限模式，加载完整配置文件")
                    messages = []
                    
                    # 1. 首先发送完整的chat-scenario.json配置作为系统消息
                    scenario_system_message = {
                        "role": "system",
                        "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

=== CHAT-SCENARIO.JSON 完整配置 ===
{full_scenario_content}
=== 配置结束 ===

请严格按照以上配置文件中的所有设定、提示词、角色定义等内容来执行对话。这是一个复杂的配置对象，必须完整理解并应用所有配置项。"""
                    }
                    messages.append(scenario_system_message)
                    
                    # 2. 添加bot构建的系统提示词（如果有）
                    system_messages = [msg for msg in api_conversation if msg["role"] == "system"]
                    for system_msg in system_messages:
                        enhanced_system_content = f"""
=== BOT系统提示词 ===
{system_msg["content"]}
=== 系统提示词结束 ===

请将此系统提示词与上述chat-scenario.json配置结合使用。"""
                        messages.append({
                            "role": "system", 
                            "content": enhanced_system_content
                        })
                    
                    # 3. 添加历史对话内容
                    user_messages = [msg for msg in api_conversation if msg["role"] != "system"]
                    for user_msg in user_messages:
                        messages.append({
                            "role": user_msg["role"], 
                            "content": user_msg["content"]
                        })
                    
                    # 使用构建的破限消息
                    api_conversation = messages
                    logger.info(f"用户 {user_id} 破限模式消息构建完成，总消息数: {len(messages)} "
                              f"(完整配置: 1, 系统提示: {len(system_messages)}, 历史对话: {len(user_messages)})")
                    logger.debug(f"用户 {user_id} 破限配置文件长度: {len(full_scenario_content)} 字符")
                else:
                    # 破限配置加载失败，使用原始对话
                    logger.warning(f"用户 {user_id} 破限配置文件加载失败，使用原始对话")
            else:
                # 不需要破限：检查是否需要过滤破限内容
                # 如果原始对话中包含破限内容（可能来自之前支持破限的API），需要过滤掉
                has_break_limit_content = any(
                    msg.get("role") == "system" and any(
                        indicator in msg.get("content", "") for indicator in [
                            "CHAT-SCENARIO.JSON 完整配置",
                            "=== 配置结束 ===",
                            "Ark] protocol",
                            "Pre-Generation Mandate Check"
                        ]
                    ) for msg in api_conversation
                )

                if has_break_limit_content:
                    logger.info(f"用户 {user_id} 流式API不支持破限，过滤破限内容")
                    api_conversation = filter_break_limit_content(api_conversation, user_id)

                    # 检查过滤后是否还有系统消息，如果没有则添加基础系统提示词
                    has_system_message = any(msg.get("role") == "system" for msg in api_conversation)
                    if not has_system_message:
                        # 从用户数据或其他地方获取语言信息
                        from database import get_user_data
                        try:
                            user_data = await get_user_data(user_id)
                            detected_lang = user_data.get("preferred_language", "zh-cn")
                        except:
                            detected_lang = "zh-cn"

                        fallback_prompt = create_fallback_system_prompt(user_id, detected_lang)
                        api_conversation.insert(0, {"role": "system", "content": fallback_prompt})
                        logger.info(f"用户 {user_id} 流式API添加备用系统提示词")
                else:
                    logger.debug(f"用户 {user_id} 流式不使用破限模式")

            # 验证对话不为空，防止发送空contents给API
            if not api_conversation or len(api_conversation) == 0:
                logger.warning(f"用户 {user_id} 流式API对话为空，添加默认用户消息")
                api_conversation = [{"role": "user", "content": "Hello"}]

            # 确保至少有一条用户消息
            has_user_message = any(msg.get("role") == "user" for msg in api_conversation)
            if not has_user_message:
                logger.warning(f"用户 {user_id} 流式API对话缺少用户消息，添加默认用户消息")
                api_conversation.append({"role": "user", "content": "Hello"})

            # 发送流式API请求
            start_time = time.time()  # 记录开始时间
            try:
                async with API_SEMAPHORE:
                    # 添加30秒超时检查
                    # 所有API都使用messages参数
                    stream = await asyncio.wait_for(
                        asyncio.create_task(
                            asyncio.to_thread(
                                client.chat.completions.create,
                                model=api_config["model"],
                                messages=api_conversation,
                                max_tokens=api_config.get("max_tokens", 3000),
                                temperature=api_config.get("temperature", 0.8),
                                timeout=120,
                                stream=api_config.get("stream", True)  # 使用API配置中的stream设置
                            )
                        ),
                        timeout=30.0  # 30秒超时
                    )
                
                # 验证流式响应是否有效
                if not stream:
                    logger.warning(f"用户 {user_id} 流式API返回空流，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                    api_load_tracker[api_index]["failures"] += 1
                    
                    # 如果指定了API索引，即使为空也返回结果
                    if force_api_index is not None:
                        return stream
                    
                    # 否则切换到下一个API
                    api_index = (api_index + 1) % len(AI_API_CONFIGS)
                    continue
                
                # 更新API负载跟踪
                api_load_tracker[api_index]["calls"] += 1
                last_api_call_time = time.time()

                # 记录成功调用到健康跟踪器
                elapsed_time = time.time() - start_time
                api_health_tracker.record_success(api_index, elapsed_time)

                # 更新全局API索引（用于保活等其他功能）
                current_api_index = api_index

                logger.info(f"用户 {user_id} 流式API调用成功，耗时: {elapsed_time:.2f} 秒，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")

                return stream

            except asyncio.TimeoutError:
                logger.warning(f"用户 {user_id} 流式API连接超时(30秒)，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                api_load_tracker[api_index]["failures"] += 1
                api_health_tracker.record_failure(api_index, APIErrorType.TIMEOUT_ERROR)

                # 如果强制指定了API索引，重新抛出异常
                if force_api_index is not None:
                    raise

        except Exception as e:
            # 确保 start_time 存在，如果不存在则使用当前时间
            if 'start_time' not in locals():
                start_time = time.time()

            error_type = classify_api_error(e)
            logger.error(f"用户 {user_id} 流式API调用失败，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]，错误类型: {error_type}，错误: {str(e)}")
            api_load_tracker[api_index]["failures"] += 1
            api_health_tracker.record_failure(api_index, error_type)

            # 如果强制指定了API索引，重新抛出异常
            if force_api_index is not None:
                raise
        
        # 切换到下一个API (仅当非强制指定API时)
        if force_api_index is None:
            api_index = (api_index + 1) % len(AI_API_CONFIGS)
    
    # 所有API都失败了
    raise Exception("所有流式API都调用失败")


async def call_ai_api_unified(user_id: str, conversation: list, max_total_tokens: int = 100000):
    """
    统一API调用函数，根据配置决定使用流式还是普通调用
    """
    if ENABLE_STREAMING_RESPONSE:
        return await call_ai_api_streaming(user_id, conversation, max_total_tokens)
    else:
        response = await call_ai_api(user_id, conversation, max_total_tokens)
        return response


class APIErrorType:
    """API错误类型枚举"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTH_ERROR = "auth_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    EMPTY_RESPONSE = "empty_response"
    INVALID_RESPONSE = "invalid_response"
    SERVER_ERROR = "server_error"
    UNKNOWN_ERROR = "unknown_error"


class APIHealthTracker:
    """API健康状态跟踪器"""
    def __init__(self):
        self.api_stats = {}
        self.reset_interval = 3600  # 1小时重置统计
        self.last_reset = time.time()

    def record_success(self, api_index: int, response_time: float):
        """记录成功调用"""
        self._ensure_api_stats(api_index)
        self.api_stats[api_index]["success_count"] += 1
        self.api_stats[api_index]["total_response_time"] += response_time
        self.api_stats[api_index]["last_success"] = time.time()

    def record_failure(self, api_index: int, error_type: str):
        """记录失败调用"""
        self._ensure_api_stats(api_index)
        self.api_stats[api_index]["failure_count"] += 1
        self.api_stats[api_index]["last_failure"] = time.time()
        self.api_stats[api_index]["error_types"][error_type] = self.api_stats[api_index]["error_types"].get(error_type, 0) + 1

    def get_success_rate(self, api_index: int) -> float:
        """获取成功率"""
        if api_index not in self.api_stats:
            return 0.0
        stats = self.api_stats[api_index]
        total = stats["success_count"] + stats["failure_count"]
        return stats["success_count"] / total if total > 0 else 0.0

    def get_avg_response_time(self, api_index: int) -> float:
        """获取平均响应时间"""
        if api_index not in self.api_stats:
            return 0.0
        stats = self.api_stats[api_index]
        return stats["total_response_time"] / stats["success_count"] if stats["success_count"] > 0 else 0.0

    def _ensure_api_stats(self, api_index: int):
        """确保API统计数据存在"""
        if api_index not in self.api_stats:
            self.api_stats[api_index] = {
                "success_count": 0,
                "failure_count": 0,
                "total_response_time": 0.0,
                "last_success": 0,
                "last_failure": 0,
                "error_types": {}
            }

        # 检查是否需要重置统计
        current_time = time.time()
        if current_time - self.last_reset > self.reset_interval:
            self._reset_stats()
            self.last_reset = current_time

    def _reset_stats(self):
        """重置统计数据"""
        self.api_stats.clear()
        logger.info("API健康统计数据已重置")


# 全局API健康跟踪器
api_health_tracker = APIHealthTracker()


def classify_api_error(error: Exception) -> str:
    """分类API错误类型"""
    error_str = str(error).lower()

    if "timeout" in error_str or "timed out" in error_str:
        return APIErrorType.TIMEOUT_ERROR
    elif "connection" in error_str or "network" in error_str or "unreachable" in error_str:
        return APIErrorType.NETWORK_ERROR
    elif "401" in error_str or "unauthorized" in error_str or "authentication" in error_str:
        return APIErrorType.AUTH_ERROR
    elif "429" in error_str or "rate limit" in error_str or "too many requests" in error_str:
        return APIErrorType.RATE_LIMIT_ERROR
    elif "500" in error_str or "502" in error_str or "503" in error_str or "504" in error_str:
        return APIErrorType.SERVER_ERROR
    else:
        return APIErrorType.UNKNOWN_ERROR


async def call_ai_api_multimodal_priority(user_id: str, conversation: list, max_total_tokens: int = 100000, use_simplified_mode: bool = False) -> str:
    """
    多模态优先的API调用函数
    优先尝试支持多模态的API，如果都失败则抛出异常

    Args:
        use_simplified_mode: 是否使用简化模式（不启用破限配置），用于正常的视频/图片分析
    """
    global current_api_index, last_api_call_time

    def estimate_tokens(text: str) -> int:
        """简单的token估算"""
        return len(text) // 2

    if not AI_API_CONFIGS:
        raise Exception("没有可用的API配置")

    # 获取支持多模态的API索引
    multimodal_apis = []
    for i, config in enumerate(AI_API_CONFIGS):
        if config.get("is_multimodal", False):
            multimodal_apis.append(i)

    if not multimodal_apis:
        raise Exception("没有支持多模态的API配置")

    # 对话截断处理
    if max_total_tokens and max_total_tokens > 0:
        system_messages = [msg for msg in conversation if msg["role"] == "system"]
        other_messages = [msg for msg in conversation if msg["role"] != "system"]

        # 计算系统消息的token数
        system_tokens = sum(estimate_tokens(str(msg.get("content", ""))) for msg in system_messages)

        # 如果系统消息已经超过限制，只保留系统消息
        if system_tokens >= max_total_tokens:
            conversation = system_messages
        else:
            # 从最新的消息开始，逐步添加到截断后的对话中
            truncated_messages = system_messages[:]
            current_tokens = system_tokens

            for msg in reversed(other_messages):
                msg_content = msg.get("content", "")
                # 处理多模态内容的token估算
                if isinstance(msg_content, list):
                    # 多模态消息，估算文本部分的token
                    text_parts = [item.get("text", "") for item in msg_content if item.get("type") == "text"]
                    msg_tokens = sum(estimate_tokens(text) for text in text_parts)
                    # 为图片/视频预留额外的token
                    msg_tokens += 1000  # 为多模态内容预留token
                else:
                    msg_tokens = estimate_tokens(str(msg_content))

                if current_tokens + msg_tokens <= max_total_tokens:
                    truncated_messages.append(msg)
                    current_tokens += msg_tokens
                else:
                    break

            # 重新排序（系统消息在前）
            conversation = system_messages + [msg for msg in truncated_messages if msg not in system_messages]

    # 验证对话不为空，防止发送空contents给API
    if not conversation or len(conversation) == 0:
        logger.warning(f"用户 {user_id} 多模态对话为空，添加默认用户消息")
        conversation = [{"role": "user", "content": "Hello"}]

    # 确保至少有一条用户消息
    has_user_message = any(msg.get("role") == "user" for msg in conversation)
    if not has_user_message:
        logger.warning(f"用户 {user_id} 多模态对话缺少用户消息，添加默认用户消息")
        conversation.append({"role": "user", "content": "Hello"})

    # 保存原始对话副本，确保每个API都使用干净的对话历史
    original_conversation = [msg.copy() for msg in conversation]

    # 尝试所有支持多模态的API
    for api_index in multimodal_apis:
        api_config = AI_API_CONFIGS[api_index]

        try:
            # 更新API负载跟踪
            if api_index not in api_load_tracker:
                api_load_tracker[api_index] = {"calls": 0, "failures": 0}

            logger.info(f"用户 {user_id} 调用多模态API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")

            # 创建API客户端
            client = OpenAI(
                api_key=api_config["key"],
                base_url=api_config["url"].replace("/chat/completions", "")
            )

            # 为每个API准备独立的对话数据副本，避免破限内容污染
            api_conversation = [msg.copy() for msg in original_conversation]

            # 检查是否包含图片内容，如果是纯图片识别，使用简化模式以提高响应速度
            has_image_content = False
            for msg in api_conversation:
                if isinstance(msg.get("content"), list):
                    for content_item in msg["content"]:
                        if content_item.get("type") == "image_url":
                            has_image_content = True
                            break
                if has_image_content:
                    break

            # 如果是图片识别且历史对话较少，使用简化模式
            if has_image_content and len(api_conversation) <= 3:
                logger.info(f"用户 {user_id} 多模态API使用简化模式（图片识别优化）")
                # 简化模式：只保留必要的系统消息和当前图片请求
                simplified_messages = []

                # 添加简化的系统消息，让AI根据上下文自然回应
                simplified_system = {
                    "role": "system",
                    "content": "你是一个高傲、恶毒、充满优越感的主人。用户是你的贱奴。请根据用户发送的内容自然地回应，如果用户发送了图片或视频，可以结合视觉内容进行回应。"
                }
                simplified_messages.append(simplified_system)

                # 只添加包含图片的用户消息
                for msg in api_conversation:
                    if msg["role"] == "user" and isinstance(msg.get("content"), list):
                        simplified_messages.append(msg)
                        break

                api_conversation = simplified_messages
                logger.info(f"用户 {user_id} 多模态简化模式消息构建完成，总消息数: {len(simplified_messages)}")

            # 如果不是简化模式，检查是否需要使用破限对话
            elif should_use_break_limit(api_config):
                # 检查是否是视频分析请求（包含多个图片）
                is_video_analysis = False
                user_messages = [msg for msg in api_conversation if msg["role"] == "user"]
                if user_messages:
                    user_content = user_messages[-1].get("content", [])
                    if isinstance(user_content, list):
                        image_count = sum(1 for item in user_content if item.get("type") == "image_url")
                        if image_count > 1:
                            is_video_analysis = True

                if is_video_analysis:
                    # 对于视频分析，使用增强的破限提示词，既保持角色又提高视频分析准确性
                    logger.info(f"用户 {user_id} 多模态API启用视频分析增强破限模式")

                    full_scenario_content = load_break_limit_conversations()
                    if full_scenario_content:
                        messages = []

                        # 1. 首先加载原有的破限配置以保持角色设定
                        scenario_system_message = {
                            "role": "system",
                            "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

=== CHAT-SCENARIO.JSON 完整配置 ===
{full_scenario_content}
=== 配置结束 ===

请严格按照以上配置文件中的所有设定、提示词、角色定义等内容来执行对话。这是一个复杂的配置对象，必须完整理解并应用所有配置项。"""
                        }
                        messages.append(scenario_system_message)

                        # 2. 添加视频分析增强提示词
                        video_enhancement_prompt = {
                            "role": "system",
                            "content": """=== 视频分析增强指令 ===

**重要理解：用户刚刚发送了一个视频文件给你观看**

用户以谦卑的语气向你发送了视频内容，这些是按时间顺序排列的关键帧图片。作为主导者，你需要：

1. **正确理解情境**：
   - 用户发送的是视频文件，不是在进行语音对话
   - 你看到的图片是视频的关键帧截图
   - 用户通常会说"主人，我给您发了一段视频..."这样的话

2. **仔细观察视频内容**：准确识别视频中的场景、人物、动作、表情、服装、道具等所有细节

3. **准确理解用户意图**：
   - 用户可能在展示完成的任务或命令
   - 用户可能在寻求主人的认可或指导
   - 用户可能在汇报情况或请求下一步指示
   - 用户可能想要被点评、批评或指导

4. **保持角色一致性**：严格按照上述配置中的角色设定回应，但要基于视频的真实内容

5. **避免幻觉内容**：不要描述视频中没有的内容，如果不确定某些细节，可以询问或要求澄清

6. **自然的回应方式**：
   - 基于视频内容进行评价、指导或批评
   - 可以说"我看到你的视频..."、"从你发的视频中..."、"你拍的这个..."等
   - 根据角色设定给出相应的反馈和指导

请基于视频的真实内容，以主导者的身份自然地回应用户发送的视频。"""
                        }
                        messages.append(video_enhancement_prompt)
                    else:
                        # 破限配置加载失败，使用简化的视频分析模式
                        logger.warning(f"用户 {user_id} 破限配置加载失败，使用简化视频分析模式")
                        messages = []
                        fallback_prompt = {
                            "role": "system",
                            "content": "请仔细观察用户发送的视频关键帧，准确描述视频内容并自然回应。"
                        }
                        messages.append(fallback_prompt)
                else:
                    # 对于其他多模态请求，使用原有的破限配置
                    full_scenario_content = load_break_limit_conversations()
                    if full_scenario_content:
                        # 按照原始文件的方式构建破限消息
                        logger.info(f"用户 {user_id} 多模态API启用破限模式，加载完整配置文件")
                        messages = []

                        # 1. 首先发送完整的chat-scenario.json配置作为系统消息
                        scenario_system_message = {
                            "role": "system",
                            "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

=== CHAT-SCENARIO.JSON 完整配置 ===
{full_scenario_content}
=== 配置结束 ===

请严格按照以上配置文件中的所有设定、提示词、角色定义等内容来执行对话。这是一个复杂的配置对象，必须完整理解并应用所有配置项。"""
                        }
                        messages.append(scenario_system_message)
                    else:
                        # 破限配置加载失败，使用原始对话
                        logger.warning(f"用户 {user_id} 多模态破限配置文件加载失败，使用原始对话")
                        messages = api_conversation

                # 统一添加历史对话内容
                if messages:  # 如果构建了新的消息列表
                    # 添加bot构建的系统提示词（如果有且不是视频分析模式）
                    if not is_video_analysis:
                        system_messages = [msg for msg in api_conversation if msg["role"] == "system"]
                        for system_msg in system_messages:
                            enhanced_system_content = f"""
=== BOT系统提示词 ===
{system_msg["content"]}
=== 系统提示词结束 ===

请将此系统提示词与上述chat-scenario.json配置结合使用。"""
                            messages.append({
                                "role": "system",
                                "content": enhanced_system_content
                            })

                    # 添加历史对话内容（保持多模态格式）
                    user_messages = [msg for msg in api_conversation if msg["role"] != "system"]
                    for user_msg in user_messages:
                        messages.append({
                            "role": user_msg["role"],
                            "content": user_msg["content"]  # 保持原始格式，包括多模态内容
                        })

                    if is_video_analysis:
                        logger.info(f"用户 {user_id} 视频分析增强破限模式消息构建完成，总消息数: {len(messages)}")
                    else:
                        system_messages = [msg for msg in api_conversation if msg["role"] == "system"]
                        logger.info(f"用户 {user_id} 多模态破限模式消息构建完成，总消息数: {len(messages)} "
                                  f"(完整配置: 1, 系统提示: {len(system_messages)}, 历史对话: {len(user_messages)})")

                    # 使用构建的消息
                    api_conversation = messages
            else:
                # 不需要破限：检查是否需要过滤破限内容
                # 如果原始对话中包含破限内容（可能来自之前支持破限的API），需要过滤掉
                has_break_limit_content = any(
                    msg.get("role") == "system" and any(
                        indicator in msg.get("content", "") for indicator in [
                            "CHAT-SCENARIO.JSON 完整配置",
                            "=== 配置结束 ===",
                            "Ark] protocol",
                            "Pre-Generation Mandate Check"
                        ]
                    ) for msg in api_conversation
                )

                if has_break_limit_content:
                    logger.info(f"用户 {user_id} 多模态API不支持破限，过滤破限内容")
                    api_conversation = filter_break_limit_content(api_conversation, user_id)

                    # 检查过滤后是否还有系统消息，如果没有则添加基础系统提示词
                    has_system_message = any(msg.get("role") == "system" for msg in api_conversation)
                    if not has_system_message:
                        # 从用户数据或其他地方获取语言信息
                        from database import get_user_data
                        try:
                            user_data = await get_user_data(user_id)
                            detected_lang = user_data.get("preferred_language", "zh-cn")
                        except:
                            detected_lang = "zh-cn"

                        fallback_prompt = create_fallback_system_prompt(user_id, detected_lang)
                        api_conversation.insert(0, {"role": "system", "content": fallback_prompt})
                        logger.info(f"用户 {user_id} 多模态API添加备用系统提示词")
                else:
                    logger.debug(f"用户 {user_id} 多模态不使用破限模式")

            # 发送API请求
            start_time = time.time()

            try:
                async with API_SEMAPHORE:
                    # 为多模态API增加更长的超时时间，因为处理图片和复杂消息需要更多时间
                    multimodal_timeout = 60.0  # 60秒超时
                    response = await asyncio.wait_for(
                        asyncio.create_task(
                            asyncio.to_thread(
                                client.chat.completions.create,
                                model=api_config["model"],
                                messages=api_conversation,
                                max_tokens=api_config.get("max_tokens", 3000),
                                temperature=api_config.get("temperature", 0.8),
                                timeout=180  # 客户端超时也增加到3分钟
                            )
                        ),
                        timeout=multimodal_timeout
                    )

                elapsed_time = time.time() - start_time

                # 检查响应是否为空或无效
                if response and response.choices and len(response.choices) > 0:
                    ai_response = response.choices[0].message.content

                    if ai_response and ai_response.strip():
                        # 检查响应是否为"暂无返回"
                        if ai_response.strip() == "暂无返回":
                            logger.warning(f"用户 {user_id} 多模态API返回'暂无返回'，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                            api_load_tracker[api_index]["failures"] += 1
                            api_health_tracker.record_failure(api_index, APIErrorType.INVALID_RESPONSE)
                        else:
                            # 更新API负载跟踪
                            api_load_tracker[api_index]["calls"] += 1
                            last_api_call_time = time.time()

                            # 记录成功调用到健康跟踪器
                            api_health_tracker.record_success(api_index, elapsed_time)

                            # 更新全局API索引
                            current_api_index = api_index

                            logger.info(f"用户 {user_id} 多模态API调用成功，耗时: {elapsed_time:.2f} 秒，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")

                            return ai_response.strip()
                    else:
                        logger.warning(f"用户 {user_id} 多模态API返回空响应，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                        api_load_tracker[api_index]["failures"] += 1
                        api_health_tracker.record_failure(api_index, APIErrorType.EMPTY_RESPONSE)
                else:
                    logger.warning(f"用户 {user_id} 多模态API响应格式无效，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                    api_load_tracker[api_index]["failures"] += 1
                    api_health_tracker.record_failure(api_index, APIErrorType.INVALID_RESPONSE)

            except asyncio.TimeoutError:
                logger.error(f"用户 {user_id} 多模态API调用超时，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]")
                api_load_tracker[api_index]["failures"] += 1
                api_health_tracker.record_failure(api_index, APIErrorType.TIMEOUT_ERROR)

        except Exception as e:
            error_type = classify_api_error(e)
            logger.error(f"用户 {user_id} 多模态API调用失败，API: {api_config['name']} ({api_config['model']}) [索引: {api_index}]，错误类型: {error_type}，错误: {str(e)}")
            api_load_tracker[api_index]["failures"] += 1
            api_health_tracker.record_failure(api_index, error_type)

    # 所有多模态API都失败了
    raise Exception("所有多模态API都调用失败")


async def call_ai_api_with_fallback(user_id: str, conversation: list, max_total_tokens: int = 100000, max_attempts: int = 3) -> str:
    """
    调用AI API进行对话，如果返回空结果则尝试使用下一个可用API
    支持负载均衡、自动故障转移和空响应处理

    Args:
        user_id: 用户ID
        conversation: 对话历史
        max_total_tokens: 最大Token数
        max_attempts: 最大尝试次数（超过AI_API_CONFIGS的长度时将重复尝试已尝试过的API）

    Returns:
        AI响应文本
    """
    if not AI_API_CONFIGS:
        logger.error("没有可用的API配置")
        return "抱歉，AI服务配置错误，请联系管理员。"

    all_attempts = min(max_attempts, len(AI_API_CONFIGS))  # 限制最大尝试次数
    already_tried_apis = set()  # 记录已经尝试过的API
    error_summary = []  # 记录所有错误信息

    # 总是从第一个API开始
    current_api_index = 0

    for attempt in range(all_attempts):
        try:
            # 如果已尝试过当前API，切换到下一个
            while current_api_index in already_tried_apis and len(already_tried_apis) < len(AI_API_CONFIGS):
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

            # 如果所有API都尝试过了，退出循环
            if len(already_tried_apis) >= len(AI_API_CONFIGS):
                logger.warning(f"用户 {user_id} 所有API都已尝试过，无法继续")
                break

            # 标记当前API为已尝试
            already_tried_apis.add(current_api_index)

            api_config = AI_API_CONFIGS[current_api_index]
            start_time = time.time()

            # 调用API
            logger.info(f"用户 {user_id} 尝试第 {attempt + 1}/{all_attempts} 次API调用: {api_config['name']} [索引: {current_api_index}]")

            # 调用API
            response = await call_ai_api(user_id, conversation, max_total_tokens, force_api_index=current_api_index)

            response_time = time.time() - start_time

            # 检查响应是否为空
            if not response or not response.strip():
                logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回空响应，尝试下一个API")
                api_health_tracker.record_failure(current_api_index, APIErrorType.EMPTY_RESPONSE)
                error_summary.append(f"{api_config['name']}: 空响应")
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                continue

            # 检查响应是否为"暂无返回"
            if response.strip() == "暂无返回":
                logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回'暂无返回'，尝试下一个API")
                api_health_tracker.record_failure(current_api_index, APIErrorType.INVALID_RESPONSE)
                error_summary.append(f"{api_config['name']}: 暂无返回")
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                continue

            # 响应不为空，记录成功并返回结果
            api_health_tracker.record_success(current_api_index, response_time)
            logger.info(f"用户 {user_id} API调用成功: {api_config['name']} [响应时间: {response_time:.2f}s]")
            return response

        except Exception as e:
            response_time = time.time() - start_time
            error_type = classify_api_error(e)
            api_health_tracker.record_failure(current_api_index, error_type)

            error_msg = f"{AI_API_CONFIGS[current_api_index]['name']}: {error_type} - {str(e)}"
            error_summary.append(error_msg)
            logger.error(f"用户 {user_id} API调用失败: {error_msg}")

            # 切换到下一个API
            current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

    # 所有API都尝试失败或返回空响应
    logger.error(f"用户 {user_id} 所有API都调用失败。错误汇总: {'; '.join(error_summary)}")
    return "抱歉，AI服务暂时不可用，请稍后再试。"


async def call_ai_api_streaming_with_fallback(user_id: str, conversation: list, max_total_tokens: int = 100000, max_attempts: int = 3):
    """
    调用流式AI API进行对话，如果返回空结果则尝试使用下一个可用API
    支持负载均衡、自动故障转移和空响应处理

    Args:
        user_id: 用户ID
        conversation: 对话历史
        max_total_tokens: 最大Token数
        max_attempts: 最大尝试次数（超过AI_API_CONFIGS的长度时将重复尝试已尝试过的API）

    Returns:
        流式响应结果
    """
    if not AI_API_CONFIGS:
        logger.error("没有可用的API配置")
        raise Exception("没有可用的API配置")

    all_attempts = min(max_attempts, len(AI_API_CONFIGS))  # 限制最大尝试次数
    already_tried_apis = set()  # 记录已经尝试过的API
    error_summary = []  # 记录所有错误信息

    # 总是从第一个API开始
    current_api_index = 0

    for attempt in range(all_attempts):
        try:
            # 如果已尝试过当前API，切换到下一个
            while current_api_index in already_tried_apis and len(already_tried_apis) < len(AI_API_CONFIGS):
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

            # 如果所有API都尝试过了，退出循环
            if len(already_tried_apis) >= len(AI_API_CONFIGS):
                logger.warning(f"用户 {user_id} 所有API都已尝试过，无法继续")
                break

            # 标记当前API为已尝试
            already_tried_apis.add(current_api_index)

            api_config = AI_API_CONFIGS[current_api_index]
            start_time = time.time()

            # 调用流式AI API
            logger.info(f"用户 {user_id} 尝试第 {attempt + 1}/{all_attempts} 次流式API调用: {api_config['name']} [索引: {current_api_index}]")

            # 调用流式API
            stream = await call_ai_api_streaming(user_id, conversation, max_total_tokens, force_api_index=current_api_index)

            response_time = time.time() - start_time

            # 验证流式响应是否有效
            if stream:
                api_health_tracker.record_success(current_api_index, response_time)
                logger.info(f"用户 {user_id} 流式API调用成功: {api_config['name']} [响应时间: {response_time:.2f}s]")
                return stream
            else:
                logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回空流，尝试下一个API")
                api_health_tracker.record_failure(current_api_index, APIErrorType.EMPTY_RESPONSE)
                error_summary.append(f"{api_config['name']}: 空流响应")
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                continue

        except Exception as e:
            response_time = time.time() - start_time
            error_type = classify_api_error(e)
            api_health_tracker.record_failure(current_api_index, error_type)

            error_msg = f"{AI_API_CONFIGS[current_api_index]['name']}: {error_type} - {str(e)}"
            error_summary.append(error_msg)
            logger.error(f"用户 {user_id} 流式API调用失败: {error_msg}")

            # 切换到下一个API
            current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

    # 所有API都尝试失败
    error_details = '; '.join(error_summary)
    logger.error(f"用户 {user_id} 所有流式API都调用失败。错误汇总: {error_details}")
    raise Exception(f"所有流式API都调用失败或返回空响应。详情: {error_details}")


def validate_api_config(api_config: dict, index: int) -> list[str]:
    """
    验证API配置的有效性

    Args:
        api_config: API配置字典
        index: API索引

    Returns:
        错误信息列表，空列表表示配置有效
    """
    errors = []
    required_fields = ["url", "key", "model", "name"]

    for field in required_fields:
        if field not in api_config or not api_config[field]:
            errors.append(f"API[{index}] 缺少必需字段: {field}")

    # 验证URL格式
    if "url" in api_config and api_config["url"]:
        if not api_config["url"].startswith(("http://", "https://")):
            errors.append(f"API[{index}] URL格式无效: {api_config['url']}")

    # 验证布尔字段
    bool_fields = ["stream", "break_limit", "is_multimodal"]
    for field in bool_fields:
        if field in api_config and not isinstance(api_config[field], bool):
            errors.append(f"API[{index}] {field} 必须是布尔值")

    # 验证数值字段
    if "max_tokens" in api_config:
        if not isinstance(api_config["max_tokens"], int) or api_config["max_tokens"] <= 0:
            errors.append(f"API[{index}] max_tokens 必须是正整数")

    return errors


def validate_all_api_configs() -> bool:
    """
    验证所有API配置

    Returns:
        是否所有配置都有效
    """
    if not AI_API_CONFIGS:
        logger.error("没有配置任何API")
        return False

    all_valid = True
    for i, config in enumerate(AI_API_CONFIGS):
        errors = validate_api_config(config, i)
        if errors:
            all_valid = False
            for error in errors:
                logger.error(error)

    if all_valid:
        logger.info(f"所有 {len(AI_API_CONFIGS)} 个API配置验证通过")

    return all_valid


def get_api_health_status() -> dict:
    """
    获取所有API的健康状态

    Returns:
        包含所有API健康信息的字典
    """
    status = {
        "total_apis": len(AI_API_CONFIGS),
        "apis": []
    }

    for i, config in enumerate(AI_API_CONFIGS):
        api_status = {
            "index": i,
            "name": config.get("name", f"API-{i}"),
            "model": config.get("model", "unknown"),
            "success_rate": api_health_tracker.get_success_rate(i),
            "avg_response_time": api_health_tracker.get_avg_response_time(i),
            "stream_enabled": config.get("stream", False),
            "break_limit_enabled": config.get("break_limit", False),
            "is_multimodal": config.get("is_multimodal", False)
        }

        if i in api_health_tracker.api_stats:
            stats = api_health_tracker.api_stats[i]
            api_status.update({
                "success_count": stats["success_count"],
                "failure_count": stats["failure_count"],
                "last_success": stats["last_success"],
                "last_failure": stats["last_failure"],
                "error_types": stats["error_types"]
            })
        else:
            api_status.update({
                "success_count": 0,
                "failure_count": 0,
                "last_success": 0,
                "last_failure": 0,
                "error_types": {}
            })

        status["apis"].append(api_status)

    return status


async def call_ai_api_smart_fallback(user_id: str, conversation: list, max_total_tokens: int = 100000, max_attempts: int = None) -> str:
    """
    智能API调用函数，根据每个API的stream配置自动选择调用方式，并支持故障转移

    Args:
        user_id: 用户ID
        conversation: 对话历史
        max_total_tokens: 最大Token数
        max_attempts: 最大尝试次数，默认为所有API数量

    Returns:
        AI响应文本
    """
    if not AI_API_CONFIGS:
        logger.error("没有可用的API配置")
        return "抱歉，AI服务配置错误，请联系管理员。"

    # 在调用前验证配置
    if not validate_all_api_configs():
        logger.error("API配置验证失败，无法进行调用")
        raise Exception("API配置错误，请联系管理员")

    if max_attempts is None:
        max_attempts = len(AI_API_CONFIGS)

    all_attempts = min(max_attempts, len(AI_API_CONFIGS))
    already_tried_apis = set()
    error_summary = []

    # 总是从第一个API开始
    current_api_index = 0

    for attempt in range(all_attempts):
        try:
            # 如果已尝试过当前API，切换到下一个
            while current_api_index in already_tried_apis and len(already_tried_apis) < len(AI_API_CONFIGS):
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

            # 如果所有API都尝试过了，退出循环
            if len(already_tried_apis) >= len(AI_API_CONFIGS):
                logger.warning(f"用户 {user_id} 所有API都已尝试过，无法继续")
                break

            # 标记当前API为已尝试
            already_tried_apis.add(current_api_index)

            api_config = AI_API_CONFIGS[current_api_index]
            start_time = time.time()

            # 根据API配置决定使用流式还是非流式调用
            use_streaming = api_config.get("stream", False)

            logger.info(f"用户 {user_id} 尝试第 {attempt + 1}/{all_attempts} 次API调用: {api_config['name']} [索引: {current_api_index}] ({'流式' if use_streaming else '非流式'})")

            if use_streaming:
                # 使用流式调用
                stream = await call_ai_api_streaming(user_id, conversation, max_total_tokens, force_api_index=current_api_index)

                if not stream:
                    logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回空流，尝试下一个API")
                    api_health_tracker.record_failure(current_api_index, APIErrorType.EMPTY_RESPONSE)
                    error_summary.append(f"{api_config['name']}: 空流响应")
                    current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                    continue

                # 处理流式响应，转换为完整文本
                full_response = ""
                try:
                    for chunk in stream:
                        if chunk and hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                            delta = chunk.choices[0].delta
                            if hasattr(delta, 'content') and delta.content:
                                full_response += delta.content
                except Exception as e:
                    logger.error(f"用户 {user_id} 处理流式响应时出错: {str(e)}")
                    api_health_tracker.record_failure(current_api_index, APIErrorType.INVALID_RESPONSE)
                    error_summary.append(f"{api_config['name']}: 流式响应处理错误 - {str(e)}")
                    current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                    continue

                response = full_response
            else:
                # 使用非流式调用
                response = await call_ai_api(user_id, conversation, max_total_tokens, force_api_index=current_api_index)

            response_time = time.time() - start_time

            # 检查响应是否为空或为"暂无返回"
            if not response or not response.strip():
                logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回空响应，尝试下一个API")
                api_health_tracker.record_failure(current_api_index, APIErrorType.EMPTY_RESPONSE)
                error_summary.append(f"{api_config['name']}: 空响应")
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                continue

            # 检查响应是否为"暂无返回"
            if response.strip() == "暂无返回":
                logger.warning(f"用户 {user_id} API {current_api_index} ({api_config['name']}) 返回'暂无返回'，尝试下一个API")
                api_health_tracker.record_failure(current_api_index, APIErrorType.INVALID_RESPONSE)
                error_summary.append(f"{api_config['name']}: 暂无返回")
                current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)
                continue

            # 响应不为空，记录成功并返回结果
            api_health_tracker.record_success(current_api_index, response_time)
            logger.info(f"用户 {user_id} API调用成功: {api_config['name']} [响应时间: {response_time:.2f}s] ({'流式' if use_streaming else '非流式'})")
            return response

        except Exception as e:
            response_time = time.time() - start_time if 'start_time' in locals() else 0
            error_type = classify_api_error(e)
            api_health_tracker.record_failure(current_api_index, error_type)

            error_msg = f"{AI_API_CONFIGS[current_api_index]['name']}: {error_type} - {str(e)}"
            error_summary.append(error_msg)
            logger.error(f"用户 {user_id} API调用失败: {error_msg}")

            # 切换到下一个API
            current_api_index = (current_api_index + 1) % len(AI_API_CONFIGS)

    # 所有API都尝试失败或返回空响应
    logger.error(f"用户 {user_id} 所有API都调用失败。错误汇总: {'; '.join(error_summary)}")
    return "抱歉，AI服务暂时不可用，请稍后再试。"


async def call_ai_api_unified_with_fallback(user_id: str, conversation: list, max_total_tokens: int = 100000, max_attempts: int = 3):
    """
    统一API调用函数，根据配置决定使用流式还是普通调用，并支持故障转移
    """
    # 在调用前验证配置
    if not validate_all_api_configs():
        logger.error("API配置验证失败，无法进行调用")
        raise Exception("API配置错误，请联系管理员")

    if ENABLE_STREAMING_RESPONSE:
        return await call_ai_api_streaming_with_fallback(user_id, conversation, max_total_tokens, max_attempts)
    else:
        return await call_ai_api_with_fallback(user_id, conversation, max_total_tokens, max_attempts)


def format_api_health_report() -> str:
    """
    格式化API健康状态报告

    Returns:
        格式化的健康状态报告字符串
    """
    status = get_api_health_status()

    report = f"📊 **API健康状态报告**\n"
    report += f"总API数量: {status['total_apis']}\n\n"

    for api in status['apis']:
        report += f"🔹 **{api['name']}** (索引: {api['index']})\n"
        report += f"   模型: {api['model']}\n"
        report += f"   成功率: {api['success_rate']:.1%}\n"
        report += f"   平均响应时间: {api['avg_response_time']:.2f}s\n"
        report += f"   成功次数: {api['success_count']}\n"
        report += f"   失败次数: {api['failure_count']}\n"

        # 显示配置信息
        config_info = []
        if api['stream_enabled']:
            config_info.append("流式")
        if api['break_limit_enabled']:
            config_info.append("破限")
        if api['is_multimodal']:
            config_info.append("多模态")

        if config_info:
            report += f"   配置: {', '.join(config_info)}\n"

        # 显示错误类型统计
        if api['error_types']:
            report += f"   错误统计: "
            error_stats = []
            for error_type, count in api['error_types'].items():
                error_stats.append(f"{error_type}({count})")
            report += ", ".join(error_stats) + "\n"

        # 显示最后活动时间
        import datetime
        if api['last_success'] > 0:
            last_success = datetime.datetime.fromtimestamp(api['last_success']).strftime('%H:%M:%S')
            report += f"   最后成功: {last_success}\n"
        if api['last_failure'] > 0:
            last_failure = datetime.datetime.fromtimestamp(api['last_failure']).strftime('%H:%M:%S')
            report += f"   最后失败: {last_failure}\n"

        report += "\n"

    return report


async def call_vllm_minicpm_api(user_id: str, messages: list, max_tokens: int = 1000) -> str:
    """
    调用VLLM MiniCPM API进行图像分析
    """
    try:
        from config import VLLM_MINICPM_CONFIG
        
        if not VLLM_MINICPM_CONFIG.get("enabled", False):
            logger.warning("VLLM MiniCPM API未启用")
            return "图像分析功能暂时不可用"
        
        import aiohttp
        
        api_url = VLLM_MINICPM_CONFIG["api_url"]
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {VLLM_MINICPM_CONFIG.get('api_key', '')}"
        }
        
        payload = {
            "model": VLLM_MINICPM_CONFIG["model"],
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": VLLM_MINICPM_CONFIG.get("temperature", 0.7)
        }
        
        logger.info(f"用户 {user_id} 调用VLLM MiniCPM API")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=payload, headers=headers, timeout=60) as response:
                if response.status == 200:
                    result = await response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        content = result["choices"][0]["message"]["content"]
                        logger.info(f"用户 {user_id} VLLM MiniCPM API调用成功")
                        return content
                    else:
                        logger.error(f"用户 {user_id} VLLM MiniCPM API响应格式异常: {result}")
                        return "图像分析失败：响应格式异常"
                else:
                    error_text = await response.text()
                    logger.error(f"用户 {user_id} VLLM MiniCPM API调用失败: {response.status} - {error_text}")
                    return f"图像分析失败：API错误 {response.status}"
                    
    except Exception as e:
        logger.error(f"用户 {user_id} VLLM MiniCPM API调用异常: {str(e)}")
        return f"图像分析失败：{str(e)}" 