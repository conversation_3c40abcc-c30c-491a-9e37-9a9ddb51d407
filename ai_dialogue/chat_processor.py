"""
对话处理核心模块 - 负责核心AI对话逻辑处理
"""

import asyncio
import time
from datetime import datetime
from database import get_user_data, save_user_data
from logging_config import logger
from enhanced_memory import enhanced_memory, MemoryType
from utils import process_ai_response, filter_xml_tags
from config import ENABLE_STREAMING_RESPONSE


async def check_membership_and_deduct_credits(user_id: str, logger, bot=None) -> tuple[bool, str, dict]:
    """
    检查会员状态并扣除积分
    
    Returns:
        tuple[bool, str, dict]: (是否可以继续, 错误消息, 扣除信息)
        扣除信息格式: {"deducted": bool, "amount": int, "from": str}
    """
    try:
        from handlers.base import get_pay_menu
        from utils import send_with_retry
        
        user_data = await get_user_data(user_id)
        lang = user_data.get("preferred_language", "zh-cn")
        
        # 初始化扣除信息
        deduct_info = {"deducted": False, "amount": 0, "from": ""}
        
        # 检查会员状态
        is_member = False
        membership_expiry = user_data.get("membership_expiry")
        
        # 首先检查是否为永久会员
        if user_data.get("permanent_member", False):
            is_member = True
            logger.info(f"用户 {user_id} 是永久会员，不受积分限制")
        elif membership_expiry:
            try:
                expiry_date = datetime.fromisoformat(membership_expiry)
                if datetime.now() < expiry_date:
                    is_member = True
                    logger.info(f"用户 {user_id} 是会员，会员到期时间: {membership_expiry}")
            except (ValueError, TypeError) as e:
                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {membership_expiry}，错误: {str(e)}")
                user_data["membership_expiry"] = None
                await save_user_data(user_id, user_data)
        
        # 如果是会员，不扣积分
        if is_member:
            logger.info(f"用户 {user_id} 是会员，不扣除任何狗粮")
            return True, "", deduct_info
        
        # 普通用户需要扣除积分
        dog_food_needed = 1  # 每次对话统一扣除1狗粮
        
        # 检查每日免费狗粮重置
        from datetime import date
        today = date.today().isoformat()
        if user_data.get("daily_free_dog_food_date") != today:
            user_data["daily_free_dog_food"] = 3
            user_data["daily_free_dog_food_date"] = today
            logger.info(f"用户 {user_id} 每日免费狗粮重置为3")
        
        # 优先使用每日免费狗粮
        if user_data.get("daily_free_dog_food", 0) >= dog_food_needed:
            user_data["daily_free_dog_food"] -= dog_food_needed
            deduct_info = {"deducted": True, "amount": dog_food_needed, "from": "daily_free"}
            logger.info(f"用户 {user_id} 扣除 {dog_food_needed} 每日免费狗粮🦴，剩余: {user_data['daily_free_dog_food']}")
        # 其次使用充值狗粮
        elif user_data.get("dog_food", 0) >= dog_food_needed:
            user_data["dog_food"] -= dog_food_needed
            deduct_info = {"deducted": True, "amount": dog_food_needed, "from": "paid"}
            logger.info(f"用户 {user_id} 扣除 {dog_food_needed} 充值狗粮🦴，剩余: {user_data['dog_food']}")
        else:
            # 狗粮不足，终止对话
            total_available = user_data.get("daily_free_dog_food", 0) + user_data.get("dog_food", 0)
            logger.info(f"用户 {user_id} 狗粮不足，终止对话。需要: {dog_food_needed}, 可用: {total_available}")
            
            # 准备错误消息
            error_message = (
                f"贱奴！你的狗粮🦴不足，滚去充值或分享赚狗粮！"
                if lang == "zh-cn" else
                f"Filthy dog! Your dog food🦴 is insufficient, go recharge or share to earn more!"
            )
            
            # 如果提供了bot参数，添加支付菜单提示
            if bot:
                reply_markup, hint = await get_pay_menu(user_id)
                error_message += f"\n{hint}"
                
                # 将回复标记添加到deduct_info中，让调用者处理发送
                deduct_info["reply_markup"] = reply_markup
            
            return False, error_message, deduct_info
        
        # 保存用户数据
        await save_user_data(user_id, user_data)
        return True, "", deduct_info
        
    except Exception as e:
        logger.error(f"用户 {user_id} 积分检查和扣除失败: {str(e)}")
        error_msg = "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！"
        return False, error_msg, {"deducted": False, "amount": 0, "from": ""}


async def refund_credits(user_id: str, deduct_info: dict, logger):
    """
    返还用户积分
    
    Args:
        user_id: 用户ID
        deduct_info: 扣除信息，格式: {"deducted": bool, "amount": int, "from": str}
        logger: 日志记录器
    """
    try:
        if not deduct_info.get("deducted", False):
            return  # 没有扣除过积分，无需返还
        
        user_data = await get_user_data(user_id)
        amount = deduct_info.get("amount", 0)
        from_type = deduct_info.get("from", "")
        
        if from_type == "daily_free":
            user_data["daily_free_dog_food"] = user_data.get("daily_free_dog_food", 0) + amount
            logger.info(f"用户 {user_id} 对话失败，返还 {amount} 每日免费狗粮🦴，当前余额: {user_data['daily_free_dog_food']}")
        elif from_type == "paid":
            user_data["dog_food"] = user_data.get("dog_food", 0) + amount
            logger.info(f"用户 {user_id} 对话失败，返还 {amount} 充值狗粮🦴，当前余额: {user_data['dog_food']}")
        else:
            logger.warning(f"用户 {user_id} 返还积分时遇到未知扣除类型: {from_type}")
            return
        
        await save_user_data(user_id, user_data)
        logger.info(f"用户 {user_id} 积分返还成功")
        
    except Exception as e:
        logger.error(f"用户 {user_id} 积分返还失败: {str(e)}")


async def chat_with_ai(user_id: str, message: str, deduct_type: str, logger, bot=None) -> tuple[list[str], str | dict]:
    """
    传统AI对话处理
    """
    deduct_info = {"deducted": False, "amount": 0, "from": ""}
    
    try:
        from .api_manager import call_ai_api_with_fallback
        from .content_processor import preprocess_message, build_system_prompt, comprehensive_content_filter
        
        # 检查会员状态并扣除积分
        can_continue, error_msg, deduct_info = await check_membership_and_deduct_credits(user_id, logger, bot)
        if not can_continue:
            # 如果有支付菜单，将其加入到deduct_type中以便发送回复按钮
            if "reply_markup" in deduct_info:
                final_deduct = {"deduct_type": deduct_type, "reply_markup": deduct_info["reply_markup"]}
                return [error_msg], final_deduct
            return [error_msg], deduct_type
        
        # 预处理消息
        processed_message, detected_lang, warning_msg = await preprocess_message(user_id, message, logger)
        
        if warning_msg:
            # 预处理失败不返还积分（这是用户输入问题）
            return [warning_msg], deduct_type
        
        # 获取用户数据
        user_data = await get_user_data(user_id)
        
        # 检查禁言状态
        if user_data["ban_until"] and datetime.now().isoformat() < user_data["ban_until"]:
            logger.info(f"用户 {user_id} 被禁言，终止对话")
            # 禁言状态不返还积分（这是用户状态问题）
            return ["贱奴！你被爷禁言了，滚去等着！"], deduct_type
        
        # 获取相关记忆
        try:
            related_memories = await enhanced_memory.get_related_memories(user_id, processed_message, limit=5)
        except Exception as e:
            logger.error(f"用户 {user_id} 获取记忆失败: {str(e)}")
            related_memories = []
        
        # 构建系统提示词
        system_prompt = await build_system_prompt(
            user_id, processed_message, user_data, related_memories, 
            len(user_data.get("conversation", [])) == 0, detected_lang
        )
        
        # 构建对话历史
        conversation = [{"role": "system", "content": system_prompt}]
        
        # 添加历史对话
        if user_data.get("conversation"):
            conversation.extend(user_data["conversation"][-10:])  # 保留最近10轮对话
        
        # 添加当前用户消息
        conversation.append({"role": "user", "content": processed_message})
        
        # 调用AI API (使用带自动故障转移的版本)
        # 尝试所有可用的API，不限制次数
        from config import AI_API_CONFIGS
        max_attempts = len(AI_API_CONFIGS) if AI_API_CONFIGS else 3
        ai_response = await call_ai_api_with_fallback(user_id, conversation, max_attempts=max_attempts)

        # 检查响应是否为"暂无返回"，如果是则返回空列表以触发重试机制
        if ai_response.strip() == "暂无返回":
            logger.warning(f"用户 {user_id} API响应为'暂无返回'，返回空列表以触发重试机制")
            return [], deduct_type

        # 应用综合内容过滤
        filtered_response = comprehensive_content_filter(ai_response, detected_lang)

        # 检查过滤后的结果是否为空，如果为空则返回空列表以触发重试机制
        if not filtered_response or not filtered_response.strip():
            logger.warning(f"用户 {user_id} AI响应经过过滤后为空，返回空列表以触发重试机制")
            # 不再保存空响应到对话历史和记忆
            return [], deduct_type
        
        # 处理AI响应
        response_sentences = process_ai_response(filtered_response)
        
        # 保存对话历史
        user_data["conversation"].extend([
            {"role": "user", "content": processed_message},
            {"role": "assistant", "content": filtered_response}
        ])
        
        # 限制对话历史长度
        if len(user_data["conversation"]) > 20:
            user_data["conversation"] = user_data["conversation"][-20:]
        
        await save_user_data(user_id, user_data)
        
        # 保存记忆
        try:
            await enhanced_memory.add_structured_memory(
                user_id=user_id,
                content=f"用户: {processed_message}\nAI: {filtered_response}",
                memory_type=MemoryType.CONVERSATION,
                tags=["对话", "chat"],
                emotional_valence=0.0
            )
        except Exception as e:
            logger.error(f"用户 {user_id} 保存记忆失败: {str(e)}")
        
        return response_sentences, deduct_type
        
    except Exception as e:
        logger.error(f"用户 {user_id} AI对话处理失败: {str(e)}")
        
        # 对话失败，返还积分
        await refund_credits(user_id, deduct_info, logger)
        
        # 根据是否实际扣除了积分来确定消息内容
        if deduct_info.get("deducted", False):
            error_msg = "贱狗！主人的系统出故障了，你的狗粮已返还，快去重新舔主人的脚趾！"
        else:
            error_msg = "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！"
        return [error_msg], deduct_type


async def chat_with_ai_streaming(user_id: str, message: str, deduct_type: str, logger, bot=None) -> tuple[list[str], str | dict]:
    """
    流式AI对话处理
    """
    deduct_info = {"deducted": False, "amount": 0, "from": ""}
    
    try:
        from .api_manager import call_ai_api_streaming_with_fallback
        from .content_processor import preprocess_message, build_system_prompt, comprehensive_content_filter
        
        # 检查会员状态并扣除积分
        can_continue, error_msg, deduct_info = await check_membership_and_deduct_credits(user_id, logger, bot)
        if not can_continue:
            # 如果有支付菜单，将其加入到deduct_type中以便发送回复按钮
            if "reply_markup" in deduct_info:
                final_deduct = {"deduct_type": deduct_type, "reply_markup": deduct_info["reply_markup"]}
                return [error_msg], final_deduct
            return [error_msg], deduct_type
        
        # 预处理消息
        processed_message, detected_lang, warning_msg = await preprocess_message(user_id, message, logger)
        
        if warning_msg:
            # 预处理失败不返还积分（这是用户输入问题）
            return [warning_msg], deduct_type
        
        # 获取用户数据
        user_data = await get_user_data(user_id)
        
        # 检查禁言状态
        if user_data["ban_until"] and datetime.now().isoformat() < user_data["ban_until"]:
            logger.info(f"用户 {user_id} 被禁言，终止对话")
            # 禁言状态不返还积分（这是用户状态问题）
            return ["贱奴！你被爷禁言了，滚去等着！"], deduct_type
        
        # 获取相关记忆
        try:
            related_memories = await enhanced_memory.get_related_memories(user_id, processed_message, limit=5)
        except Exception as e:
            logger.error(f"用户 {user_id} 获取记忆失败: {str(e)}")
            related_memories = []
        
        # 构建系统提示词
        system_prompt = await build_system_prompt(
            user_id, processed_message, user_data, related_memories, 
            len(user_data.get("conversation", [])) == 0, detected_lang
        )
        
        # 构建对话历史
        conversation = [{"role": "system", "content": system_prompt}]
        
        # 添加历史对话
        if user_data.get("conversation"):
            conversation.extend(user_data["conversation"][-10:])  # 保留最近10轮对话
        
        # 添加当前用户消息
        conversation.append({"role": "user", "content": processed_message})
        
        # 调用流式AI API (使用带自动故障转移的版本)
        # 尝试所有可用的API，不限制次数
        from config import AI_API_CONFIGS
        max_attempts = len(AI_API_CONFIGS) if AI_API_CONFIGS else 3
        stream = await call_ai_api_streaming_with_fallback(user_id, conversation, max_attempts=max_attempts)
        
        # 处理流式响应
        full_response = ""
        try:
            if stream:
                for chunk in stream:
                    if chunk and hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                        delta = chunk.choices[0].delta
                        if hasattr(delta, 'content') and delta.content:
                            full_response += delta.content
            
            # 如果没有有效响应
            if not full_response:
                logger.warning(f"用户 {user_id} 流式API响应为空，返回空列表以触发重试机制")
                return [], deduct_type
        except Exception as e:
            logger.error(f"用户 {user_id} 处理流式响应时出错: {str(e)}")
            return [], deduct_type
            
        # 检查响应是否为"暂无返回"，如果是则返回空列表以触发重试机制
        if full_response.strip() == "暂无返回":
            logger.warning(f"用户 {user_id} 流式API响应为'暂无返回'，返回空列表以触发重试机制")
            return [], deduct_type

        # 应用综合内容过滤
        filtered_response = comprehensive_content_filter(full_response, detected_lang)

        # 检查过滤后的结果是否为空，如果为空则返回空列表以触发重试机制
        if not filtered_response or not filtered_response.strip():
            logger.warning(f"用户 {user_id} 流式AI响应经过过滤后为空，返回空列表以触发重试机制")
            # 不再保存空响应到对话历史和记忆
            return [], deduct_type
        
        # 处理AI响应
        response_sentences = process_ai_response(filtered_response)
        
        # 保存对话历史
        user_data["conversation"].extend([
            {"role": "user", "content": processed_message},
            {"role": "assistant", "content": filtered_response}
        ])
        
        # 限制对话历史长度
        if len(user_data["conversation"]) > 20:
            user_data["conversation"] = user_data["conversation"][-20:]
        
        await save_user_data(user_id, user_data)
        
        # 保存记忆
        try:
            await enhanced_memory.add_structured_memory(
                user_id=user_id,
                content=f"用户: {processed_message}\nAI: {filtered_response}",
                memory_type=MemoryType.CONVERSATION,
                tags=["对话", "chat", "streaming"],
                emotional_valence=0.0
            )
        except Exception as e:
            logger.error(f"用户 {user_id} 保存记忆失败: {str(e)}")
        
        return response_sentences, deduct_type
        
    except Exception as e:
        logger.error(f"用户 {user_id} 流式AI对话处理失败: {str(e)}")
        
        # 对话失败，返还积分
        await refund_credits(user_id, deduct_info, logger)
        
        # 根据是否实际扣除了积分来确定消息内容
        if deduct_info.get("deducted", False):
            error_msg = "贱狗！主人的系统出故障了，你的狗粮已返还，快去重新舔主人的脚趾！"
        else:
            error_msg = "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！"
        return [error_msg], deduct_type


async def chat_with_ai_unified(user_id: str, message: str, deduct_type: str, logger, bot=None) -> tuple[list[str], str | dict]:
    """
    统一AI对话处理
    根据每个API的配置自动选择流式或非流式调用
    """
    deduct_info = {"deducted": False, "amount": 0, "from": ""}

    try:
        from .api_manager import call_ai_api_smart_fallback
        from .content_processor import preprocess_message, build_system_prompt, comprehensive_content_filter

        # 检查会员状态并扣除积分
        can_continue, error_msg, deduct_info = await check_membership_and_deduct_credits(user_id, logger, bot)
        if not can_continue:
            # 如果有支付菜单，将其加入到deduct_type中以便发送回复按钮
            if "reply_markup" in deduct_info:
                final_deduct = {"deduct_type": deduct_type, "reply_markup": deduct_info["reply_markup"]}
                return [error_msg], final_deduct
            return [error_msg], deduct_type

        # 预处理消息
        processed_message, detected_lang, warning_msg = await preprocess_message(user_id, message, logger)

        if warning_msg:
            # 预处理失败不返还积分（这是用户输入问题）
            return [warning_msg], deduct_type

        # 获取用户数据
        user_data = await get_user_data(user_id)

        # 检查禁言状态
        if user_data["ban_until"] and datetime.now().isoformat() < user_data["ban_until"]:
            logger.info(f"用户 {user_id} 被禁言，终止对话")
            # 禁言状态不返还积分（这是用户状态问题）
            return ["贱奴！你被爷禁言了，滚去等着！"], deduct_type

        # 获取相关记忆
        try:
            related_memories = await enhanced_memory.get_related_memories(user_id, processed_message, limit=5)
        except Exception as e:
            logger.error(f"用户 {user_id} 获取记忆失败: {str(e)}")
            related_memories = []

        # 构建系统提示词
        system_prompt = await build_system_prompt(
            user_id, processed_message, user_data, related_memories,
            len(user_data.get("conversation", [])) == 0, detected_lang
        )

        # 构建对话历史
        conversation = [{"role": "system", "content": system_prompt}]

        # 添加历史对话
        if user_data.get("conversation"):
            conversation.extend(user_data["conversation"][-10:])  # 保留最近10轮对话

        # 添加当前用户消息
        conversation.append({"role": "user", "content": processed_message})

        # 使用智能API调用，根据每个API配置自动选择流式或非流式
        ai_response = await call_ai_api_smart_fallback(user_id, conversation)

        # 检查响应是否为"暂无返回"，如果是则返回空列表以触发重试机制
        if ai_response.strip() == "暂无返回":
            logger.warning(f"用户 {user_id} 统一API响应为'暂无返回'，返回空列表以触发重试机制")
            return [], deduct_type

        # 应用综合内容过滤
        filtered_response = comprehensive_content_filter(ai_response, detected_lang)

        # 检查过滤后的结果是否为空，如果为空则返回空列表以触发重试机制
        if not filtered_response or not filtered_response.strip():
            logger.warning(f"用户 {user_id} AI响应经过过滤后为空，返回空列表以触发重试机制")
            # 不再保存空响应到对话历史和记忆
            return [], deduct_type

        # 处理AI响应
        response_sentences = process_ai_response(filtered_response)

        # 保存对话历史
        user_data["conversation"].extend([
            {"role": "user", "content": processed_message},
            {"role": "assistant", "content": filtered_response}
        ])

        # 限制对话历史长度
        if len(user_data["conversation"]) > 20:
            user_data["conversation"] = user_data["conversation"][-20:]

        await save_user_data(user_id, user_data)

        # 保存记忆
        try:
            await enhanced_memory.add_structured_memory(
                user_id=user_id,
                content=f"用户: {processed_message}\nAI: {filtered_response}",
                memory_type=MemoryType.CONVERSATION,
                tags=["对话", "chat", "unified"],
                emotional_valence=0.0
            )
        except Exception as e:
            logger.error(f"用户 {user_id} 保存记忆失败: {str(e)}")

        return response_sentences, deduct_type

    except Exception as e:
        logger.error(f"用户 {user_id} 统一AI对话处理失败: {str(e)}")

        # 对话失败，返还积分
        await refund_credits(user_id, deduct_info, logger)

        # 根据是否实际扣除了积分来确定消息内容
        if deduct_info.get("deducted", False):
            error_msg = "贱狗！主人的系统出故障了，你的狗粮已返还，快去重新舔主人的脚趾！"
        else:
            error_msg = "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！"
        return [error_msg], deduct_type


async def chat_with_ai_multimodal(user_id: str, message: str, image_data = None, deduct_type: str = "text", logger=None, bot=None, use_simplified_mode: bool = False) -> tuple[list[str], str | dict]:
    """
    多模态AI对话处理
    支持图片和文字混合输入
    image_data可以是：
    - str: 单个图片的base64编码
    - list: 多个图片的base64编码列表（用于视频多帧分析）

    Args:
        use_simplified_mode: 是否使用简化模式（不启用破限配置），用于正常的视频/图片分析
    """
    deduct_info = {"deducted": False, "amount": 0, "from": ""}
    
    try:
        from .api_manager import call_ai_api_with_fallback
        from .content_processor import preprocess_message, build_system_prompt, comprehensive_content_filter
        
        # 检查会员状态并扣除积分
        can_continue, error_msg, deduct_info = await check_membership_and_deduct_credits(user_id, logger, bot)
        if not can_continue:
            # 如果有支付菜单，将其加入到deduct_type中以便发送回复按钮
            if "reply_markup" in deduct_info:
                final_deduct = {"deduct_type": deduct_type, "reply_markup": deduct_info["reply_markup"]}
                return [error_msg], final_deduct
            return [error_msg], deduct_type
        
        # 预处理消息
        processed_message, detected_lang, warning_msg = await preprocess_message(user_id, message, logger)
        
        if warning_msg:
            # 预处理失败不返还积分（这是用户输入问题）
            return [warning_msg], deduct_type
        
        # 获取用户数据
        user_data = await get_user_data(user_id)
        
        # 检查禁言状态
        if user_data["ban_until"] and datetime.now().isoformat() < user_data["ban_until"]:
            logger.info(f"用户 {user_id} 被禁言，终止对话")
            # 禁言状态不返还积分（这是用户状态问题）
            return ["贱奴！你被爷禁言了，滚去等着！"], deduct_type
        
        # 获取相关记忆
        try:
            related_memories = await enhanced_memory.get_related_memories(user_id, processed_message, limit=5)
        except Exception as e:
            logger.error(f"用户 {user_id} 获取记忆失败: {str(e)}")
            related_memories = []
        
        # 构建系统提示词
        system_prompt = await build_system_prompt(
            user_id, processed_message, user_data, related_memories, 
            len(user_data.get("conversation", [])) == 0, detected_lang
        )
        
        # 构建对话历史
        conversation = [{"role": "system", "content": system_prompt}]
        
        # 添加历史对话
        if user_data.get("conversation"):
            conversation.extend(user_data["conversation"][-10:])  # 保留最近10轮对话
        
        # 构建多模态消息
        if image_data:
            # 检查是否有对话历史，如果有则让AI更自然地融入上下文
            has_conversation_history = user_data.get("conversation") and len(user_data["conversation"]) > 0

            # 判断是单图还是多图
            is_multi_image = isinstance(image_data, list)

            # 处理空消息或简单提示的情况
            if not processed_message.strip() or processed_message.strip() in ["看看这个", "Look at this", "看看这个视频", "Look at this video"]:
                if has_conversation_history:
                    # 有对话历史且是简单提示，使用自然的用户语气
                    if is_multi_image:
                        text_prompt = f"主人，我给您发了一段视频，请您看看怎么样"
                    else:
                        text_prompt = "主人，我给您发了一张图片，请您看看"

                    user_content = [{"type": "text", "text": text_prompt}]
                    if is_multi_image:
                        # 多图情况
                        for img_data in image_data:
                            user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_data}"}})
                    else:
                        # 单图情况
                        user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}})
                else:
                    # 没有对话历史，使用谦卑的用户语气
                    if is_multi_image:
                        simple_prompt = "主人，我给您发了一段视频，请您看看拍的怎么样，点评一下，骂我"
                    else:
                        simple_prompt = "主人，我给您发了一张图片，请您看看并指导我"

                    user_content = [{"type": "text", "text": simple_prompt}]
                    if is_multi_image:
                        # 多图情况
                        for img_data in image_data:
                            user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_data}"}})
                    else:
                        # 单图情况
                        user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}})
            else:
                # 用户有具体的问题或说明，正常处理
                # 检查是否需要添加自然的用户语气前缀
                if not any(keyword in processed_message.lower() for keyword in ["主人", "master", "我给您", "i sent", "请您", "please"]):
                    # 如果是多图且没有明显的角色扮演语气，添加自然前缀
                    if is_multi_image:
                        processed_message = f"主人，我给您发了一段视频，{processed_message}"
                    else:
                        processed_message = f"主人，我给您发了一张图片，{processed_message}"

                user_content = [{"type": "text", "text": processed_message}]
                if is_multi_image:
                    # 多图情况
                    for img_data in image_data:
                        user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_data}"}})
                else:
                    # 单图情况
                    user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{img_data}"}})
        else:
            user_content = processed_message
        
        conversation.append({"role": "user", "content": user_content})
        
        # 优先尝试多模态API，如果失败则回退到本地识图+文本API
        ai_response = None

        # 检查是否有图片数据且有支持多模态的API
        if image_data:
            from config import AI_API_CONFIGS
            has_multimodal_api = any(config.get("is_multimodal", False) for config in AI_API_CONFIGS)

            if has_multimodal_api:
                try:
                    # 尝试使用多模态API，传递简化模式参数
                    from .api_manager import call_ai_api_multimodal_priority
                    ai_response = await call_ai_api_multimodal_priority(user_id, conversation, use_simplified_mode=use_simplified_mode)
                    logger.info(f"用户 {user_id} 多模态API调用成功{'(简化模式)' if use_simplified_mode else ''}")
                except Exception as e:
                    logger.warning(f"用户 {user_id} 多模态API调用失败，回退到本地识图: {str(e)}")
                    ai_response = None

        # 如果多模态API失败或没有图片数据，使用原有的API调用方式
        if ai_response is None:
            from config import AI_API_CONFIGS
            max_attempts = len(AI_API_CONFIGS) if AI_API_CONFIGS else 3

            # 如果有图片数据但多模态API失败，需要先进行本地识图
            if image_data:
                # 使用本地识图模型处理图片
                from .api_manager import call_vllm_minicpm_api

                # 判断是单图还是多图
                is_multi_image = isinstance(image_data, list)
                image_descriptions = []

                if is_multi_image:
                    # 多图处理：逐个分析每张图片
                    for i, img_data in enumerate(image_data):
                        image_analysis_messages = [
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": f"请详细描述第{i+1}张图片的内容，包括主要物体、场景、人物、动作、表情、背景等所有可见的细节。"
                                    },
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/jpeg;base64,{img_data}"
                                        }
                                    }
                                ]
                            }
                        ]

                        try:
                            description = await call_vllm_minicpm_api(user_id, image_analysis_messages)
                            image_descriptions.append(f"第{i+1}张图片: {description}")
                            logger.info(f"用户 {user_id} 本地识图第{i+1}张成功")
                        except Exception as e:
                            logger.warning(f"用户 {user_id} 本地识图第{i+1}张失败: {str(e)}")
                            image_descriptions.append(f"第{i+1}张图片: 识别失败")
                else:
                    # 单图处理
                    image_analysis_messages = [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "请详细描述这张图片的内容，包括主要物体、场景、人物、动作、表情、背景等所有可见的细节。"
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{image_data}"
                                    }
                                }
                            ]
                        }
                    ]

                    try:
                        description = await call_vllm_minicpm_api(user_id, image_analysis_messages)
                        image_descriptions.append(description)
                        logger.info(f"用户 {user_id} 本地识图成功")
                    except Exception as e:
                        logger.warning(f"用户 {user_id} 本地识图失败: {str(e)}")
                        image_descriptions.append("图片识别失败")

                if image_descriptions:
                    # 将图片描述添加到用户消息中
                    combined_description = "\n".join(image_descriptions)
                    enhanced_message = f"{processed_message}\n\n[图片内容描述]: {combined_description}"

                    # 重新构建对话，使用文本格式
                    conversation = [{"role": "system", "content": system_prompt}]
                    if user_data.get("conversation"):
                        conversation.extend(user_data["conversation"][-10:])
                    conversation.append({"role": "user", "content": enhanced_message})
                else:
                    # 所有图片识别都失败，使用原始消息
                    logger.warning(f"用户 {user_id} 所有图片本地识图都失败")
                    conversation = [{"role": "system", "content": system_prompt}]
                    if user_data.get("conversation"):
                        conversation.extend(user_data["conversation"][-10:])
                    conversation.append({"role": "user", "content": processed_message})

            # 调用常规API
            from .api_manager import call_ai_api_with_fallback
            ai_response = await call_ai_api_with_fallback(user_id, conversation, max_attempts=max_attempts)

        # 检查响应是否为"暂无返回"，如果是则返回空列表以触发重试机制
        if ai_response.strip() == "暂无返回":
            logger.warning(f"用户 {user_id} 多模态API响应为'暂无返回'，返回空列表以触发重试机制")
            return [], deduct_type

        # 应用综合内容过滤
        filtered_response = comprehensive_content_filter(ai_response, detected_lang)

        # 检查过滤后的结果是否为空，如果为空则返回空列表以触发重试机制
        if not filtered_response or not filtered_response.strip():
            logger.warning(f"用户 {user_id} 多模态AI响应经过过滤后为空，返回空列表以触发重试机制")
            # 不再保存空响应到对话历史和记忆
            return [], deduct_type
        
        # 处理AI响应
        response_sentences = process_ai_response(filtered_response)
        
        # 保存对话历史
        if image_data:
            # 多模态对话：保存更详细的图片信息到历史记录
            # 从AI响应中提取图片描述信息用于后续上下文
            image_description_summary = ""
            if filtered_response and len(filtered_response) > 50:
                # 提取AI响应的前200个字符作为图片内容摘要
                image_description_summary = filtered_response[:200] + "..." if len(filtered_response) > 200 else filtered_response

            user_content = f"{processed_message} [包含图片 - AI分析: {image_description_summary}]" if image_description_summary else f"{processed_message} [包含图片]"

            user_data["conversation"].extend([
                {"role": "user", "content": user_content},
                {"role": "assistant", "content": filtered_response}
            ])
        else:
            # 普通文本对话
            user_data["conversation"].extend([
                {"role": "user", "content": processed_message},
                {"role": "assistant", "content": filtered_response}
            ])
        
        # 限制对话历史长度
        if len(user_data["conversation"]) > 20:
            user_data["conversation"] = user_data["conversation"][-20:]
        
        await save_user_data(user_id, user_data)
        
        # 保存记忆
        try:
            memory_content = f"用户: {processed_message}"
            if image_data:
                memory_content += " [包含图片]"
            memory_content += f"\nAI: {filtered_response}"
            
            await enhanced_memory.add_structured_memory(
                user_id=user_id,
                content=memory_content,
                memory_type=MemoryType.CONVERSATION,
                tags=["对话", "chat", "multimodal"] if image_data else ["对话", "chat"],
                emotional_valence=0.0
            )
        except Exception as e:
            logger.error(f"用户 {user_id} 保存记忆失败: {str(e)}")
        
        return response_sentences, deduct_type
        
    except Exception as e:
        logger.error(f"用户 {user_id} 多模态AI对话处理失败: {str(e)}")
        
        # 对话失败，返还积分
        await refund_credits(user_id, deduct_info, logger)
        
        # 根据是否实际扣除了积分来确定消息内容
        if deduct_info.get("deducted", False):
            error_msg = "贱狗！主人的系统出故障了，你的狗粮已返还，快去重新舔主人的脚趾！"
        else:
            error_msg = "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！"
        return [error_msg], deduct_type


async def ask_ai_with_context(user_id: str, user_message: str, context_data: dict, chat_id: str, username: str) -> dict:
    """
    带上下文的AI对话
    """
    deduct_info = {"deducted": False, "amount": 0, "from": ""}
    
    try:
        from .api_manager import call_ai_api_with_fallback
        from .content_processor import preprocess_message, build_system_prompt, comprehensive_content_filter
        
        # 检查会员状态并扣除积分
        can_continue, error_msg, deduct_info = await check_membership_and_deduct_credits(user_id, logger)
        if not can_continue:
            return {"response": error_msg, "success": False}
        
        # 预处理消息
        processed_message, detected_lang, warning_msg = await preprocess_message(user_id, user_message, logger)
        
        if warning_msg:
            # 预处理失败不返还积分（这是用户输入问题）
            return {"response": warning_msg, "success": False}
        
        # 获取用户数据
        user_data = await get_user_data(user_id)
        
        # 构建带上下文的系统提示词
        base_prompt = await build_system_prompt(
            user_id, processed_message, user_data, [], 
            len(user_data.get("conversation", [])) == 0, detected_lang
        )
        
        # 添加上下文信息
        context_prompt = f"{base_prompt}\n\n上下文信息：\n{context_data}"
        
        # 构建对话
        conversation = [
            {"role": "system", "content": context_prompt},
            {"role": "user", "content": processed_message}
        ]
        
        # 调用AI API (使用带自动故障转移的版本)
        # 尝试所有可用的API，不限制次数
        from config import AI_API_CONFIGS
        max_attempts = len(AI_API_CONFIGS) if AI_API_CONFIGS else 3
        ai_response = await call_ai_api_with_fallback(user_id, conversation, max_attempts=max_attempts)

        # 检查响应是否为"暂无返回"，如果是则生成兜底回复
        if ai_response.strip() == "暂无返回":
            logger.warning(f"用户 {user_id} 上下文API响应为'暂无返回'，提供兜底回复")
            filtered_response = "对不起，我无法基于当前上下文生成有效回复。请尝试重新描述您的问题。"
            return {"response": filtered_response, "success": True}

        # 应用综合内容过滤
        filtered_response = comprehensive_content_filter(ai_response, detected_lang)

        # 检查过滤后的结果是否为空，如果为空则生成兜底回复
        if not filtered_response or not filtered_response.strip():
            logger.warning(f"用户 {user_id} 上下文AI响应经过过滤后为空，提供兜底回复")
            filtered_response = "对不起，我无法基于当前上下文生成有效回复。请尝试重新描述您的问题。"
        
        return {"response": filtered_response, "success": True}
        
    except Exception as e:
        logger.error(f"用户 {user_id} 带上下文AI对话失败: {str(e)}")
        
        # 对话失败，返还积分
        await refund_credits(user_id, deduct_info, logger)
        
        # 根据是否实际扣除了积分来确定消息内容
        if deduct_info.get("deducted", False):
            return {"response": "贱狗！主人的系统出故障了，你的狗粮已返还，快去重新舔主人的脚趾！", "success": False}
        else:
            return {"response": "贱狗！主人的系统出故障了，快去重新舔主人的脚趾！", "success": False} 