"""
多媒体处理模块 - 负责图片、视频等多媒体内容的处理
"""

import os
import base64
import asyncio
from PIL import Image
from telegram import <PERSON><PERSON>
from logging_config import logger
from config import DOWNLOAD_DIR


async def download_and_process_image(bot: Bot, file_id: str, user_id: str, logger) -> str:
    """
    下载并处理图片，返回base64编码的图片数据
    """
    try:
        # 获取文件信息
        file = await bot.get_file(file_id)
        file_path = file.file_path
        
        # 下载文件
        download_path = os.path.join(DOWNLOAD_DIR, f"{user_id}_{file_id}.jpg")
        await file.download_to_drive(download_path)
        
        logger.info(f"用户 {user_id} 图片下载成功: {download_path}")
        
        def encode_image(image_path):
            """编码图片为base64格式"""
            try:
                with Image.open(image_path) as img:
                    # 转换为RGB模式
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # 压缩图片
                    max_size = (1024, 1024)
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    
                    # 保存压缩后的图片
                    compressed_path = image_path.replace('.jpg', '_compressed.jpg')
                    img.save(compressed_path, 'JPEG', quality=85, optimize=True)
                    
                    # 读取并编码
                    with open(compressed_path, "rb") as image_file:
                        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                    
                    # 清理临时文件
                    try:
                        os.remove(image_path)
                        os.remove(compressed_path)
                    except:
                        pass
                    
                    return encoded_string
                    
            except Exception as e:
                logger.error(f"图片编码失败: {str(e)}")
                try:
                    os.remove(image_path)
                except:
                    pass
                return None
        
        # 在线程池中处理图片
        loop = asyncio.get_event_loop()
        encoded_image = await loop.run_in_executor(None, encode_image, download_path)
        
        if encoded_image:
            logger.info(f"用户 {user_id} 图片处理成功")
            return encoded_image
        else:
            logger.error(f"用户 {user_id} 图片处理失败")
            return None
            
    except Exception as e:
        logger.error(f"用户 {user_id} 图片下载处理失败: {str(e)}")
        return None


async def download_and_process_video(bot: Bot, file_id: str, user_id: str, logger) -> str:
    """
    下载并处理视频，提取关键帧进行分析
    """
    try:
        # 获取文件信息
        file = await bot.get_file(file_id)
        file_path = file.file_path
        
        # 下载文件
        download_path = os.path.join(DOWNLOAD_DIR, f"{user_id}_{file_id}.mp4")
        await file.download_to_drive(download_path)
        
        logger.info(f"用户 {user_id} 视频下载成功: {download_path}")
        
        # 提取关键帧
        frames = await extract_video_frames(download_path, user_id, logger)
        
        # 清理视频文件
        try:
            os.remove(download_path)
        except:
            pass
        
        if frames:
            # 分析关键帧
            analysis_results = []
            for i, frame in enumerate(frames[:3]):  # 最多分析3帧
                try:
                    result = await process_single_frame(frame, user_id, i, logger)
                    if result:
                        analysis_results.append(f"帧 {i+1}: {result}")
                except Exception as e:
                    logger.error(f"用户 {user_id} 帧 {i+1} 处理失败: {str(e)}")
            
            if analysis_results:
                combined_result = "\n".join(analysis_results)
                logger.info(f"用户 {user_id} 视频分析完成")
                return combined_result
            else:
                logger.warning(f"用户 {user_id} 视频分析无结果")
                return "视频分析失败，无法提取有效信息"
        else:
            logger.error(f"用户 {user_id} 视频帧提取失败")
            return "视频处理失败，无法提取关键帧"
            
    except Exception as e:
        logger.error(f"用户 {user_id} 视频下载处理失败: {str(e)}")
        return f"视频处理失败: {str(e)}"


async def extract_video_frames(video_path: str, user_id: str, logger) -> list:
    """
    提取视频关键帧
    """
    try:
        import cv2
        
        def extract_frames():
            """在线程池中提取帧"""
            frames = []
            cap = cv2.VideoCapture(video_path)
            
            if not cap.isOpened():
                logger.error(f"用户 {user_id} 无法打开视频文件")
                return frames
            
            # 获取视频信息
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 选择关键帧位置（开头、中间、结尾）
            key_positions = [0, frame_count // 2, frame_count - 1]
            
            for pos in key_positions:
                cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                ret, frame = cap.read()
                
                if ret:
                    # 转换为PIL Image
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(frame_rgb)
                    frames.append(pil_image)
                    logger.debug(f"用户 {user_id} 提取帧 {pos} 成功")
                else:
                    logger.warning(f"用户 {user_id} 提取帧 {pos} 失败")
            
            cap.release()
            return frames
        
        # 在线程池中执行
        loop = asyncio.get_event_loop()
        frames = await loop.run_in_executor(None, extract_frames)
        
        logger.info(f"用户 {user_id} 视频帧提取完成，共 {len(frames)} 帧")
        return frames
        
    except ImportError:
        logger.error(f"用户 {user_id} 缺少opencv-python依赖，无法处理视频")
        return []
    except Exception as e:
        logger.error(f"用户 {user_id} 视频帧提取失败: {str(e)}")
        return []


async def extract_video_key_frame_for_multimodal(bot: Bot, file_id: str, user_id: str, logger) -> list:
    """
    下载视频并提取多个关键帧，转换为base64格式用于多模态API一次性分析
    返回多个帧的base64编码列表，用于一次性多图分析
    """
    try:
        # 获取文件信息
        file = await bot.get_file(file_id)
        file_path = file.file_path

        # 下载文件
        download_path = os.path.join(DOWNLOAD_DIR, f"{user_id}_{file_id}.mp4")
        await file.download_to_drive(download_path)

        logger.info(f"用户 {user_id} 视频下载成功: {download_path}")

        # 提取多个关键帧用于一次性分析
        try:
            import cv2

            # 打开视频文件
            cap = cv2.VideoCapture(download_path)

            if not cap.isOpened():
                logger.error(f"用户 {user_id} 无法打开视频文件: {download_path}")
                return None

            # 获取视频总帧数
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            if total_frames <= 0:
                logger.error(f"用户 {user_id} 视频帧数为0")
                cap.release()
                return None

            # 计算5个关键帧的位置：均匀分布
            if total_frames <= 5:
                # 如果帧数不足5帧，使用所有帧
                frame_positions = list(range(total_frames))
            else:
                # 均匀分布5个关键帧：10%, 30%, 50%, 70%, 90%
                percentages = [0.1, 0.3, 0.5, 0.7, 0.9]
                frame_positions = [int(total_frames * p) for p in percentages]
                frame_positions = [min(pos, total_frames - 1) for pos in frame_positions]

            logger.info(f"用户 {user_id} 视频关键帧提取计划: 总帧数={total_frames}, 关键帧位置={frame_positions}")

            # 提取所有关键帧
            encoded_frames = []
            for i, pos in enumerate(frame_positions):
                cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                ret, frame = cap.read()

                if ret:
                    # 转换为PIL Image
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(frame_rgb)

                    # 压缩图片
                    max_size = (512, 512)
                    pil_image.thumbnail(max_size, Image.Resampling.LANCZOS)

                    # 转换为base64
                    import io
                    buffer = io.BytesIO()
                    pil_image.save(buffer, format='JPEG', quality=80)
                    encoded_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    encoded_frames.append(encoded_image)

                    logger.debug(f"用户 {user_id} 成功提取第{i+1}帧: 位置{pos}")
                else:
                    logger.warning(f"用户 {user_id} 无法提取第{i+1}帧(位置{pos})")

            cap.release()

            if encoded_frames:
                logger.info(f"用户 {user_id} 视频多帧提取成功，共{len(encoded_frames)}帧")
                return encoded_frames
            else:
                logger.error(f"用户 {user_id} 无法提取任何视频帧")
                return None

        except Exception as e:
            logger.error(f"用户 {user_id} 视频关键帧提取失败: {str(e)}")
            return None

        finally:
            # 清理视频文件
            try:
                os.remove(download_path)
            except:
                pass

    except Exception as e:
        logger.error(f"用户 {user_id} 视频下载处理失败: {str(e)}")
        return None


async def process_single_frame(pil_image: Image, user_id: str, timestamp: float, logger) -> str:
    """
    处理单个视频帧，返回分析结果
    """
    try:
        # 压缩图片
        max_size = (512, 512)
        pil_image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        # 转换为base64
        import io
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=80)
        encoded_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        # 调用AI分析
        from .api_manager import call_vllm_minicpm_api
        
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请描述这张图片的内容，包括主要物体、场景、动作等。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{encoded_image}"
                        }
                    }
                ]
            }
        ]
        
        result = await call_vllm_minicpm_api(user_id, messages, max_tokens=200)
        
        if result and result != "图像分析功能暂时不可用":
            logger.info(f"用户 {user_id} 视频帧分析成功")
            return result
        else:
            logger.warning(f"用户 {user_id} 视频帧分析失败")
            return "无法分析此帧内容"
            
    except Exception as e:
        logger.error(f"用户 {user_id} 视频帧处理失败: {str(e)}")
        return f"帧处理失败: {str(e)}" 