"""
用户管理模块 - 负责用户数据管理、个性化分析、奖励机制等
"""

import asyncio
import time
from datetime import datetime, timedelta, date
from database import get_user_data, save_user_data, get_cached_personality, cache_personality
from logging_config import logger
from config import ALWAYS_USE_VOICE_RESPONSE, DEFAULT_VOICE, MASTER_ID


async def analyze_user_personality(user_id: str, user_data: dict) -> str:
    """分析用户个性"""
    try:
        cached_personality = await get_cached_personality(user_id)
        if cached_personality:
            return cached_personality
        
        conversation_history = user_data.get("conversation", [])
        
        if len(conversation_history) < 6:
            return "需要更多对话来分析您的个性特点"
        
        from .api_manager import call_ai_api
        
        recent_conversations = conversation_history[-10:]
        conversation_text = ""
        
        for conv in recent_conversations:
            if conv.get("role") == "user":
                conversation_text += f"用户: {conv.get('content', '')}\n"
            elif conv.get("role") == "assistant":
                conversation_text += f"AI: {conv.get('content', '')}\n"
        
        analysis_prompt = f"""
        请基于以下对话历史，分析这位用户的个性特点、兴趣爱好、沟通风格等。
        请用温和、客观的语言描述，避免负面评价。

        对话历史：
        {conversation_text}

        请分析：
        1. 个性特点
        2. 兴趣爱好  
        3. 沟通风格
        4. 其他特征
        """
        
        analysis_conversation = [
            {"role": "system", "content": "你是一个专业的心理分析师，擅长通过对话分析人格特征。"},
            {"role": "user", "content": analysis_prompt}
        ]
        
        personality_analysis = await call_ai_api(user_id, analysis_conversation, max_total_tokens=3000)
        await cache_personality(user_id, personality_analysis)
        
        logger.info(f"用户 {user_id} 个性分析完成")
        return personality_analysis
        
    except Exception as e:
        logger.error(f"用户 {user_id} 个性分析失败: {str(e)}")
        return "个性分析暂时不可用，请稍后再试。"


async def handle_share_rewards(user_id: str, user_data: dict, bot, sharer_id: str | None):
    """处理分享奖励机制"""
    try:
        if not sharer_id or sharer_id == user_id:
            return
        
        if user_data.get("share_processed", False):
            return
        
        sharer_data = await get_user_data(sharer_id)
        
        current_credits = sharer_data.get("credits", 0)
        share_reward = 10
        
        sharer_data["credits"] = current_credits + share_reward
        sharer_data["total_referrals"] = sharer_data.get("total_referrals", 0) + 1
        
        await save_user_data(sharer_id, sharer_data)
        
        user_data["share_processed"] = True
        user_data["referred_by"] = sharer_id
        await save_user_data(user_id, user_data)
        
        try:
            await bot.send_message(
                chat_id=sharer_id,
                text=f"🎉 恭喜！您成功邀请了一位新用户！\n"
                     f"奖励积分：+{share_reward}\n"
                     f"当前积分：{sharer_data['credits']}\n"
                     f"累计邀请：{sharer_data['total_referrals']} 人"
            )
        except Exception as e:
            logger.error(f"通知分享者 {sharer_id} 失败: {str(e)}")
        
        logger.info(f"处理分享奖励：分享者 {sharer_id}，新用户 {user_id}")
        
    except Exception as e:
        logger.error(f"处理分享奖励失败: {str(e)}")


async def determine_voice_usage(user_id, user_data, bot, chat_id):
    """确定是否使用语音回复 - 基于用户的selected_voice设置"""
    try:
        from config import ALWAYS_USE_VOICE_RESPONSE
        from database import save_user_data
        from datetime import datetime
        
        # 如果全局设置为总是使用语音，则返回True
        if ALWAYS_USE_VOICE_RESPONSE:
            return True, "voice"
        
        # 获取用户的语音选择
        selected_voice = user_data.get("selected_voice", "text")
        
        # 检查是否为会员（包括永久会员）
        is_member = False

        # 首先检查是否为永久会员
        if user_data.get("permanent_member", False):
            is_member = True
            logger.info(f"用户 {user_id} 是永久会员")
        else:
            # 检查会员到期时间
            membership_expiry = user_data.get("membership_expiry")
            if membership_expiry:
                try:
                    expiry_date = datetime.fromisoformat(membership_expiry)
                    if datetime.now() < expiry_date:
                        is_member = True
                        logger.info(f"用户 {user_id} 是会员，会员到期时间: {membership_expiry}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {membership_expiry}，错误: {str(e)}")
        
        # 会员可以使用任何语音模型，不受狗粮限制
        if is_member:
            use_voice = selected_voice != "text"
            deduct_type = "voice" if use_voice else "text"
            logger.debug(f"用户 {user_id} 是会员，语音设置: selected_voice={selected_voice}, use_voice={use_voice}, deduct_type={deduct_type}")
            return use_voice, deduct_type
        
        # 非会员需要检查狗粮
        total_dog_food = user_data.get("daily_free_dog_food", 0) + user_data.get("dog_food", 0)
        use_voice = selected_voice != "text" and total_dog_food > 0
        deduct_type = "voice" if use_voice else "text"
        logger.debug(f"用户 {user_id} 语音设置: selected_voice={selected_voice}, use_voice={use_voice}, deduct_type={deduct_type}, 总狗粮={total_dog_food}")
        
        # 如果选择了语音模型但没有狗粮，自动切换为文字模式
        if selected_voice != "text" and total_dog_food <= 0:
            user_data["selected_voice"] = "text"
            await save_user_data(user_id, user_data)
            deduct_type = "text"
            use_voice = False
            
            # 记录日志但不发送消息，避免重复提示
            logger.info(f"用户 {user_id} 狗粮不足，自动切换为文字模式")
        
        return use_voice, deduct_type
        
    except Exception as e:
        logger.error(f"用户 {user_id} 判断语音使用失败: {str(e)}")
        return False, "text"


async def reset_daily_voice(user_id, user_data):
    """重置每日语音配额"""
    try:
        today = date.today().isoformat()
        last_reset = user_data.get("last_voice_reset", "")
        
        if last_reset != today:
            user_data["daily_voice_used"] = 0
            user_data["last_voice_reset"] = today
            await save_user_data(user_id, user_data)
            logger.info(f"用户 {user_id} 每日语音配额已重置")
        
    except Exception as e:
        logger.error(f"用户 {user_id} 重置每日语音配额失败: {str(e)}")


async def daily_reset(context):
    """每日重置任务"""
    try:
        logger.info("开始执行每日重置任务")
        
        import aiosqlite
        from config import DB_PATH
        
        async with aiosqlite.connect(DB_PATH) as db:
            async with db.execute("SELECT user_id FROM users") as cursor:
                users = await cursor.fetchall()
        
        reset_count = 0
        
        for user_row in users:
            try:
                user_id = user_row[0]
                user_data = await get_user_data(user_id)
                
                await reset_daily_voice(user_id, user_data)
                
                user_data["daily_requests"] = 0
                user_data["daily_tokens"] = 0
                
                await save_user_data(user_id, user_data)
                reset_count += 1
                
            except Exception as e:
                logger.error(f"重置用户 {user_id} 数据失败: {str(e)}")
        
        logger.info(f"每日重置任务完成，处理用户数: {reset_count}")
        
    except Exception as e:
        logger.error(f"每日重置任务失败: {str(e)}")


async def check_and_notify_usage(bot, chat_id, user_id, deduct_type, username):
    """检查并通知使用情况"""
    try:
        user_data = await get_user_data(user_id)
        
        daily_requests = user_data.get("daily_requests", 0)
        daily_tokens = user_data.get("daily_tokens", 0)
        daily_voice = user_data.get("daily_voice_used", 0)
        
        request_limit = user_data.get("daily_request_limit", 100)
        token_limit = user_data.get("daily_token_limit", 50000)
        voice_limit = user_data.get("daily_voice_limit", 20)
        
        warnings = []
        
        if daily_requests >= request_limit * 0.8:
            warnings.append(f"📊 今日请求数: {daily_requests}/{request_limit}")
        
        if daily_tokens >= token_limit * 0.8:
            warnings.append(f"🔤 今日Token数: {daily_tokens}/{token_limit}")
        
        if deduct_type == "voice" and daily_voice >= voice_limit * 0.8:
            warnings.append(f"🎤 今日语音数: {daily_voice}/{voice_limit}")
        
        if warnings:
            warning_text = "⚠️ 使用量提醒\n\n" + "\n".join(warnings)
            await bot.send_message(chat_id=chat_id, text=warning_text)
        
    except Exception as e:
        logger.error(f"用户 {user_id} 检查使用情况失败: {str(e)}") 