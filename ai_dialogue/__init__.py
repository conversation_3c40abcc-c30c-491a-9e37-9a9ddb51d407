"""
AI对话模块 - 模块化的Telegram Bot AI对话系统

此模块包含以下子模块：
- api_manager: API管理和负载均衡
- content_processor: 内容处理和过滤
- media_handler: 多媒体处理
- chat_processor: 核心对话处理逻辑
- streaming_handler: 流式响应处理
- admin_features: 管理员功能
- user_manager: 用户管理和个性化
- response_sender: 响应发送和格式化
- worker_manager: 工作线程管理
"""

from .api_manager import (
    api_keepalive, start_api_keepalive_task, get_api_status, 
    get_api_keepalive_status, call_ai_api, call_ai_api_streaming,
    call_ai_api_unified, call_vllm_minicpm_api
)

from .content_processor import (
    preprocess_message, filter_sensitive_words, filter_thinking_chain,
    validate_and_clean_messages, format_settings, build_system_prompt,
    adjust_tone_based_on_preferences
)

from .media_handler import (
    download_and_process_image, download_and_process_video,
    process_single_frame
)

from .chat_processor import (
    chat_with_ai, chat_with_ai_multimodal, chat_with_ai_streaming,
    chat_with_ai_unified, ask_ai_with_context
)

from .streaming_handler import (
    send_response_streaming, send_single_sentence
)

from .admin_features import (
    start_broadcast, finish_broadcast, confirm_broadcast,
    cancel_broadcast, handle_broadcast_message,
    private_chat_with_user, handle_private_message,
    broadcast_data
)

from .user_manager import (
    analyze_user_personality, handle_share_rewards,
    determine_voice_usage, reset_daily_voice, daily_reset,
    check_and_notify_usage
)

from .response_sender import (
    send_response, send_with_retry, keep_typing_alive
)

from .worker_manager import (
    TaskManager, ai_worker, ai_worker_streaming, ai_worker_unified,
    task_manager
)

from .utils import (
    answer_with_retry, extract_message_info, filter_message
)

__all__ = [
    # API管理
    'api_keepalive', 'start_api_keepalive_task', 'get_api_status', 
    'get_api_keepalive_status', 'call_ai_api', 'call_ai_api_streaming',
    'call_ai_api_unified', 'call_vllm_minicpm_api',
    
    # 内容处理
    'preprocess_message', 'filter_sensitive_words', 'filter_thinking_chain',
    'validate_and_clean_messages', 'format_settings', 'build_system_prompt',
    'adjust_tone_based_on_preferences',
    
    # 多媒体处理
    'download_and_process_image', 'download_and_process_video',
    'process_single_frame',
    
    # 对话处理
    'chat_with_ai', 'chat_with_ai_multimodal', 'chat_with_ai_streaming',
    'chat_with_ai_unified', 'ask_ai_with_context',
    
    # 流式处理
    'send_response_streaming', 'send_single_sentence',
    
    # 管理员功能
    'start_broadcast', 'finish_broadcast', 'confirm_broadcast',
    'cancel_broadcast', 'handle_broadcast_message',
    'private_chat_with_user', 'handle_private_message',
    'broadcast_data',
    
    # 用户管理
    'analyze_user_personality', 'handle_share_rewards',
    'determine_voice_usage', 'reset_daily_voice', 'daily_reset',
    'check_and_notify_usage',
    
    # 响应发送
    'send_response', 'send_with_retry', 'keep_typing_alive',
    
    # 工作线程管理
    'TaskManager', 'ai_worker', 'ai_worker_streaming', 'ai_worker_unified',
    'task_manager',
    
    # 工具函数
    'answer_with_retry', 'extract_message_info', 'filter_message'
] 