"""
增强反重复检测模块
提供多维度的重复检测和内容多样性分析
"""

import re
import asyncio
from typing import List, Dict, Tuple, Optional
from collections import Counter
from datetime import datetime

from config import ANTI_REPETITION_CONFIG
from logging_config import logger


class EnhancedAntiRepetition:
    """增强反重复检测器"""
    
    def __init__(self):
        self.config = ANTI_REPETITION_CONFIG
        
    async def analyze_response_similarity(
        self, 
        current_response: str, 
        recent_responses: List[str], 
        user_id: str
    ) -> Dict:
        """
        分析回复相似度的多个维度
        """
        try:
            analysis = {
                'overall_similarity': 0.0,
                'lexical_similarity': 0.0,
                'structural_similarity': 0.0,
                'semantic_similarity': 0.0,
                'is_repetitive': False,
                'repetition_type': None,
                'suggestions': []
            }
            
            if not recent_responses:
                return analysis
            
            # 1. 词汇相似度检测
            lexical_scores = []
            for recent in recent_responses:
                score = self._calculate_lexical_similarity(current_response, recent)
                lexical_scores.append(score)
            
            analysis['lexical_similarity'] = max(lexical_scores) if lexical_scores else 0.0
            
            # 2. 结构相似度检测
            if self.config.get('structure_check', True):
                structural_scores = []
                for recent in recent_responses:
                    score = self._calculate_structural_similarity(current_response, recent)
                    structural_scores.append(score)
                
                analysis['structural_similarity'] = max(structural_scores) if structural_scores else 0.0
            
            # 3. 语义相似度检测（简化版）
            if self.config.get('semantic_check', True):
                semantic_scores = []
                for recent in recent_responses:
                    score = self._calculate_semantic_similarity(current_response, recent)
                    semantic_scores.append(score)
                
                analysis['semantic_similarity'] = max(semantic_scores) if semantic_scores else 0.0
            
            # 4. 综合评分
            weights = {
                'lexical': 0.4,
                'structural': 0.3,
                'semantic': 0.3
            }
            
            analysis['overall_similarity'] = (
                analysis['lexical_similarity'] * weights['lexical'] +
                analysis['structural_similarity'] * weights['structural'] +
                analysis['semantic_similarity'] * weights['semantic']
            )
            
            # 5. 判断是否重复
            threshold = self.config.get('similarity_threshold', 0.6)
            analysis['is_repetitive'] = analysis['overall_similarity'] > threshold
            
            # 6. 确定重复类型
            if analysis['is_repetitive']:
                if analysis['lexical_similarity'] > 0.7:
                    analysis['repetition_type'] = 'lexical'
                elif analysis['structural_similarity'] > 0.7:
                    analysis['repetition_type'] = 'structural'
                elif analysis['semantic_similarity'] > 0.7:
                    analysis['repetition_type'] = 'semantic'
                else:
                    analysis['repetition_type'] = 'mixed'
            
            # 7. 剧情推进检测
            if self.config.get('plot_progression_check', True):
                analysis['plot_progression_score'] = self._calculate_plot_progression(
                    current_response, recent_responses
                )

                # 如果剧情推进不足，增加重复性评分
                if analysis['plot_progression_score'] < 0.3:
                    analysis['overall_similarity'] += 0.2
                    analysis['is_repetitive'] = analysis['overall_similarity'] > threshold
                    if not analysis['repetition_type']:
                        analysis['repetition_type'] = 'plot_stagnation'

            # 8. 生成改进建议
            analysis['suggestions'] = self._generate_improvement_suggestions(analysis)
            
            logger.debug(f"用户 {user_id} 相似度分析 - 总体: {analysis['overall_similarity']:.2f}, "
                        f"词汇: {analysis['lexical_similarity']:.2f}, "
                        f"结构: {analysis['structural_similarity']:.2f}, "
                        f"语义: {analysis['semantic_similarity']:.2f}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"用户 {user_id} 相似度分析失败: {str(e)}")
            return {
                'overall_similarity': 0.0,
                'lexical_similarity': 0.0,
                'structural_similarity': 0.0,
                'semantic_similarity': 0.0,
                'plot_progression_score': 0.0,
                'is_repetitive': False,
                'repetition_type': None,
                'suggestions': []
            }
    
    def _calculate_lexical_similarity(self, text1: str, text2: str) -> float:
        """计算词汇相似度"""
        try:
            # 分词并转换为小写
            words1 = set(re.findall(r'\w+', text1.lower()))
            words2 = set(re.findall(r'\w+', text2.lower()))
            
            if not words1 or not words2:
                return 0.0
            
            # 计算Jaccard相似度
            intersection = words1 & words2
            union = words1 | words2
            
            return len(intersection) / len(union) if union else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_structural_similarity(self, text1: str, text2: str) -> float:
        """计算结构相似度"""
        try:
            # 提取结构特征
            features1 = self._extract_structural_features(text1)
            features2 = self._extract_structural_features(text2)
            
            # 计算特征相似度
            similarity_scores = []
            
            for feature in features1:
                if feature in features2:
                    similarity_scores.append(1.0)
                else:
                    similarity_scores.append(0.0)
            
            return sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0.0
            
        except Exception:
            return 0.0
    
    def _extract_structural_features(self, text: str) -> List[str]:
        """提取文本结构特征"""
        features = []
        
        # 句子开头模式
        sentences = re.split(r'[.!?。！？]', text)
        for sentence in sentences[:3]:  # 只检查前3个句子
            sentence = sentence.strip()
            if sentence:
                # 提取开头词汇
                words = sentence.split()[:3]  # 前3个词
                if words:
                    features.append(f"start_{' '.join(words)}")
        
        # 标点符号模式
        punctuation_pattern = re.sub(r'[^.!?。！？,，;；:]', '', text)
        if punctuation_pattern:
            features.append(f"punct_{punctuation_pattern[:10]}")
        
        # 长度模式
        length_category = "short" if len(text) < 50 else "medium" if len(text) < 150 else "long"
        features.append(f"length_{length_category}")
        
        return features
    
    def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度（简化版）"""
        try:
            # 提取关键词
            keywords1 = self._extract_keywords(text1)
            keywords2 = self._extract_keywords(text2)
            
            if not keywords1 or not keywords2:
                return 0.0
            
            # 计算关键词重叠度
            common_keywords = set(keywords1) & set(keywords2)
            total_keywords = set(keywords1) | set(keywords2)
            
            return len(common_keywords) / len(total_keywords) if total_keywords else 0.0
            
        except Exception:
            return 0.0
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取（可以后续改进为更复杂的算法）
        words = re.findall(r'\w+', text.lower())
        
        # 过滤停用词
        stop_words = {'的', '了', '在', '是', '我', '你', '他', '她', '它', '们', '这', '那', '有', '和', '与',
                     'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        
        keywords = [word for word in words if word not in stop_words and len(word) > 1]
        
        # 返回出现频率最高的词汇
        word_counts = Counter(keywords)
        return [word for word, count in word_counts.most_common(10)]
    
    def _generate_improvement_suggestions(self, analysis: Dict) -> List[str]:
        """根据分析结果生成改进建议"""
        suggestions = []
        
        if analysis['lexical_similarity'] > 0.7:
            suggestions.append("使用更多样化的词汇表达")
            suggestions.append("尝试同义词替换")
        
        if analysis['structural_similarity'] > 0.7:
            suggestions.append("改变句子结构和开头方式")
            suggestions.append("调整段落组织方式")
        
        if analysis['semantic_similarity'] > 0.7:
            suggestions.append("引入新的话题或角度")
            suggestions.append("深化或转换讨论重点")
        
        if analysis['overall_similarity'] > 0.8:
            suggestions.append("完全重新构思回复内容")
            suggestions.append("从不同的情感角度回应")
        
        return suggestions


# 全局实例
enhanced_anti_repetition = EnhancedAntiRepetition()


async def check_response_repetition(
    user_id: str, 
    current_response: str, 
    recent_responses: List[str]
) -> Tuple[bool, Dict]:
    """
    检查回复是否重复
    返回 (is_repetitive, analysis_result)
    """
    analysis = await enhanced_anti_repetition.analyze_response_similarity(
        current_response, recent_responses, user_id
    )
    
    return analysis['is_repetitive'], analysis


async def get_diversity_enhancement_prompt(analysis: Dict, lang: str = "zh-cn") -> str:
    """
    根据分析结果生成多样性增强提示词
    """
    if not analysis['suggestions']:
        return ""
    
    if lang == "zh-cn":
        prompt = f"""
### 🎨 内容多样性增强要求
**检测到的问题**: {analysis['repetition_type'] or '内容相似度过高'}
**相似度评分**: {analysis['overall_similarity']:.2f}/1.0

**改进建议**:
{chr(10).join([f"• {suggestion}" for suggestion in analysis['suggestions']])}

**强制要求**: 必须完全避免重复，创造全新的表达方式！
"""
    else:
        prompt = f"""
### 🎨 Content Diversity Enhancement Requirements
**Detected Issue**: {analysis['repetition_type'] or 'Content similarity too high'}
**Similarity Score**: {analysis['overall_similarity']:.2f}/1.0

**Improvement Suggestions**:
{chr(10).join([f"• {suggestion}" for suggestion in analysis['suggestions']])}

**Mandatory Requirement**: Must completely avoid repetition and create entirely new expressions!
"""
    
    return prompt

    def _calculate_plot_progression(self, current_response: str, recent_responses: List[str]) -> float:
        """计算剧情推进度"""
        try:
            if not recent_responses:
                return 1.0  # 没有历史回复，认为是新剧情

            # 剧情推进关键词
            progression_keywords = {
                'zh': [
                    '然后', '接着', '突然', '这时', '现在', '于是', '随后', '接下来',
                    '开始', '继续', '进入', '转向', '变成', '发展', '推进', '深入',
                    '新的', '另一个', '不同的', '更', '进一步', '升级', '加强'
                ],
                'en': [
                    'then', 'next', 'suddenly', 'now', 'so', 'after', 'following',
                    'begin', 'continue', 'enter', 'turn', 'become', 'develop', 'progress', 'deeper',
                    'new', 'another', 'different', 'more', 'further', 'upgrade', 'enhance'
                ]
            }

            # 检测语言
            is_chinese = any(ord(char) > 127 for char in current_response)
            keywords = progression_keywords['zh'] if is_chinese else progression_keywords['en']

            # 计算当前回复中的推进关键词数量
            current_progression_count = sum(
                1 for keyword in keywords
                if keyword in current_response.lower()
            )

            # 计算最近回复的平均推进关键词数量
            recent_progression_counts = []
            for response in recent_responses[-3:]:  # 只检查最近3个回复
                count = sum(
                    1 for keyword in keywords
                    if keyword in response.lower()
                )
                recent_progression_counts.append(count)

            avg_recent_progression = sum(recent_progression_counts) / len(recent_progression_counts) if recent_progression_counts else 0

            # 检查是否有新的动作或情节元素
            action_keywords = {
                'zh': ['做', '说', '看', '听', '感受', '想', '决定', '选择', '行动'],
                'en': ['do', 'say', 'see', 'hear', 'feel', 'think', 'decide', 'choose', 'act']
            }

            action_words = action_keywords['zh'] if is_chinese else action_keywords['en']
            current_actions = sum(1 for word in action_words if word in current_response.lower())

            # 计算推进度评分
            progression_score = 0.0

            # 1. 推进关键词评分 (40%)
            if current_progression_count > avg_recent_progression:
                progression_score += 0.4
            elif current_progression_count > 0:
                progression_score += 0.2

            # 2. 动作多样性评分 (30%)
            if current_actions > 0:
                progression_score += min(current_actions * 0.1, 0.3)

            # 3. 内容新颖性评分 (30%)
            # 检查是否包含新的概念或描述
            current_words = set(re.findall(r'\w+', current_response.lower()))
            recent_words = set()
            for response in recent_responses[-2:]:
                recent_words.update(re.findall(r'\w+', response.lower()))

            if recent_words:
                new_words_ratio = len(current_words - recent_words) / len(current_words)
                progression_score += new_words_ratio * 0.3
            else:
                progression_score += 0.3

            return min(progression_score, 1.0)

        except Exception as e:
            logger.error(f"计算剧情推进度失败: {str(e)}")
            return 0.5  # 默认中等推进度
