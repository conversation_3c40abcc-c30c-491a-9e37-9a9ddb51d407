"""
AI对话模块 - 重构后的主入口文件
保持向后兼容性，导入所有模块化组件

此文件已被重构为模块化架构：
- ai_dialogue/api_manager.py - API管理和负载均衡
- ai_dialogue/content_processor.py - 内容处理和过滤  
- ai_dialogue/media_handler.py - 多媒体处理
- ai_dialogue/chat_processor.py - 核心对话处理
- ai_dialogue/streaming_handler.py - 流式响应处理
- ai_dialogue/admin_features.py - 管理员功能
- ai_dialogue/user_manager.py - 用户管理
- ai_dialogue/response_sender.py - 响应发送
- ai_dialogue/worker_manager.py - 工作线程管理
- ai_dialogue/utils.py - 通用工具函数
"""

# 导入所有模块化组件，保持向后兼容性
from ai_dialogue import *

# 为了保持完全的向后兼容性，这里重新导出所有主要函数和类
# 这样原有的代码可以继续使用 from ai_dialogue import function_name

# API管理相关
from ai_dialogue.api_manager import (
    api_keepalive, start_api_keepalive_task, get_api_status, 
    get_api_keepalive_status, call_ai_api, call_ai_api_streaming,
    call_ai_api_unified, call_vllm_minicpm_api,
    api_load_tracker, current_api_index, API_SEMAPHORE, last_api_call_time
)

# 内容处理相关
from ai_dialogue.content_processor import (
    preprocess_message, filter_sensitive_words, filter_thinking_chain,
    validate_and_clean_messages, format_settings, build_system_prompt,
    adjust_tone_based_on_preferences
)

# 多媒体处理相关
from ai_dialogue.media_handler import (
    download_and_process_image, download_and_process_video,
    process_single_frame
)

# 对话处理相关
from ai_dialogue.chat_processor import (
    chat_with_ai, chat_with_ai_multimodal, chat_with_ai_streaming,
    chat_with_ai_unified, ask_ai_with_context
)

# 流式处理相关
from ai_dialogue.streaming_handler import (
    send_response_streaming, send_single_sentence
)

# 管理员功能相关
from ai_dialogue.admin_features import (
    start_broadcast, finish_broadcast, confirm_broadcast,
    cancel_broadcast, handle_broadcast_message,
    private_chat_with_user, handle_private_message,
    broadcast_data
)

# 用户管理相关
from ai_dialogue.user_manager import (
    analyze_user_personality, handle_share_rewards,
    determine_voice_usage, reset_daily_voice, daily_reset,
    check_and_notify_usage
)

# 响应发送相关
from ai_dialogue.response_sender import (
    send_response, send_with_retry, keep_typing_alive
)

# 工作线程管理相关
from ai_dialogue.worker_manager import (
    TaskManager, ai_worker, ai_worker_streaming, ai_worker_unified,
    task_manager
)

# 工具函数相关
from ai_dialogue.utils import (
    answer_with_retry, extract_message_info, filter_message
)

# 保持原有的全局变量
from logging_config import logger

# 向后兼容性说明
print("✅ AI对话模块已成功重构为模块化架构")
print("📁 模块位置: ai_dialogue/ 目录")
print("🔄 向后兼容: 原有导入方式仍然有效")
print("🚀 新功能: 模块化设计提供更好的维护性和扩展性")