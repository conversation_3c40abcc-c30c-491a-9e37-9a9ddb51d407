import os
import re
import random
import asyncio
import time
import aiohttp
import uuid
import hashlib
import shutil  # 新增导入
from urllib.parse import urlencode
from datetime import datetime
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.error import TelegramError, Forbidden
from pydub import AudioSegment
from fish_audio_sdk import AsyncWebSocketSession, TTSRequest, ASRRequest
from gradio_client import Client, handle_file
from config import (
    DOWNLOAD_DIR, openai_client, elevenlabs_client, fishaudio_session,
    fishaudio_stt_session, TTS_CONFIG, FISH_STT_CONFIG, REDEEM_CODE_PURCHASE_URL,
    DEFAULT_VOICE, WHISPER_CONFIG
)
from database import get_user_data, save_user_data
from logging_config import logger
from asyncio import Semaphore
import cv2
import logging
from telegram.error import BadRequest

FISH_AUDIO_SEMAPHORE = Semaphore(10)

# 玩法列表
PRACTICES = [
    "被操", "肛门插入", "尿道插入", "扩肛", "扩尿道", "控制高潮", "拒绝高潮", "强制射精", "颜射", "口交",
    "狗姿展示", "住狗笼", "狗项圈", "戴镣铐", "圈养", "看门", "爬行", "钻裆", "舔脚", "舔鞋",
    "舔食", "踩踏", "狗叫", "遛狗", "乳环", "阴茎环", "贞操锁", "肛塞", "身上写字", "剃光头",
    "剃毛", "灌肠", "语言侮辱", "人格侮辱", "思维控制", "耳光", "鞭打", "口塞", "捆绑", "拘束",
    "乳夹", "鸡巴吊重物", "阴囊吊重物", "击打鸡巴", "电击", "体训", "体罚", "冰块", "野外暴露", "公共暴露",
    "睡眠剥夺", "被催眠", "做家务", "伺候主人", "舔精", "吞精", "喝尿（圣水）", "淋尿", "尿扮食物",
    "伺候主人大便", "伺候主人尿尿", "人体马桶", "控制大小便", "脏内裤", "脏鞋袜", "喝洗脚水"
]

# 新的奴种分类系统
SLAVE_TYPES = {
    "dog_slave": {
        "name": "狗奴",
        "description": "狗奴扮演狗的角色，模仿动物行为，接受'主人'训练，强调忠诚和动物化互动。",
        "practices": [
            {"name": "狗项圈", "description": "佩戴项圈，主人用牵引绳控制移动。"},
            {"name": "狗姿展示", "description": "摆出类似狗的姿势，如四肢着地展示身体。"},
            {"name": "住狗笼", "description": "被关在笼子里，模拟宠物环境。"},
            {"name": "爬行", "description": "以四肢爬行移动，强化动物角色。"},
            {"name": "狗叫", "description": "模仿狗的吠叫，响应主人指令。"},
            {"name": "遛狗", "description": "被牵引绳牵着'散步'，可室内或安全户外进行。"},
            {"name": "钻裆", "description": "爬行通过主人双腿间，增加臣服感。"},
            {"name": "舔食", "description": "从狗食盆中'进食'或接受主人喂食。"},
            {"name": "看门", "description": "扮演守门犬角色，象征性守护主人空间。"},
            {"name": "抚摸与奖励", "description": "通过抚摸或表扬强化顺从。"}
        ]
    },
    "foot_slave": {
        "name": "脚奴",
        "description": "脚奴专注于崇拜或服务主人的脚部，足部恋物癖。",
        "practices": [
            {"name": "舔脚", "description": "用嘴清洁或亲吻主人的脚或脚趾。"},
            {"name": "舔鞋", "description": "清洁或崇拜主人的鞋子，如男性的运动鞋、女性的高跟鞋。"},
            {"name": "踩踏", "description": "主人用脚踩你的身体，强度因人而异。"},
            {"name": "足部按摩", "description": "为主人的脚部按摩，表达奉献。"},
            {"name": "脏鞋袜", "description": "崇拜、清洁主人的脏袜子或鞋子，闻、舔、嚼。"},
            {"name": "喝洗脚水", "description": "饮用主人洗脚后的水，或用嘴给主人洗脚，强调臣服。"},
            {"name": "足部束缚", "description": "被主人绑着脚。"}
        ]
    },
    "punishment_slave": {
        "name": "刑奴",
        "description": "刑奴接受惩罚或疼痛以强化纪律和服从，常与S/M结合。",
        "practices": [
            {"name": "鞭打", "description": "被主人使用鞭子、藤条或皮带进行击打。"},
            {"name": "体训", "description": "如站立保持姿势、负重或高强度训练任务。"},
            {"name": "耳光", "description": "你的脸就是主人发泄的工具。"},
            {"name": "拘束", "description": "使用铐具限制手脚活动。"},
            {"name": "捆绑", "description": "用绳子、胶带等束缚身体。"},
            {"name": "口塞", "description": "使用球形口塞或布条塞嘴，限制表达、流口水。"},
            {"name": "睡眠剥夺", "description": "短暂限制睡眠作为惩罚，主人需要关注你的心理影响。"}
        ]
    },
    "sex_slave": {
        "name": "性奴",
        "description": "性奴以性服务或性行为为核心，满足主人的性需求，需明确界限。",
        "practices": [
            {"name": "被操", "description": "被主人的鸡巴或者主人使用假的操你。"},
            {"name": "口交", "description": "为主人进行口交服务。"},
            {"name": "肛门插入（肛塞）", "description": "使用肛塞或玩具进行肛门刺激。"},
            {"name": "控制高潮", "description": "主人决定你是否可达到高潮。"},
            {"name": "拒绝高潮", "description": "禁你的高潮，延长性刺激。"},
            {"name": "强制射精", "description": "通过刺激强制达到射精。"},
            {"name": "颜射", "description": "精液射在你的脸上，刺激不，想吃不。"},
            {"name": "舔精/吞精", "description": "没有什么比吞食你的主人的精液更美味的了。"},
            {"name": "贞操锁", "description": "使用贞操带限制你的性行为，连鸡巴都没有自主的权利。"},
            {"name": "乳环/阴茎环", "description": "佩戴环状装饰或控制性器官。"},
            {"name": "扩肛", "description": "使用玩具或工具进行肛门扩张，需润滑和安全。"},
            {"name": "灌肠", "description": "清洁肛门，常见于肛门玩法前准备。"},
            {"name": "尿道插入（扩尿道）", "description": "使用细小工具刺激尿道，需专业技术和卫生。"}
        ]
    },
    "domestic_slave": {
        "name": "家奴",
        "description": "家奴专注于日常生活服务，强调奉献和非性化任务。",
        "practices": [
            {"name": "做家务", "description": "打扫、洗衣、做饭等，满足主人需求。"},
            {"name": "伺候主人", "description": "如准备衣物、端茶或协助更衣。"},
            {"name": "礼仪训练", "description": "学习特定礼仪，如端茶、各种主人指定的姿势、迎接方式。"},
            {"name": "物品管理", "description": "维护主人的道具或物品（如皮具护理）。"},
            {"name": "长期协议", "description": "制定日常服务规则，形成D/s生活动态。"}
        ]
    },
    "pain_slave": {
        "name": "痛奴",
        "description": "痛奴专注于接受疼痛刺激，满足S/M需求。",
        "practices": [
            {"name": "乳夹", "description": "使用夹具施加乳头疼痛。"},
            {"name": "鸡巴吊重物/阴囊吊重物", "description": "在性器官上悬挂轻量物体，需谨慎。"},
            {"name": "击打鸡巴", "description": "击打性器官，需控制力度和安全。"},
            {"name": "电击", "description": "使用低压电击设备刺激身体。"},
            {"name": "蜡烛玩法", "description": "滴落低温蜡烛制造热感疼痛。"},
            {"name": "冰块", "description": "用冰块接触皮肤，造成冷感刺激。"},
            {"name": "针刺玩法", "description": "浅层刺入皮肤，需专业技术和卫生。"}
        ]
    },
    "humiliation_slave": {
        "name": "羞辱奴",
        "description": "羞辱奴通过羞耻或贬低获得心理满足，需确保心理安全。",
        "practices": [
            {"name": "语言侮辱", "description": "被主人用贬低语言称呼。"},
            {"name": "人格侮辱", "description": "通过语言或行为贬低你的人格。"},
            {"name": "身上写字", "description": "在身体上写羞辱性词语。"},
            {"name": "剃光头/剃毛", "description": "剃除头发或体毛，象征臣服。"},
            {"name": "野外暴露/公共暴露", "description": "在安全、私密场景下暴露身体，明确界限注意安全。"},
            {"name": "脏内裤", "description": "崇拜或清洁主人的脏内裤。"},
            {"name": "思维控制", "description": "通过心理操控（如忽视）制造羞耻感。"},
            {"name": "被催眠", "description": "通过催眠强化臣服感，需专业指导。"}
        ]
    },
    "toilet_slave": {
        "name": "厕奴",
        "description": "厕奴专注于与排泄相关的玩法，强调极端的臣服和羞辱，需严格卫生和同意。",
        "practices": [
            {"name": "喝尿（圣水）/淋尿", "description": "饮用或被淋主人的尿液。"},
            {"name": "伺候主人尿尿", "description": "协助主人排尿，如提供容器或用嘴清洁。"},
            {"name": "伺候主人大便", "description": "伺候或清洁主人排便，用你的嘴擦干净主人的屁股。"},
            {"name": "人体马桶", "description": "扮演'马桶'角色，接受排泄物。"},
            {"name": "尿扮食物", "description": "将尿液用于食物场景。"},
            {"name": "控制大小便", "description": "主人控制臣服者的排泄行为，如定时允许。"}
        ]
    },
    "atm_slave": {
        "name": "ATM奴",
        "description": "ATM奴通过金钱奉献、报销账单或经济服务表达臣服，强调财务控制和权力交换。",
        "practices": [
            {"name": "上供金钱", "description": "定期或按需向主人提供金钱作为奉献。"},
            {"name": "报销账单", "description": "支付主人的账单，如购物、餐饮或生活费用。"},
            {"name": "礼物供奉", "description": "购买礼物（如奢侈品、服装）献给主人。"},
            {"name": "财务控制", "description": "主人管理臣服者的预算或开支，需明确协议。"},
            {"name": "金钱羞辱", "description": "通过语言强调臣服者的金钱奉献，如'你的钱只为我服务'。"},
            {"name": "任务换金钱", "description": "完成主人指定的任务（如羞辱或服务）以换取支付机会。"},
            {"name": "公开奉献", "description": "在双方同意的场景下（如线上平台）展示金钱奉献，需保护隐私。"}
        ]
    }
}

# 奴种显示顺序
SLAVE_TYPE_ORDER = [
    "dog_slave", "foot_slave", "punishment_slave", "sex_slave", 
    "domestic_slave", "pain_slave", "humiliation_slave", "toilet_slave", "atm_slave"
]

async def send_with_retry(bot, method, max_retries=3, retry_delay=10, user_id=None, **kwargs):
    """重试发送Telegram消息，处理限流和屏蔽错误"""
    start_time = time.time()
    for attempt in range(max_retries):
        try:
            result = await asyncio.wait_for(method(**kwargs), timeout=30.0)
            logger.info(f"发送成功，用户ID: {user_id}, 耗时: {(time.time() - start_time):.2f} 秒")
            return result
        except asyncio.TimeoutError:
            logger.warning(f"发送超时，尝试 {attempt + 1}/{max_retries}，用户ID: {user_id}, 耗时: {(time.time() - start_time):.2f} 秒")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (2 ** attempt))
                continue
            logger.error(f"发送消息超时，用户ID: {user_id}, 超过最大重试次数")
            raise
        except Forbidden as e:
            logger.warning(f"用户 {user_id} 已屏蔽Bot，错误: {str(e)}")
            # 如果有用户ID，更新用户的is_blocked状态
            if user_id:
                try:
                    from database import get_user_data, save_user_data
                    user_data = await get_user_data(user_id)
                    user_data["is_blocked"] = 1
                    await save_user_data(user_id, user_data)
                    logger.info(f"用户 {user_id} 的is_blocked状态已设置为1")
                except Exception as db_error:
                    logger.error(f"更新用户 {user_id} 的is_blocked状态失败: {str(db_error)}")
            return None
        except TelegramError as e:
            if "Too Many Requests" in str(e):
                logger.warning(f"API限流，尝试 {attempt + 1}/{max_retries}，用户ID: {user_id}, 错误: {str(e)}")
                await asyncio.sleep(retry_delay * (2 ** attempt))
                continue
            logger.error(f"发送失败，尝试 {attempt + 1}/{max_retries}，用户ID: {user_id}, 错误: {str(e)}")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (2 ** attempt))
                continue
            logger.error(f"发送消息失败，用户ID: {user_id}, 超过最大重试次数")
            raise
    logger.error(f"发送消息失败，用户ID: {user_id}, 超过最大重试次数")
    return None

def get_main_menu(user_id=None, context=None):
    welcome_text = (
        "🔥 贱奴！跪在爷面前，准备接受调教吧！🔥\n"
        "选择你的命运，别浪费爷的时间！\n"
        "───────────────────"
    )
    buttons = [
        [InlineKeyboardButton("⚙️ 调教设定", callback_data="training_settings_menu"),
         InlineKeyboardButton("🎲 骰子羞辱", callback_data="dice_game")],
        [InlineKeyboardButton("🧠 任务中心", url="https://t.me/bdsm_taskbot"),
         InlineKeyboardButton("⏱️ 寸止折磨", url="https://www.silencedom.com/edge")],
        [InlineKeyboardButton("🔍 换脸脱衣", url="https://t.me/guangliuliubot?start=KQBV3BVQJYUNCT0K"),
         InlineKeyboardButton("📞 联系真爹", url="https://t.me/deanmasterbot")],
        [InlineKeyboardButton("🎙️ 语音设置", callback_data=f"voice_settings_{user_id}"),
         InlineKeyboardButton("💰 免费积分", callback_data=f"free_credits_{user_id}")],
        [InlineKeyboardButton("🍾 漂流瓶", callback_data=f"drift_bottle_{user_id}"),
         InlineKeyboardButton("🔍 资源搜索", url="https://t.me/soso?start=a_7359571866")]
#        [InlineKeyboardButton("🔍 免费流量", url="https://hy.yunhaoka.com/#/pages/micro_store/index?agent_id=26e9d5563becbc622210db56129c38e0")]
    ]

    if user_id:
        # 移除调教模式按钮
        buttons.append([InlineKeyboardButton("👤 个人中心", callback_data=f"personal_center_{user_id}")])

    return InlineKeyboardMarkup(buttons)

def get_quick_reply_menu(user_id):
    keyboard = [
        [KeyboardButton("菜单"), KeyboardButton("调教风格"), KeyboardButton("犬舍")]
    ]
    return ReplyKeyboardMarkup(
        keyboard,
        resize_keyboard=True,
        one_time_keyboard=False,
        is_persistent=True  # 确保菜单持续显示在输入框右侧
    )

async def get_free_credits_menu(user_id):
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    search_invite_link = get_search_group_invite_link(user_id)
    hint = (
        "💰 **贱奴！白嫖积分指南** 💰\n"
        "───────────────────\n"
        f"{'当前每日免费狗粮' if lang == 'zh-cn' else 'Current Daily Free Dog Food'}: {user_data.get('daily_free_dog_food', 0)} 个\n"
        "1️⃣ 分享Bot拉新：每人3个狗粮🦴\n"
        "2️⃣ 关注频道：首次5个狗粮🦴，每日访问2个狗粮🦴\n"
        "3️⃣ 搜索群拉新：每人1个狗粮🦴，5人额外2个狗粮🦴\n"
        "4️⃣ 搜索群搜索资源：每次搜索1个免费狗粮🦴，每日最多5次\n"
        "───────────────────\n"
        "🔥 选个方式赚，别磨蹭！"
    )
    keyboard = [
        [InlineKeyboardButton("📢 分享Bot拉新" if lang == "zh-cn" else "📢 Share Bot to Earn", callback_data=f"share_bot_{user_id}")],
        [InlineKeyboardButton("📣 关注频道" if lang == "zh-cn" else "📣 Follow Channel", callback_data=f"follow_channel_{user_id}")],
        [InlineKeyboardButton("🔍 搜索群拉新" if lang == "zh-cn" else "🔍 Search Group Invite", callback_data=f"search_group_invite_{user_id}")],
        [InlineKeyboardButton("🔍 搜索群搜索资源" if lang == "zh-cn" else "🔍 Search Group Resources", callback_data=f"search_group_{user_id}")],
        [InlineKeyboardButton("🏃 返回主菜单" if lang == "zh-cn" else "🏃 Back to Main Menu", callback_data="back_to_main")]
    ]
    return InlineKeyboardMarkup(keyboard), hint

async def get_voice_settings_menu(user_id, page=None):
    """获取美化的语音设置菜单，显示语音选择和分页导航，每页4行，每行2个语音，支持多种 TTS 服务，并包含自定义声音"""
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    remaining_count = user_data["remaining_count"]
    voice_count = user_data["voice_count"]
    daily_voice_count = user_data["daily_voice_count"]
    selected_voice = user_data.get("selected_voice", DEFAULT_VOICE)
    
    # 根据主人性别自动选择默认声音
    master_gender = user_data.get("master_gender", "male")
    if selected_voice == DEFAULT_VOICE:  # 如果用户还没有选择过声音，根据性别自动选择
        if master_gender == "female":
            # 查找女性主人声音
            female_voice_id = None
            for svc in ["fishaudio", "local_tts"]:
                if TTS_CONFIG.get(svc, {}).get("enabled", False):
                    for v in TTS_CONFIG[svc].get("voices", []):
                        if v.get("reference_id") == "local_female_master":
                            female_voice_id = v.get("reference_id")
                            break
                    if female_voice_id:
                        break
            
            if female_voice_id and selected_voice == DEFAULT_VOICE:
                selected_voice = female_voice_id
                user_data["selected_voice"] = selected_voice
                await save_user_data(user_id, user_data)
                logger.info(f"用户 {user_id} 主人性别为女性，自动选择女性主人声音: {selected_voice}")

    current_method = ("文字调教" if lang == "zh-cn" else "Text Training") if selected_voice == "text" else \
                     next(
                         (v["name"] for svc in ["fishaudio", "local_tts"] for v in TTS_CONFIG.get(svc, {}).get("voices", []) if v.get("reference_id") == selected_voice),
                         "未知声音"
                     )

    # 合并启用的 TTS 服务的语音列表
    voices = []
    for svc in ["fishaudio", "local_tts"]:
        if TTS_CONFIG.get(svc, {}).get("enabled", False):
            voices.extend(TTS_CONFIG[svc].get("voices", []))

    # 检查用户是否上传了自定义声音，如果有则添加到列表
    custom_voice_path = os.path.join("reference_audios", f"{user_id}_voice1.mp3")
    if os.path.exists(custom_voice_path):
        custom_voice_id = f"custom_{user_id}"
        # 检查是否已经在列表中，避免重复添加
        if not any(v.get("reference_id") == custom_voice_id for v in voices):
            voices.append({
                "name": "自定义声音" if lang == "zh-cn" else "Custom Voice",
                "reference_id": custom_voice_id,
                "reference_audio": custom_voice_path,
                "service": "local_tts"
            })
            logger.info(f"用户 {user_id} 的自定义声音已添加到语音列表，ID: {custom_voice_id}, 文件路径: {custom_voice_path}")
        else:
            logger.debug(f"用户 {user_id} 的自定义声音已在语音列表中，ID: {custom_voice_id}")
    else:
        logger.debug(f"用户 {user_id} 没有自定义语音文件: {custom_voice_path}")

    # 如果没有可用的语音模型，显示警告
    if not voices:
        logger.warning(f"用户 {user_id} 没有可用的语音模型，请检查 TTS_CONFIG 配置")
        menu_text = (
            f"🎙️ {'语音设置' if lang == 'zh-cn' else 'Voice Settings'} 🎙️\n"
            f"────────────\n"
            f"{'当前方式' if lang == 'zh-cn' else 'Current Method'}: {current_method}\n"
            f"{'每日免费语音' if lang == 'zh-cn' else 'Daily Free Voice'}: {daily_voice_count}\n"
            f"{'充值语音' if lang == 'zh-cn' else 'Paid Voice'}: {voice_count}\n"
            f"────────────\n"
            f"{'警告：没有可用的语音模型！' if lang == 'zh-cn' else 'Warning: No available voice models!'}\n"
            f"{'请联系管理员检查配置。' if lang == 'zh-cn' else 'Please contact admin to check configuration.'}"
        )
        keyboard = [[InlineKeyboardButton(f"🏠 {'返回主菜单' if lang == 'zh-cn' else 'Back to Main Menu'}", callback_data="back_to_main")]]
        return menu_text, InlineKeyboardMarkup(keyboard)

    voices_per_page = 8  # 每页8个语音选项（4行，每行2个）
    total_pages = max(1, (len(voices) + voices_per_page - 1) // voices_per_page)  # 计算总页数，确保至少1页

    if page is None:
        page = user_data.get("voice_settings_page", 0)  # 从用户数据加载上次的页码
    page = max(0, min(page, total_pages - 1))  # 确保页码在有效范围内

    # 更新用户数据中的页码
    user_data["voice_settings_page"] = page
    await save_user_data(user_id, user_data)

    menu_text = (
        f"🎙️ {'语音设置' if lang == 'zh-cn' else 'Voice Settings'} 🎙️\n"
        f"────────────\n"
        f"{'当前方式' if lang == 'zh-cn' else 'Current Method'}: {current_method}\n"
        f"{'每日免费语音' if lang == 'zh-cn' else 'Daily Free Voice'}: {daily_voice_count}\n"
        f"{'充值语音' if lang == 'zh-cn' else 'Paid Voice'}: {voice_count}\n"
        f"{'页数' if lang == 'zh-cn' else 'Page'}: {page + 1}/{total_pages}\n"
        f"────────────\n"
        f"{'选择语音或文字模式' if lang == 'zh-cn' else 'Choose Voice or Text Mode'}:"
    )

    start_idx = page * voices_per_page
    end_idx = min(start_idx + voices_per_page, len(voices))

    logger.debug(f"用户 {user_id} 语音设置菜单，页码: {page + 1}/{total_pages}, 语音总数: {len(voices)}, 显示范围: {start_idx}-{end_idx}")

    keyboard = [[InlineKeyboardButton(f"📝 {'文字调教' if lang == 'zh-cn' else 'Text Training'} {'✅' if selected_voice == 'text' else '⬜'}", callback_data=f"choose_voice_text_{user_id}")]]

    page_voices = voices[start_idx:end_idx]
    logger.debug(f"用户 {user_id} 当前页语音列表: {[v['name'] for v in page_voices]}")
    for i in range(0, len(page_voices), 2):
        row = [
            InlineKeyboardButton(
                f"🎤 {page_voices[j]['name']} {'✅' if selected_voice == page_voices[j]['reference_id'] else '⬜'}",
                callback_data=f"choose_voice_{page_voices[j]['reference_id']}_{user_id}"
            )
            for j in range(i, min(i + 2, len(page_voices)))
        ]
        keyboard.append(row)

    nav_row = []
    if page > 0:
        nav_row.append(InlineKeyboardButton(f"⬅️ {'上一页' if lang == 'zh-cn' else 'Previous'}", callback_data=f"prev_voice_page_{user_id}"))
    if page < total_pages - 1:
        nav_row.append(InlineKeyboardButton(f"{'下一页' if lang == 'zh-cn' else 'Next'} ➡️", callback_data=f"next_voice_page_{user_id}"))
    if nav_row:
        keyboard.append(nav_row)
    else:
        logger.debug(f"用户 {user_id} 没有翻页按钮，当前页: {page + 1}, 总页数: {total_pages}")

    # 增加"自定义声音"按钮，用于进入自定义声音管理界面
    keyboard.append([InlineKeyboardButton(f"🎤 {'自定义声音' if lang == 'zh-cn' else 'Custom Voice'}", callback_data=f"custom_voice_{user_id}")])
    keyboard.append([InlineKeyboardButton(f"🏠 {'返回主菜单' if lang == 'zh-cn' else 'Back to Main Menu'}", callback_data="back_to_main")])

    return menu_text, InlineKeyboardMarkup(keyboard)

def filter_xml_tags(text):
    """
    过滤AI回复中的XML标签
    - 移除所有的<plot>...</plot>内容
    - 移除所有的<summary>...</summary>内容
    - 保留<dream>...</dream>中间的内容，但移除标签本身
    - 处理不完整的标签
    """
    try:
        import re
        
        if not text:
            return text
        
        original_text = text
        logger.debug(f"原始文本长度: {len(text)}, 开头: {text[:100]}...")
        
        # 移除<plot>...</plot>内容（包括标签）
        text = re.sub(r'<plot>.*?</plot>', '', text, flags=re.DOTALL)
        
        # 移除<summary>...</summary>内容（包括标签）
        text = re.sub(r'<summary>.*?</summary>', '', text, flags=re.DOTALL)
        
        # 处理<dream>标签
        # 1. 先处理完整的<dream>...</dream>标签
        text = re.sub(r'<dream>(.*?)</dream>', r'\1', text, flags=re.DOTALL)
        
        # 2. 处理不完整的<dream>标签（只有开始标签的情况）
        if '<dream>' in text and '</dream>' not in text:
            # 如果只有开始标签，移除标签保留内容
            text = re.sub(r'<dream>\s*', '', text)
            logger.warning(f"发现不完整的<dream>标签，已移除开始标签")
        
        # 3. 处理其他可能的不完整标签
        text = re.sub(r'</?dream>', '', text)  # 移除任何剩余的dream标签
        text = re.sub(r'</?plot>', '', text)   # 移除任何剩余的plot标签
        text = re.sub(r'</?summary>', '', text) # 移除任何剩余的summary标签
        
        # 清理多余的换行符和空格
        text = re.sub(r'\n\s*\n', '\n', text)  # 将多个连续换行转为单个换行
        text = text.strip()
        
        if text != original_text:
            logger.debug(f"XML过滤后文本长度: {len(text)}, 开头: {text[:100]}...")
        
        return text
        
    except Exception as e:
        logger.error(f"过滤XML标签失败: {str(e)}")
        return text  # 如果过滤失败，返回原始文本

def process_ai_response(text):
    """
    优化后处理逻辑，按中英文标点分割句子，保留语气标点，并将连续标点归并到前一句，处理引号逻辑

    优化的连续标点处理：
    - 当遇到断句标点时，检查下一个字符
    - 如果下一个字符是。？！"）这五个标点，暂时不断句
    - 注意：这里的"是中文右侧引号，不是左侧引号"
    - 继续检查直到不是这些标点为止，然后进行断句
    """
    try:
        text = text.strip()
        if not text:
            return []

        logger.debug(f"开始分句处理，原始文本长度: {len(text)}")

        # 预处理：将连续的标点符号合并，避免被单独分割
        # 处理连续的省略号和其他连续标点符号
        text = re.sub(r'([。！？.!?])\s*([。！？.!?]+)', r'\1\2', text)
        # 特别处理连续的省略号，确保它们不被分割
        text = re.sub(r'\.{2,}', lambda m: m.group(0), text)

        # 改进的分句逻辑：检查下一个字符是否为右引号或右括号
        sentences = []
        current_sentence = ""
        quote_count = 0
        max_sentence_length = 500

        # 定义右引号和右括号
        right_quotes_brackets = ['"', '"', '』', '》', '】', ')', ']', '}', '）', '］', '｝']

        # 逐字符处理，确保引号配对
        i = 0
        while i < len(text):
            char = text[i]
            current_sentence += char

            # 计算引号数量
            if char == '"':
                quote_count += 1

            # 检查是否为句子结束标点
            if char in ['。', '！', '？', '.', '!', '?']:
                # 按照用户要求优化：检查后续连续的特定标点符号（。？！"）），直到不是这些标点为止
                j = i + 1
                continuous_punct = False

                # 继续检查后续字符，如果是。？！"）这五个标点，继续累积
                # 注意：这里的"是中文右侧引号，不是左侧引号"
                while j < len(text):
                    next_char = text[j]
                    # 检查下一个字符是否是需要特殊处理的标点：。？！"）
                    # 使用中文右侧引号"，而不是左侧引号"或英文引号"
                    if next_char in ['。', '？', '！', '"', '）', '.', '?', '!', ')']:
                        current_sentence += next_char
                        continuous_punct = True
                        j += 1
                    else:
                        # 如果不是这些特定标点，停止检查
                        break

                if continuous_punct:
                    i = j - 1  # 更新索引到最后一个特定标点

                # 检查引号配对，如果引号已配对，可以结束句子
                if quote_count % 2 == 0:
                    sentence = current_sentence.strip()
                    if sentence:
                        # 过滤思维标记和特殊内容
                        if not should_filter_sentence(sentence):
                            # 检查句子长度，如果太短则与下一句合并
                            if len(sentence) <= 5 and i + 1 < len(text):
                                # 句子太短，继续累积，不断句
                                logger.debug(f"句子太短({len(sentence)}字)，不断句: {sentence}")
                                # 不重置current_sentence，继续累积
                            else:
                                # 句子长度合适，可以断句
                                sentences.append(sentence)
                                logger.debug(f"添加句子: {sentence[:50]}...")
                                current_sentence = ""
                                quote_count = 0
                    else:
                        current_sentence = ""
                        quote_count = 0
                # 如果引号未配对，继续累积

            # 检查句子长度，防止过长
            elif len(current_sentence) > max_sentence_length and quote_count % 2 == 0:
                # 只有在引号配对的情况下才强制分割
                sentence = current_sentence.strip()
                if sentence:
                    if not should_filter_sentence(sentence):
                        sentences.append(sentence)
                        logger.debug(f"强制分割长句: {sentence[:50]}...")
                current_sentence = ""
                quote_count = 0

            i += 1

        # 处理剩余内容
        if current_sentence.strip():
            sentence = current_sentence.strip()
            if not should_filter_sentence(sentence):
                sentences.append(sentence)
                logger.debug(f"添加最后剩余句子: {sentence[:50]}...")

        # 最终过滤
        filtered_sentences = []
        for s in sentences:
            s = s.strip()
            # 过滤只包含标点符号的句子
            if s and not re.match(r'^[。！？.!?~@#$%^&*()_=+\s"""''`\[\]{}|\\/:;<>,-]+$', s):
                filtered_sentences.append(s)
            else:
                if s:
                    logger.debug(f"过滤掉只包含标点符号的句子: '{s}'")

        logger.info(f"分句结果，句子数: {len(filtered_sentences)}, 句子: {filtered_sentences}")
        return filtered_sentences

    except Exception as e:
        logger.error(f"分句处理失败，错误: {str(e)}", exc_info=True)
        return [text]  # 回退：返回原始文本作为单句

def should_filter_sentence(sentence):
    """检查句子是否应该被过滤"""
    # 过滤思维提示词相关内容
    if re.search(r'<(think|thinking)>|</?(think|thinking)>', sentence, re.IGNORECASE):
        logger.warning(f"发现句子中包含思维标记，跳过: {sentence[:50]}...")
        return True

    # 过滤特殊指令行
    if re.search(r'^--\s*consider:|^-->|^\s*--\s*>|^<!--', sentence):
        logger.warning(f"发现句子中包含指令行，跳过: {sentence[:50]}...")
        return True

    # 过滤包含①②③等标记的行
    if re.search(r'[①②③④⑤]<[\w]+>内', sentence):
        logger.warning(f"发现句子中包含角标思维内容，跳过: {sentence[:50]}...")
        return True

    # 过滤模型自我指称
    if re.search(r'我是.*?基于.*?(claude|AI助手)', sentence):
        logger.warning(f"发现句子中包含模型自我指称，跳过: {sentence[:50]}...")
        return True

    return False

def get_active_tts():
    """获取当前启用的 TTS 服务"""
    # 检查每个服务是否启用，并按优先级返回第一个启用的服务名称
    for svc in ["fishaudio", "local_tts", "openai", "elevenlabs"]:
        if TTS_CONFIG.get(svc, {}).get("enabled", False):
            return svc
    raise ValueError("没有启用任何 TTS 服务，请在 TTS_CONFIG 中至少启用一个服务")

async def get_redeem_code_menu(user_id):
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    keyboard = [
        [InlineKeyboardButton("前往 @master_kabot 购买" if lang == "zh-cn" else "Buy at @master_kabot", url=REDEEM_CODE_PURCHASE_URL)],
        [InlineKeyboardButton("返回支付菜单" if lang == "zh-cn" else "Back to Payment Menu", callback_data=f"go_to_pay_{user_id}")]
    ]
    hint = (
        "💰 **贱奴！兑换码使用说明** 💰\n"
        "───────────────────\n"
        "点击下方按钮前往 @master_kabot 购买兑换码（支持微信，支付宝）。\n"
        "已有兑换码？直接在下方输入！\n"
        "───────────────────\n"
        "🔥 快行动，别浪费爷的时间！"
        if lang == "zh-cn" else
        "💰 **Filthy dog! Redeem Code Guide** 💰\n"
        "───────────────────\n"
        "Click below to buy a redeem code at @master_kabot (supports WeChat, Alipay).\n"
        "Already have a code? Enter it below!\n"
        "───────────────────\n"
        "🔥 Move fast, don't waste my time!"
    )
    return InlineKeyboardMarkup(keyboard), hint

# Ensure ACTIVE_TTS is defined after TTS_CONFIG is loaded
try:
    ACTIVE_TTS = get_active_tts()
except ValueError as e:
    logger.error(f"初始化 ACTIVE_TTS 失败: {e}")
    ACTIVE_TTS = None # Set to None or a default if no service is enabled


async def text_to_speech(text, logger, user_id=None):
    """使用 FishAudio 或本地 index-tts API 将文本转换为语音，支持批次推理并分割长文本，支持多用户并发"""
    if not text.strip():
        logger.error(f"文本为空，用户ID: {user_id}")
        return None

    voice_id = DEFAULT_VOICE
    service = None
    reference_audio_path = None

    # --- 查找语音模型所属的服务和参考音频路径 ---
    if user_id:
        user_data = await get_user_data(user_id)
        voice_id = user_data.get("selected_voice", DEFAULT_VOICE)
        
        # 根据主人性别自动选择默认声音
        master_gender = user_data.get("master_gender", "male")
        if voice_id == DEFAULT_VOICE:  # 如果用户还没有选择过声音，根据性别自动选择
            if master_gender == "female":
                # 查找女性主人声音
                female_voice_id = None
                for svc in ["fishaudio", "local_tts"]:
                    if TTS_CONFIG.get(svc, {}).get("enabled", False):
                        for v in TTS_CONFIG[svc].get("voices", []):
                            if v.get("reference_id") == "local_female_master":
                                female_voice_id = v.get("reference_id")
                                break
                        if female_voice_id:
                            break
                
                if female_voice_id:
                    voice_id = female_voice_id
                    user_data["selected_voice"] = voice_id
                    await save_user_data(user_id, user_data)
                    logger.info(f"用户 {user_id} 主人性别为女性，自动选择女性主人声音: {voice_id}")
        
        if voice_id == "text":
            logger.info(f"用户 {user_id} 选择文字模式，不生成语音")
            return None

        # 查找语音模型所属的服务和参考音频路径
        for svc in ["fishaudio", "local_tts"]:
            if not TTS_CONFIG.get(svc, {}).get("enabled", False):
                continue
            for v in TTS_CONFIG[svc].get("voices", []):
                if v.get("reference_id") == voice_id:
                    service = svc
                    if svc == "local_tts":
                        reference_audio_path = v.get("reference_audio", "")
                        # 转换为绝对路径，假设相对路径是相对于 bot 根目录
                        if reference_audio_path and not os.path.isabs(reference_audio_path):
                            reference_audio_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), reference_audio_path)
                    break
            if service:
                break

        # 如果用户选择的语音模型不可用，回退到默认语音模型
        if not service:
            # 特别处理自定义语音
            if voice_id.startswith("custom_"):
                custom_voice_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reference_audios", f"{user_id}_voice1.mp3")
                if os.path.exists(custom_voice_path):
                    service = "local_tts"
                    reference_audio_path = custom_voice_path
                    logger.info(f"用户 {user_id} 使用自定义语音文件: {custom_voice_path}")
                    # 动态添加到 TTS_CONFIG 中，确保后续查找时能找到
                    custom_voice_id = f"custom_{user_id}"
                    if "local_tts" in TTS_CONFIG and TTS_CONFIG["local_tts"].get("enabled", False):
                        if not any(v.get("reference_id") == custom_voice_id for v in TTS_CONFIG["local_tts"].get("voices", [])):
                            TTS_CONFIG["local_tts"]["voices"].append({
                                "name": "自定义声音" if user_data.get("preferred_language", "zh-cn") == "zh-cn" else "Custom Voice",
                                "reference_id": custom_voice_id,
                                "reference_audio": custom_voice_path,
                                "service": "local_tts"
                            })
                            logger.info(f"用户 {user_id} 的自定义语音已动态添加到 TTS_CONFIG，ID: {custom_voice_id}")
                else:
                    logger.warning(f"用户 {user_id} 的自定义语音文件不存在: {custom_voice_path}，回退到默认语音模型 {DEFAULT_VOICE}")
                    voice_id = DEFAULT_VOICE
                    service = None
                    reference_audio_path = None
            if not service:
                logger.info(f"用户 {user_id} 选择的语音模型 {voice_id} 不可用，回退到默认语音模型 {DEFAULT_VOICE}")
                voice_id = DEFAULT_VOICE
                for svc in ["fishaudio", "local_tts"]:
                    if TTS_CONFIG.get(svc, {}).get("enabled", False):
                        for v in TTS_CONFIG[svc].get("voices", []):
                            if v.get("reference_id") == voice_id:
                                service = svc
                                if svc == "local_tts":
                                    reference_audio_path = v.get("reference_audio", "")
                                    if reference_audio_path and not os.path.isabs(reference_audio_path):
                                        reference_audio_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), reference_audio_path)
                                break
                        if service:
                            break
                if not service:
                    logger.warning(f"用户 {user_id} 回退的默认语音模型 {voice_id} 也不可用，无法切换")
                    return None

    # 如果仍未找到可用服务，记录错误并返回 None
    if not service:
        logger.error(f"没有可用的 TTS 服务，用户ID: {user_id}")
        return None

    temp_file = os.path.join(DOWNLOAD_DIR, f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4()}.wav")

    # 分割长文本以适应批次推理
    max_length = 500  # 根据 API 的限制调整
    try:
        text = text.strip()
        if not text:
            return None
            
        # 预处理：将连续的标点符号合并，避免被单独分割
        # 处理连续的省略号和其他连续标点符号
        text = re.sub(r'([。！？.!?])\s*([。！？.!?]+)', r'\1\2', text)
        # 特别处理连续的省略号，确保它们不被分割
        text = re.sub(r'\.{2,}', lambda m: m.group(0), text)
        
        # 改进的TTS分句逻辑：检查下一个字符是否为右引号或右括号
        segments = []
        current_segment = ""
        quote_count = 0

        # 定义右引号和右括号
        right_quotes_brackets = ['"', '"', '』', '》', '】', ')', ']', '}', '）', '］', '｝']

        # 逐字符处理，确保引号配对和延迟断句
        i = 0
        while i < len(text):
            char = text[i]
            current_segment += char

            # 计算引号数量
            if char == '"':
                quote_count += 1

            # 检查是否为句子结束标点
            if char in ['。', '！', '？', '.', '!', '?']:
                # 检查下一个字符是否为右引号或右括号
                should_delay = False
                if i + 1 < len(text):
                    next_char = text[i + 1]
                    if next_char in right_quotes_brackets:
                        should_delay = True
                        logger.debug(f"TTS分句：检测到下一个字符是右引号/括号 '{next_char}'，延迟断句")

                # 如果不需要延迟断句，且引号已配对，可以结束句子
                if not should_delay and quote_count % 2 == 0:
                    segment = current_segment.strip()
                    if segment:
                        # 检查句子长度，如果太短则与下一句合并（TTS效果优化）
                        if len(segment) <= 5 and i + 1 < len(text):
                            # 句子太短，继续累积，不断句
                            logger.debug(f"TTS句子太短({len(segment)}字)，不断句: {segment}")
                            # 不重置current_segment，继续累积
                        else:
                            # 句子长度合适，可以断句
                            segments.append(segment)
                            logger.debug(f"TTS添加句子: {segment[:50]}...")
                            current_segment = ""
                            quote_count = 0
                    else:
                        current_segment = ""
                        quote_count = 0
                # 如果需要延迟断句或引号未配对，继续累积

            # 检查句子长度，防止过长
            elif len(current_segment) > max_length and quote_count % 2 == 0:
                # 只有在引号配对的情况下才强制分割
                segment = current_segment.strip()
                if segment:
                    segments.append(segment)
                    logger.debug(f"TTS强制分割长句: {segment[:50]}...")
                current_segment = ""
                quote_count = 0

            i += 1

        # 处理剩余内容
        if current_segment.strip():
            segment = current_segment.strip()
            segments.append(segment)
            logger.debug(f"TTS添加最后剩余句子: {segment[:50]}...")

        # 过滤空句子和只包含标点符号的句子
        filtered_segments = []
        for s in segments:
            s = s.strip()
            # 添加引号和其他常见标点符号到过滤正则表达式中
            if s and not re.match(r'^[。！？.!?~@#$%^&*()-_=+\s"""''`\[\]{}|\\/:;<>,]+$', s):
                filtered_segments.append(s)
            else:
                if s:  # 如果被过滤的句子不为空，记录日志
                    logger.debug(f"过滤掉只包含标点符号的句子: '{s}'")
        segments = filtered_segments
        
        logger.info(f"分句结果，句子数: {len(segments)}, 句子: {segments}")
    except Exception as e:
        logger.error(f"分句处理失败，错误: {str(e)}", exc_info=True)
        segments = [text]  # 回退：返回原始文本作为单句

    audio_files = []
    start_time = time.time()

    if service == "fishaudio":
        if not TTS_CONFIG.get("fishaudio", {}).get("enabled", False):
            logger.error(f"FishAudio 服务未启用，用户ID: {user_id}")
            return None

        api_key = TTS_CONFIG["fishaudio"]["api_key"]
        async_websocket = AsyncWebSocketSession(api_key)
        tts_request = TTSRequest(
            text="",
            reference_id=voice_id,
            temperature=0.7,
            top_p=0.7
        )

        async with FISH_AUDIO_SEMAPHORE:
            for idx, segment in enumerate(segments):
                if not segment.strip():
                    continue
                segment_file = os.path.join(DOWNLOAD_DIR, f"temp_segment_{uuid.uuid4()}.mp3")
                async def stream_segment():
                    words = segment.split()
                    for word in words:
                        yield word + " "
                    if segment and not segment.endswith(" "):
                        yield ""

                for attempt in range(3):
                    try:
                        segment_start = time.time()
                        with open(segment_file, "wb") as f:
                            async for chunk in async_websocket.tts(
                                tts_request,
                                stream_segment(),
                                backend="speech-1.6"
                            ):
                                f.write(chunk)
                        elapsed = time.time() - segment_start
                        logger.info(f"FishAudio TTS 段生成成功，用户ID: {user_id}, 文件: {segment_file}, 耗时: {elapsed:.2f} 秒")
                        audio_files.append(segment_file)
                        break
                    except Exception as e:
                        logger.error(f"FishAudio TTS 段失败，尝试 {attempt + 1}/3，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
                        if attempt < 2:
                            await asyncio.sleep(3 * (2 ** attempt))
                        continue
                else:
                    logger.error(f"FishAudio TTS 段最终失败，用户ID: {user_id}, 段内容: {segment[:100]}...")

    elif service == "local_tts":
        if not TTS_CONFIG.get("local_tts", {}).get("enabled", False):
            logger.error(f"本地 TTS 服务未启用，用户ID: {user_id}")
            return None

        if not reference_audio_path or not os.path.exists(reference_audio_path):
            logger.error(f"参考音频文件不存在: {reference_audio_path}, 用户ID: {user_id}")
            return None

        # 使用 aiohttp 进行 FastAPI 调用
        api_url = TTS_CONFIG["local_tts"]["api_url"]
        if not api_url.endswith("/"):
            api_url += "/"
        endpoint = f"{api_url}tts_url"  # 假设 FastAPI 端点为 /tts_url

        # 使用 Semaphore 限制并发，防止本地 TTS 服务过载
        LOCAL_TTS_SEMAPHORE = Semaphore(5)  # 限制为 5 个并发请求
        async with LOCAL_TTS_SEMAPHORE:
            for idx, segment in enumerate(segments):
                if not segment.strip():
                    continue
                try:
                    logger.info(f"生成本地 TTS 段 {idx+1}/{len(segments)}，用户ID: {user_id}")
                    data = {
                        "text": segment,
                        "audio_paths": [reference_audio_path]  # 传递绝对路径
                    }
                    async with aiohttp.ClientSession() as session:
                        async with session.post(endpoint, json=data) as response:
                            if response.status == 200:
                                segment_file = os.path.join(DOWNLOAD_DIR, f"temp_segment_{uuid.uuid4()}.wav")
                                with open(segment_file, "wb") as f:
                                    f.write(await response.read())
                                logger.info(f"本地 TTS 段 {idx+1} 生成成功，用户ID: {user_id}, 文件: {segment_file}")
                                audio_files.append(segment_file)
                            else:
                                error_text = await response.text()
                                logger.error(f"本地 TTS 段 {idx+1} 生成失败，用户ID: {user_id}, 状态码: {response.status}, 错误: {error_text}")
                except Exception as e:
                    logger.error(f"本地 TTS 段 {idx+1} 生成失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)

    if not audio_files:
        logger.error(f"所有 TTS 段生成失败，用户ID: {user_id}")
        return None

    # 合并音频文件
    combined = AudioSegment.empty()
    for audio_path in audio_files:
        if isinstance(audio_path, str) and os.path.exists(audio_path):
            try:
                audio = AudioSegment.from_file(audio_path)
                combined += audio
                try:
                    os.remove(audio_path)
                    logger.debug(f"已删除临时音频文件: {audio_path}")
                except Exception as e:
                    logger.error(f"删除临时音频文件失败，文件: {audio_path}, 错误: {str(e)}")
            except Exception as e:
                logger.error(f"加载或合并音频文件失败: {audio_path}, 错误: {str(e)}")
        else:
            logger.warning(f"音频文件不存在或类型错误，跳过合并: {audio_path}")

    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    try:
        combined.export(temp_file, format="wav")
        logger.info(f"TTS 成功，用户ID: {user_id}, 文件: {temp_file}, 服务: {service}, 总耗时: {time.time() - start_time:.2f} 秒")
        return temp_file
    except Exception as e:
        logger.error(f"导出合并音频文件失败: {temp_file}, 错误: {str(e)}")
        for audio_path in audio_files:
            if isinstance(audio_path, str) and os.path.exists(audio_path):
                try:
                    os.remove(audio_path)
                    logger.debug(f"已删除临时音频文件: {audio_path}")
                except Exception as e_clean:
                    logger.error(f"删除临时音频文件失败 during cleanup, file: {audio_path}, error: {str(e_clean)}")
        return None

def get_search_group_invite_link(user_id):
    """为用户生成唯一的搜索群邀请链接"""
    from config import SEARCH_GROUP_LINK
    return f"{SEARCH_GROUP_LINK}?start=search_invite_{user_id}"


async def transcribe_voice(file_path):
    mp3_path = None
    try:
        # 检查文件是否存在且有效
        if not os.path.exists(file_path):
            logger.error(f"语音文件不存在: {file_path}")
            return None
        audio = AudioSegment.from_ogg(file_path)
        mp3_path = file_path.replace(".ogg", ".mp3")
        audio.export(mp3_path, format="mp3", bitrate="192k")

        with open(mp3_path, "rb") as audio_file:
            audio_data = audio_file.read()

        if not FISH_STT_CONFIG.get("enabled", False) or not fishaudio_stt_session:
            logger.warning("Fish Audio STT 未启用或会话无效，尝试 Whisper")
            if WHISPER_CONFIG.get("enabled", False) and openai_client:
                for attempt in range(3):
                    try:
                        start_time = time.time()
                        transcription = openai_client.audio.transcriptions.create(
                            model="whisper-1", file=open(mp3_path, "rb")
                        )
                        elapsed_time = time.time() - start_time
                        logger.info(f"Whisper 语音转文字成功，文件: {mp3_path}, 耗时: {elapsed_time:.2f} 秒")
                        return transcription.text if transcription else None
                    except Exception as e:
                        logger.error(f"Whisper 语音转文字失败，尝试 {attempt + 1}/3，文件: {mp3_path}, 错误: {str(e)}")
                        if attempt < 2:
                            await asyncio.sleep(2)  # 增加重试间隔
                        continue
                logger.error(f"Whisper 语音转文字最终失败，文件: {mp3_path}")
                return None
            logger.error("没有可用的 STT 服务")
            return None

        asr_request = ASRRequest(
            audio=audio_data,
            language=FISH_STT_CONFIG.get("default_language", "zh"), # Default to zh if not specified
            ignore_timestamps=True
        )

        for attempt in range(3):
            try:
                start_time = time.time()
                response = fishaudio_stt_session.asr(asr_request)
                elapsed_time = time.time() - start_time
                logger.info(f"Fish Audio 语音转文字成功，文件: {mp3_path}, 耗时: {elapsed_time:.2f} 秒")
                logger.debug(f"Fish Audio 响应: {vars(response) if response else None}")
                if response and hasattr(response, "text") and response.text:
                    return response.text
                logger.warning(f"无效的 Fish Audio 响应，文件: {mp3_path}")
                break # Invalid response, no need to retry Fish Audio
            except Exception as e:
                logger.error(f"Fish Audio 语音转文字失败，尝试 {attempt + 1}/3，文件: {mp3_path}, 错误: {str(e)}")
                if attempt < 2:
                    await asyncio.sleep(2)  # 增加重试间隔
                else:
                    logger.warning(f"Fish Audio 失败，回退到 Whisper，文件: {mp3_path}")
                    if WHISPER_CONFIG.get("enabled", False) and openai_client:
                        for whisper_attempt in range(3):
                            try:
                                start_time = time.time()
                                transcription = openai_client.audio.transcriptions.create(
                                    model="whisper-1", file=open(mp3_path, "rb")
                                )
                                elapsed_time = time.time() - start_time
                                logger.info(f"Whisper 语音转文字成功，文件: {mp3_path}, 耗时: {elapsed_time:.2f} 秒")
                                return transcription.text if transcription else None
                            except Exception as we:
                                logger.error(f"Whisper 语音转文字失败，尝试 {whisper_attempt + 1}/3，文件: {mp3_path}, 错误: {str(we)}")
                                if whisper_attempt < 2:
                                    await asyncio.sleep(2)
                                continue
                        logger.error(f"Whisper 语音转文字最终失败，文件: {mp3_path}")
                        return None
                    return None # No fallback or fallback failed
    except Exception as e:
        logger.error(f"语音转文字处理失败，文件: {file_path}, 错误: {str(e)}")
        return None
    finally:
        if mp3_path and os.path.exists(mp3_path):
            try:
                os.remove(mp3_path)
                logger.debug(f"已删除临时文件: {mp3_path}")
            except Exception as e:
                logger.error(f"删除临时文件失败，文件: {mp3_path}, 错误: {str(e)}")

def extract_3_key_frames(video_path: str, output_dir: str = None, user_id: str = None) -> list[dict]:
    """
    从视频中提取3个关键帧（开头10%、中间50%、结尾90%）
    
    Args:
        video_path: 视频文件路径
        output_dir: 输出目录，如果为None则不保存帧图片
        user_id: 用户ID，用于日志记录
    
    Returns:
        list[dict]: 包含帧信息的字典列表，每个字典包含：
            - position: 帧位置
            - timestamp: 时间戳（秒）
            - label: 帧标签（开头/中间/结尾）
            - frame: OpenCV帧数据（如果成功提取）
            - frame_path: 保存的帧路径（如果保存了）
    """
    try:
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            if user_id:
                logger = logging.getLogger(__name__)
                logger.error(f"用户 {user_id} 无法打开视频文件: {video_path}")
            return []
        
        try:
            # 获取视频信息
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            if total_frames <= 0:
                if user_id:
                    logger = logging.getLogger(__name__)
                    logger.error(f"用户 {user_id} 视频帧数无效: {total_frames}")
                return []
            
            # 计算3个关键帧的位置：开头（10%），中间（50%），结尾（90%）
            frame_positions = []
            frame_labels = ["开头", "中间", "结尾"]
            
            if total_frames == 1:
                # 只有一帧的情况
                frame_positions = [0, 0, 0]
            elif total_frames <= 3:
                # 少于等于3帧的情况
                frame_positions = list(range(total_frames))
                # 补齐到3个位置
                while len(frame_positions) < 3:
                    frame_positions.append(frame_positions[-1])
            else:
                # 正常情况：分别取10%、50%、90%的位置
                frame_positions = [
                    int(total_frames * 0.1),    # 开头部分 (10%)
                    int(total_frames * 0.5),    # 中间部分 (50%)
                    int(total_frames * 0.9)     # 结尾部分 (90%)
                ]
                # 确保位置不超出范围
                frame_positions = [min(pos, total_frames - 1) for pos in frame_positions]
            
            if user_id:
                logger = logging.getLogger(__name__)
                logger.info(f"用户 {user_id} 3关键帧提取计划: 总帧数={total_frames}, FPS={fps:.2f}, 关键帧位置={frame_positions}")
            
            # 提取3个关键帧
            frame_results = []
            for i, pos in enumerate(frame_positions):
                cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                ret, frame = cap.read()
                
                timestamp = pos / fps if fps > 0 else 0
                frame_info = {
                    'position': pos,
                    'timestamp': timestamp,
                    'label': frame_labels[i],
                    'frame': None,
                    'frame_path': None
                }
                
                if ret:
                    frame_info['frame'] = frame
                    
                    # 如果指定了输出目录，保存帧图片
                    if output_dir and user_id:
                        os.makedirs(output_dir, exist_ok=True)
                        frame_filename = f"key_frame_{user_id}_{i+1}_{frame_labels[i]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                        frame_path = os.path.join(output_dir, frame_filename)
                        cv2.imwrite(frame_path, frame)
                        frame_info['frame_path'] = frame_path
                    
                    if user_id:
                        logger = logging.getLogger(__name__)
                        logger.info(f"用户 {user_id} 成功提取{frame_labels[i]}关键帧: 位置={pos}, 时间={timestamp:.1f}s")
                else:
                    if user_id:
                        logger = logging.getLogger(__name__)
                        logger.warning(f"用户 {user_id} 无法提取{frame_labels[i]}关键帧: 位置={pos}")
                
                frame_results.append(frame_info)
            
            return frame_results
            
        finally:
            cap.release()
    except Exception as e:
        logger.error(f"关键帧提取失败，错误: {str(e)}", exc_info=True)
        return []

def analyze_video_key_frames_summary(frame_results: list[dict], user_id: str = None) -> dict:
    """
    分析3个关键帧的提取结果，生成摘要
    
    Args:
        frame_results: extract_3_key_frames函数返回的帧结果列表
        user_id: 用户ID，用于日志记录
    
    Returns:
        dict: 包含分析摘要的字典
    """
    valid_frames = [f for f in frame_results if f['frame'] is not None]
    failed_frames = [f for f in frame_results if f['frame'] is None]
    
    summary = {
        'total_frames_requested': len(frame_results),
        'valid_frames_count': len(valid_frames),
        'failed_frames_count': len(failed_frames),
        'success_rate': len(valid_frames) / len(frame_results) if frame_results else 0,
        'frame_details': [],
        'timestamps': [f['timestamp'] for f in valid_frames],
        'labels': [f['label'] for f in valid_frames]
    }
    
    for frame in frame_results:
        detail = {
            'label': frame['label'],
            'position': frame['position'],
            'timestamp': frame['timestamp'],
            'extracted': frame['frame'] is not None,
            'saved': frame['frame_path'] is not None
        }
        summary['frame_details'].append(detail)
    
    if user_id:
        logger = logging.getLogger(__name__)
        logger.info(f"用户 {user_id} 3关键帧提取摘要: 成功={len(valid_frames)}/3, 成功率={summary['success_rate']:.2%}")
    
    return summary

def demo_3_key_frames_extraction(video_path: str = None, user_id: str = "demo_user") -> dict:
    """
    演示3关键帧提取功能的使用方法
    
    Args:
        video_path: 视频文件路径，如果为None则生成演示数据
        user_id: 用户ID，用于演示
    
    Returns:
        dict: 包含演示结果的字典
    """
    try:
        if video_path and os.path.exists(video_path):
            # 实际提取关键帧
            logger.info(f"开始演示3关键帧提取: {video_path}")
            frame_results = extract_3_key_frames(video_path, user_id=user_id)
            summary = analyze_video_key_frames_summary(frame_results, user_id=user_id)
            
            demo_result = {
                'mode': 'real_extraction',
                'video_path': video_path,
                'frame_results': frame_results,
                'summary': summary,
                'optimization_features': [
                    '固定提取3个关键帧（开头10%、中间50%、结尾90%）',
                    '智能处理短视频和极短视频的情况',
                    '提供详细的时间戳和位置信息',
                    '包含完整的成功率统计',
                    '支持帧图片保存功能'
                ]
            }
            
        else:
            # 生成演示数据
            logger.info(f"生成3关键帧提取演示数据")
            demo_frame_results = [
                {
                    'position': 30,
                    'timestamp': 1.0,
                    'label': '开头',
                    'frame': 'mock_frame_data',
                    'frame_path': None
                },
                {
                    'position': 150,
                    'timestamp': 5.0,
                    'label': '中间',
                    'frame': 'mock_frame_data',
                    'frame_path': None
                },
                {
                    'position': 270,
                    'timestamp': 9.0,
                    'label': '结尾',
                    'frame': 'mock_frame_data',
                    'frame_path': None
                }
            ]
            
            demo_summary = {
                'total_frames_requested': 3,
                'valid_frames_count': 3,
                'failed_frames_count': 0,
                'success_rate': 1.0,
                'frame_details': [
                    {'label': '开头', 'position': 30, 'timestamp': 1.0, 'extracted': True, 'saved': False},
                    {'label': '中间', 'position': 150, 'timestamp': 5.0, 'extracted': True, 'saved': False},
                    {'label': '结尾', 'position': 270, 'timestamp': 9.0, 'extracted': True, 'saved': False}
                ],
                'timestamps': [1.0, 5.0, 9.0],
                'labels': ['开头', '中间', '结尾']
            }
            
            demo_result = {
                'mode': 'demo_data',
                'video_path': 'demo_video.mp4',
                'frame_results': demo_frame_results,
                'summary': demo_summary,
                'optimization_features': [
                    '固定提取3个关键帧（开头10%、中间50%、结尾90%）',
                    '智能处理短视频和极短视频的情况',
                    '提供详细的时间戳和位置信息',
                    '包含完整的成功率统计',
                    '支持帧图片保存功能'
                ]
            }
        
        # 打印演示结果
        logger.info("=" * 50)
        logger.info("🎬 3关键帧提取演示结果")
        logger.info("=" * 50)
        logger.info(f"模式: {demo_result['mode']}")
        logger.info(f"视频路径: {demo_result['video_path']}")
        logger.info(f"总体成功率: {demo_result['summary']['success_rate']:.2%}")
        logger.info(f"提取的关键帧: {demo_result['summary']['valid_frames_count']}/3")
        logger.info("关键帧详情:")
        
        for detail in demo_result['summary']['frame_details']:
            status = "✅成功" if detail['extracted'] else "❌失败"
            logger.info(f"  • {detail['label']}帧: 位置={detail['position']}, 时间={detail['timestamp']:.1f}s, 状态={status}")
        
        logger.info("优化特性:")
        for feature in demo_result['optimization_features']:
            logger.info(f"  ✨ {feature}")
        
        logger.info("=" * 50)
        
        return demo_result
        
    except Exception as e:
        logger.error(f"3关键帧提取演示失败: {str(e)}", exc_info=True)
        return {
            'mode': 'error',
            'error': str(e),
            'optimization_features': []
        }

async def safe_edit_message(query, text, reply_markup=None, parse_mode=None, logger=None):
    """
    安全地编辑消息，避免消息内容相同时的错误
    
    Args:
        query: CallbackQuery对象
        text: 新的消息文本
        reply_markup: 新的键盘标记
        parse_mode: 解析模式
        logger: 日志记录器
    
    Returns:
        bool: 编辑是否成功
    """
    try:
        # 检查当前消息内容是否与要设置的内容相同
        current_text = query.message.text if query.message.text else ""
        current_markup = query.message.reply_markup
        
        # 比较文本内容
        text_changed = current_text != text
        
        # 比较键盘（简单比较，可以根据需要优化）
        markup_changed = True
        if current_markup is None and reply_markup is None:
            markup_changed = False
        elif current_markup is not None and reply_markup is not None:
            # 简单比较键盘按钮数量
            try:
                current_buttons = len(current_markup.inline_keyboard) if current_markup.inline_keyboard else 0
                new_buttons = len(reply_markup.inline_keyboard) if reply_markup.inline_keyboard else 0
                markup_changed = current_buttons != new_buttons
            except:
                markup_changed = True
        
        # 如果内容和键盘都没有变化，直接返回成功
        if not text_changed and not markup_changed:
            if logger:
                logger.info("消息内容和键盘未变化，跳过编辑")
            try:
                await query.answer("操作完成")
            except:
                pass
            return True
        
        # 尝试编辑消息
        await query.edit_message_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        
        if logger:
            logger.info("消息编辑成功")
        return True
        
    except BadRequest as e:
        error_msg = str(e).lower()
        if "not modified" in error_msg or "message is not modified" in error_msg:
            # 消息内容未改变的错误，静默处理
            if logger:
                logger.info(f"消息内容未变化，跳过编辑: {str(e)}")
            try:
                await query.answer("操作完成")
            except:
                pass
            return True
        else:
            if logger:
                logger.error(f"编辑消息失败: {str(e)}")
            # 尝试发送新消息作为备用方案
            try:
                await query.message.reply_text(
                    text=text,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
                if logger:
                    logger.info("使用备用消息发送成功")
                return True
            except Exception as fallback_e:
                if logger:
                    logger.error(f"备用消息发送也失败: {str(fallback_e)}")
                return False
    except Exception as e:
        if logger:
            logger.error(f"编辑消息时发生未知错误: {str(e)}")
        return False

def stream_sentence_splitter(text_buffer: str) -> tuple[list[str], str]:
    """
    流式断句处理器，按照用户要求的逻辑进行断句
    返回: (完整的句子列表, 剩余的文本缓冲区)

    断句规则：
    1. 主要断句符号：。！？.!?
    2. 次要断句符号：，；,; (当累积文本较长时)
    3. 优化的连续标点处理：
       - 当遇到断句标点时，检查下一个字符
       - 如果下一个字符是。？！"）这五个标点，暂时不断句
       - 注意：这里的"是中文右侧引号，不是左侧引号"
       - 继续检查直到不是这些标点为止，然后进行断句
    4. 引号和括号位置优化：
       - 左引号、左括号应该在句子开头
       - 右引号、右括号应该在句子结尾
       - 断句时自动调整位置
    5. 保持程序原有的逻辑
    6. 过滤思维链和特殊标签
    """
    try:
        if not text_buffer.strip():
            return [], text_buffer
            
        sentences = []
        current_sentence = ""
        i = 0
        
        # 定义引号和括号
        left_quotes = ['"', '"', '『', '《', '【']
        right_quotes = ['"', '"', '』', '》', '】']
        left_brackets = ['(', '[', '{', '（', '［', '｛']
        right_brackets = [')', ']', '}', '）', '］', '｝']
        
        def adjust_quote_bracket_position(sentence: str, remaining_buffer: str) -> tuple[str, str]:
            """
            调整引号和括号的位置
            返回: (调整后的句子, 调整后的剩余缓冲区)
            """
            # 检查句子末尾是否有左引号或左括号
            while sentence and sentence[-1] in (left_quotes + left_brackets):
                # 将左引号/左括号移到下一句开头
                moved_char = sentence[-1]
                sentence = sentence[:-1].rstrip()
                remaining_buffer = moved_char + remaining_buffer
                logger.debug(f"移动左引号/括号'{moved_char}'到下一句开头")
            
            # 检查剩余缓冲区开头是否有右引号或右括号
            while remaining_buffer and remaining_buffer[0] in (right_quotes + right_brackets):
                # 将右引号/右括号移到当前句子结尾
                moved_char = remaining_buffer[0]
                sentence = sentence + moved_char
                remaining_buffer = remaining_buffer[1:].lstrip()
                logger.debug(f"移动右引号/括号'{moved_char}'到当前句子结尾")
            
            return sentence, remaining_buffer
            
        def filter_thinking_patterns(sentence: str) -> tuple[bool, str]:
            """
            过滤思维链和特殊标记
            返回: (是否保留句子, 过滤后的句子)
            """
            # 过滤思维链标签
            if re.search(r'<(think|thinking)>|</?(think|thinking)>', sentence, re.IGNORECASE):
                return False, sentence

            # 过滤指令标记
            if re.search(r'^--\s*consider:|^-->|^\s*--\s*>|^<!--', sentence):
                return False, sentence

            # 过滤角标和规则内容
            if re.search(r'[①②③④⑤]<[\w]+>内|<[\w]+规则>', sentence):
                return False, sentence

            # 过滤带编号的规则说明
            if re.search(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*[^：]*规则[：:]', sentence):
                return False, sentence

            if re.search(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*[^：]*格式[：:]', sentence):
                return False, sentence

            # 过滤"应用："开头的说明
            if re.search(r'^应用[：:]', sentence):
                return False, sentence

            # 过滤NPC互动规则相关内容
            if re.search(r'NPC互动规则|说话方式.*?直白.*?呛人', sentence):
                return False, sentence

            # 过滤语气和称呼相关的指令泄露
            if re.search(r'语气要保持.*?|使用.*?贱狗.*?废物.*?称呼', sentence):
                return False, sentence

            # 过滤格式输出相关的指令
            if re.search(r'输出的格式有[：:]|需要输出的格式', sentence):
                return False, sentence

            # 过滤纪录规则和句式规则
            if re.search(r'纪录规则|句式规则|思考结束后', sentence):
                return False, sentence

            # 过滤模型自我指称
            if re.search(r'我是.*?基于.*?(claude|AI助手)', sentence):
                return False, sentence

            # 移除任何剩余的XML标签
            filtered = re.sub(r'<[^>]*>', '', sentence)

            return True, filtered
        
        while i < len(text_buffer):
            char = text_buffer[i]
            current_sentence += char
            
            # 主要断句标点
            is_major_punct = char in ['。', '！', '？', '.', '!', '?']
            # 次要断句标点（当句子较长时使用）
            is_minor_punct = char in ['，', '；', ',', ';'] and len(current_sentence.strip()) > 15
            
            # 检查是否为断句标点
            if is_major_punct or is_minor_punct:
                # 检查是否有连续的特定标点符号（。？！"）），按照用户要求进行优化
                j = i + 1
                continuous_punct = False

                # 按照用户要求：如果断句的下一个字符仍然是。？！"）这五个标点，那么暂时不断句
                # 继续检查直到不是这些标点为止，然后进行断句
                # 注意：这里的"是中文右侧引号，不是左侧引号"
                while j < len(text_buffer):
                    next_char = text_buffer[j]
                    # 检查下一个字符是否是需要特殊处理的标点：。？！"）
                    # 使用中文右侧引号"，而不是左侧引号"或英文引号"
                    if next_char in ['。', '？', '！', '"', '）', '.', '?', '!', ')']:
                        current_sentence += next_char
                        continuous_punct = True
                        j += 1
                    else:
                        # 如果不是这些特定标点，停止检查
                        break

                if continuous_punct:
                    i = j - 1  # 更新索引到最后一个特定标点
                
                # 检查是否在引号或括号内（基于配对检查）
                quote_pairs = {'"': '"', '"': '"', '『': '』', '《': '》', '【': '】'}
                bracket_pairs = {'(': ')', '[': ']', '{': '}', '（': '）', '［': '］', '｛': '｝'}
                
                # 检查引号配对
                in_quotes = False
                for left_q, right_q in quote_pairs.items():
                    left_count = current_sentence.count(left_q)
                    right_count = current_sentence.count(right_q)
                    if left_count > right_count:  # 有未配对的左引号
                        in_quotes = True
                        break
                
                # 检查括号配对
                in_brackets = False
                for left_b, right_b in bracket_pairs.items():
                    left_count = current_sentence.count(left_b)
                    right_count = current_sentence.count(right_b)
                    if left_count > right_count:  # 有未配对的左括号
                        in_brackets = True
                        break
                
                # 如果在引号或括号内，不断句
                if in_quotes or in_brackets:
                    i += 1
                    continue
                
                # 对于次要标点，需要额外检查
                if is_minor_punct and not is_major_punct:
                    # 检查是否在数字、日期、时间等特殊格式中
                    if re.search(r'\d+[，,]\d+', current_sentence[-10:]):  # 数字格式
                        i += 1
                        continue
                    
                    # 检查下一个字符，如果紧跟着重要内容，不要断句
                    next_char_idx = i + 1
                    while next_char_idx < len(text_buffer) and text_buffer[next_char_idx].isspace():
                        next_char_idx += 1
                    
                    if next_char_idx < len(text_buffer):
                        next_char = text_buffer[next_char_idx]
                        # 如果下一个字符是数字、引号或特殊字符，可能不适合断句
                        if next_char.isdigit() or next_char in (left_quotes + left_brackets + ['《']):
                            i += 1
                            continue
                
                # 可以断句
                sentence = current_sentence.strip()
                # 添加引号和其他常见标点符号到过滤正则表达式中
                if sentence and not re.match(r'^[。！？.!?~@#$%^&*()，；,-_=+\s"""''`\[\]{}|\\/:;<>]+$', sentence):
                    # 检查句子长度，如果太短则不断句（优化TTS效果）
                    if len(sentence) <= 5 and i + 1 < len(text_buffer):
                        # 句子太短，继续累积，不断句
                        logger.debug(f"流式断句：句子太短({len(sentence)}字)，不断句: {sentence}")
                        # 不重置current_sentence，继续累积
                        i += 1
                        continue

                    # 获取剩余的文本缓冲区
                    remaining_buffer = text_buffer[i + 1:]

                    # 调整引号和括号的位置
                    sentence, remaining_buffer = adjust_quote_bracket_position(sentence, remaining_buffer)

                    # 过滤思维链和特殊标记
                    should_keep, filtered_sentence = filter_thinking_patterns(sentence)

                    # 更新text_buffer以反映调整后的剩余内容
                    text_buffer = text_buffer[:i + 1] + remaining_buffer

                    # 只有通过过滤的句子才添加到结果
                    if should_keep and filtered_sentence.strip():
                        sentences.append(filtered_sentence)
                        logger.debug(f"流式断句：'{filtered_sentence[:30]}...' (长度: {len(filtered_sentence)})")

                current_sentence = ""
            
            i += 1
        
        # 返回完整句子和剩余缓冲区
        return sentences, current_sentence
        
    except Exception as e:
        logger.error(f"流式断句处理失败，错误: {str(e)}", exc_info=True)
        return [], text_buffer


# 转发消息映射存储（内存中存储，重启后清空）
# 格式: {f"{original_chat_id}:{original_message_id}": forwarded_message_id}
forwarded_message_mapping = {}

async def store_forwarded_message_mapping(original_chat_id, original_message_id, forwarded_message_id):
    """存储原始消息ID与转发消息ID的映射关系"""
    try:
        key = f"{original_chat_id}:{original_message_id}"
        forwarded_message_mapping[key] = forwarded_message_id
        logger.debug(f"存储转发消息映射：{key} -> {forwarded_message_id}")

        # 清理过期的映射（保留最近1000条）
        if len(forwarded_message_mapping) > 1000:
            # 删除最旧的100条记录
            keys_to_remove = list(forwarded_message_mapping.keys())[:100]
            for key in keys_to_remove:
                del forwarded_message_mapping[key]
            logger.debug(f"清理了{len(keys_to_remove)}条过期的转发消息映射")

    except Exception as e:
        logger.error(f"存储转发消息映射失败: {str(e)}")

async def get_forwarded_message_id(original_chat_id, original_message_id):
    """根据原始消息ID获取转发消息ID"""
    try:
        key = f"{original_chat_id}:{original_message_id}"
        forwarded_message_id = forwarded_message_mapping.get(key)
        if forwarded_message_id:
            logger.debug(f"找到转发消息映射：{key} -> {forwarded_message_id}")
        else:
            logger.debug(f"未找到转发消息映射：{key}")
        return forwarded_message_id
    except Exception as e:
        logger.error(f"获取转发消息ID失败: {str(e)}")
        return None

