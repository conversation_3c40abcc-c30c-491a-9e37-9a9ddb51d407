#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漂流瓶数字检测功能演示脚本
展示如何防止用户通过漂流瓶交换联系方式
"""

import re
from datetime import datetime


def contains_numbers(text):
    """检测文本中是否包含数字（阿拉伯数字和中文数字）"""
    if not text:
        return False
    
    # 阿拉伯数字检测
    if re.search(r'\d', text):
        return True
    
    # 中文数字检测（包括大写和小写）
    chinese_numbers = [
        '零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
        '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖', '拾',
        '百', '千', '万', '亿', '佰', '仟', '萬', '億',
        '两', '兩', '廿', '卅'
    ]
    
    for num in chinese_numbers:
        if num in text:
            return True
    
    return False


def simulate_voice_check(user_id, transcribed_text, is_member=False):
    """模拟语音检查过程"""
    print(f"\n{'='*60}")
    print(f"用户ID: {user_id}")
    print(f"会员状态: {'会员' if is_member else '普通用户'}")
    print(f"语音转录内容: {transcribed_text}")
    print(f"{'='*60}")
    
    if is_member:
        print("✅ 会员用户，跳过内容检测，允许发送")
        return True
    
    has_numbers = contains_numbers(transcribed_text)
    
    if has_numbers:
        print("❌ 检测到数字内容，拒绝发送")
        print("💡 提示：为防止用户交换联系方式，非会员不能发送包含数字的语音")
        print("🎯 建议：升级为会员或重新录制不包含数字的语音")
        return False
    else:
        print("✅ 内容检查通过，允许发送")
        return True


def demo_scenarios():
    """演示各种场景"""
    print("🍾 漂流瓶数字检测功能演示")
    print("=" * 80)
    print("目的：防止用户通过漂流瓶交换联系方式（电话、微信、QQ等）")
    print("=" * 80)
    
    # 测试场景
    scenarios = [
        {
            "user_id": "user_001",
            "is_member": False,
            "voice_content": "你好，我想和你做朋友",
            "description": "普通用户发送正常内容"
        },
        {
            "user_id": "user_002", 
            "is_member": False,
            "voice_content": "加我微信13812345678",
            "description": "普通用户尝试发送电话号码"
        },
        {
            "user_id": "user_003",
            "is_member": False,
            "voice_content": "我的QQ号是一二三四五六七八九",
            "description": "普通用户用中文数字发送QQ号"
        },
        {
            "user_id": "user_004",
            "is_member": True,
            "voice_content": "我的电话是13812345678，加我微信",
            "description": "会员用户发送联系方式"
        },
        {
            "user_id": "user_005",
            "is_member": False,
            "voice_content": "我今年二十五岁，住在三环路",
            "description": "普通用户发送包含年龄和地址的内容"
        },
        {
            "user_id": "user_006",
            "is_member": False,
            "voice_content": "我喜欢听音乐和看电影",
            "description": "普通用户发送兴趣爱好"
        },
        {
            "user_id": "user_007",
            "is_member": False,
            "voice_content": "联系我：微信abc一二三，电话一八八八八八八八八八八",
            "description": "普通用户巧妙地用中文数字发送联系方式"
        },
        {
            "user_id": "user_008",
            "is_member": False,
            "voice_content": "我在北京工作，喜欢旅游",
            "description": "普通用户发送工作和爱好信息"
        }
    ]
    
    # 统计结果
    total_tests = len(scenarios)
    passed_tests = 0
    blocked_tests = 0
    member_bypassed = 0
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['description']}")
        
        result = simulate_voice_check(
            scenario['user_id'],
            scenario['voice_content'],
            scenario['is_member']
        )
        
        if scenario['is_member']:
            member_bypassed += 1
        elif result:
            passed_tests += 1
        else:
            blocked_tests += 1
    
    # 显示统计结果
    print(f"\n{'='*80}")
    print("📊 检测结果统计")
    print(f"{'='*80}")
    print(f"总测试场景: {total_tests}")
    print(f"✅ 通过检测: {passed_tests}")
    print(f"❌ 被阻止: {blocked_tests}")
    print(f"💎 会员绕过: {member_bypassed}")
    print(f"🛡️ 防护效率: {(blocked_tests / (total_tests - member_bypassed) * 100):.1f}%")
    
    print(f"\n{'='*80}")
    print("🎯 功能说明")
    print(f"{'='*80}")
    print("1. 普通用户发送的语音会被转录并检测数字内容")
    print("2. 包含阿拉伯数字或中文数字的语音会被拒绝")
    print("3. 会员用户不受此限制，可发送任意内容")
    print("4. 有效防止用户通过漂流瓶交换联系方式")
    print("5. 鼓励用户升级为会员以获得更多功能")


def demo_number_detection_details():
    """演示数字检测的详细规则"""
    print(f"\n{'='*80}")
    print("🔍 数字检测规则详解")
    print(f"{'='*80}")
    
    detection_rules = [
        {
            "category": "阿拉伯数字",
            "examples": ["0", "1", "123", "13812345678", "QQ123456"],
            "pattern": "正则表达式 \\d 匹配"
        },
        {
            "category": "中文小写数字",
            "examples": ["一", "二", "三", "十", "一十三八一二三四五六七八"],
            "pattern": "字符匹配：零一二三四五六七八九十"
        },
        {
            "category": "中文大写数字",
            "examples": ["壹", "贰", "叁", "拾", "壹叁捌壹贰叁肆伍陆柒捌"],
            "pattern": "字符匹配：壹贰叁肆伍陆柒捌玖拾"
        },
        {
            "category": "中文数量词",
            "examples": ["百", "千", "万", "亿", "两个", "廿年"],
            "pattern": "字符匹配：百千万亿佰仟萬億两兩廿卅"
        }
    ]
    
    for rule in detection_rules:
        print(f"\n📝 {rule['category']}")
        print(f"   规则: {rule['pattern']}")
        print(f"   示例: {', '.join(rule['examples'])}")
        
        for example in rule['examples']:
            detected = contains_numbers(example)
            status = "✅ 检测到" if detected else "❌ 未检测到"
            print(f"   测试 '{example}': {status}")


if __name__ == "__main__":
    demo_scenarios()
    demo_number_detection_details()
    
    print(f"\n{'='*80}")
    print("🎉 演示完成！")
    print("💡 这个功能有效防止了用户通过漂流瓶交换联系方式")
    print("🔒 保护了用户隐私，维护了平台的健康环境")
    print(f"{'='*80}")
