import asyncio
import time
from collections import defaultdict
from logging_config import logger

class AIProcessingStatusManager:
    """AI处理状态管理器，防止用户在AI回复过程中发送重复消息造成资源浪费"""
    
    def __init__(self):
        # 记录用户是否正在AI处理中
        self.processing_users = set()
        # 记录用户开始处理的时间，用于超时清理
        self.processing_start_time = {}
        # 记录被忽略的消息数量，用于统计
        self.ignored_message_count = defaultdict(int)
        # 用于保护状态操作的锁
        self.lock = asyncio.Lock()
        # 最大处理时间(秒)，超过此时间自动清理状态，防止死锁
        self.max_processing_time = 60  # 1分钟
    
    async def set_user_processing(self, user_id: str) -> bool:
        """
        设置用户为处理中状态
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: True表示成功设置，False表示用户已在处理中
        """
        async with self.lock:
            user_id = str(user_id)
            
            # 检查是否已经在处理中
            if user_id in self.processing_users:
                logger.debug(f"用户 {user_id} 已在AI处理中，拒绝新的处理请求")
                return False
            
            # 设置为处理中
            self.processing_users.add(user_id)
            self.processing_start_time[user_id] = time.time()
            logger.debug(f"用户 {user_id} 设置为AI处理中状态")
            return True
    
    async def clear_user_processing(self, user_id: str):
        """
        清除用户的处理中状态
        
        Args:
            user_id: 用户ID
        """
        async with self.lock:
            user_id = str(user_id)
            
            if user_id in self.processing_users:
                self.processing_users.remove(user_id)
                logger.debug(f"用户 {user_id} 清除AI处理中状态")
            
            if user_id in self.processing_start_time:
                processing_duration = time.time() - self.processing_start_time[user_id]
                del self.processing_start_time[user_id]
                logger.debug(f"用户 {user_id} AI处理耗时: {processing_duration:.2f}秒")
            
            # 清除忽略计数
            if user_id in self.ignored_message_count:
                ignored_count = self.ignored_message_count[user_id]
                if ignored_count > 0:
                    logger.info(f"用户 {user_id} 本次AI处理期间忽略了 {ignored_count} 条消息")
                del self.ignored_message_count[user_id]
    
    async def is_user_processing(self, user_id: str) -> bool:
        """
        检查用户是否正在AI处理中
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: True表示正在处理中
        """
        async with self.lock:
            user_id = str(user_id)
            return user_id in self.processing_users
    
    async def increment_ignored_count(self, user_id: str):
        """
        增加用户被忽略的消息计数
        
        Args:
            user_id: 用户ID
        """
        async with self.lock:
            user_id = str(user_id)
            self.ignored_message_count[user_id] += 1
            logger.debug(f"用户 {user_id} 忽略消息计数: {self.ignored_message_count[user_id]}")
    
    async def cleanup_expired_processing(self):
        """
        清理超时的处理状态，防止状态卡死
        """
        current_time = time.time()
        expired_users = []
        
        async with self.lock:
            for user_id, start_time in self.processing_start_time.items():
                if current_time - start_time > self.max_processing_time:
                    expired_users.append(user_id)
            
            for user_id in expired_users:
                logger.warning(f"用户 {user_id} AI处理超时({self.max_processing_time}秒)，强制清理状态")
                self.processing_users.discard(user_id)
                del self.processing_start_time[user_id]
                self.ignored_message_count.pop(user_id, None)
    
    async def get_processing_stats(self) -> dict:
        """
        获取当前处理状态统计信息
        
        Returns:
            dict: 包含当前处理用户数、被忽略消息总数等统计信息
        """
        async with self.lock:
            total_ignored = sum(self.ignored_message_count.values())
            return {
                "processing_users_count": len(self.processing_users),
                "processing_users": list(self.processing_users),
                "total_ignored_messages": total_ignored,
                "users_with_ignored_messages": dict(self.ignored_message_count)
            }

# 创建全局实例
ai_processing_status = AIProcessingStatusManager()

async def start_periodic_cleanup():
    """
    启动定期清理任务
    """
    while True:
        try:
            await ai_processing_status.cleanup_expired_processing()
            await asyncio.sleep(60)  # 每分钟检查一次
        except Exception as e:
            logger.error(f"AI处理状态定期清理失败: {str(e)}")
            await asyncio.sleep(60) 