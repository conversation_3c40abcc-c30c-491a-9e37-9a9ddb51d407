import os
import time
import numpy as np
import faiss
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from sentence_transformers import SentenceTransformer
from logging_config import logger
from config import DB_PATH, redis_client
import pickle
import hashlib
import threading
from enum import Enum

class MemoryType(Enum):
    CONVERSATION = "conversation"
    CHARACTER_SETTING = "character_setting"
    PLOT_EVENT = "plot_event"
    USER_PREFERENCE = "user_preference"
    RELATIONSHIP = "relationship"
    SCENE_CONTEXT = "scene_context"
    EMOTIONAL_STATE = "emotional_state"

@dataclass
class StructuredMemory:
    """结构化记忆对象"""
    id: str
    user_id: str
    content: str
    memory_type: MemoryType
    timestamp: str
    importance: float
    tags: List[str]
    related_entities: List[str]  # 相关实体（人物、物品、地点等）
    emotional_valence: float  # 情感价值 (-1到1)
    plot_stage: Optional[str] = None  # 剧情阶段
    context_window: Optional[int] = None  # 上下文窗口

class EnhancedVectorMemory:
    """增强版向量记忆系统 - 轻量级版本"""
    
    def __init__(self):
        """初始化增强向量记忆系统，采用轻量级方案"""
        try:
            self.dimension = 384
            
            # 初始化SentenceTransformer模型，强制使用CPU
            try:
                logger.info("正在加载SentenceTransformer模型...")
                self.model = SentenceTransformer('all-MiniLM-L6-v2', device='cpu')
                # 确保模型在CPU上
                if hasattr(self.model, 'to'):
                    self.model = self.model.to('cpu')
                logger.info("SentenceTransformer模型加载成功")
            except Exception as e:
                logger.error(f"SentenceTransformer模型加载失败: {str(e)}")
                # 创建一个虚拟模型作为fallback
                self.model = None
                
            # 设置文件路径
            self.index_path = os.path.join(os.path.dirname(DB_PATH), "enhanced_faiss_index")
            self.metadata_path = os.path.join(os.path.dirname(DB_PATH), "enhanced_metadata.json")
            self.structured_memory_path = os.path.join(os.path.dirname(DB_PATH), "structured_memories.json")
            
            # 轻量级内存存储 - 不再频繁保存到磁盘
            self.memory_vectors = []  # 内存中的向量
            self.memory_metadata = {}  # 内存中的元数据
            self.structured_memories: Dict[str, List[StructuredMemory]] = {}
            self.faiss_lock = threading.Lock()
            
            # 初始化Redis连接
            try:
                self.redis = redis_client
                logger.debug("Redis连接初始化成功")
            except Exception as e:
                logger.warning(f"Redis连接初始化失败: {str(e)}")
                self.redis = None
                
            self._commit_task = None
            self._last_save_time = 0
            self._save_interval = 300  # 5分钟保存一次到磁盘
            
            # 创建简单的内存索引
            self.index = faiss.IndexFlatL2(self.dimension)
            self.metadata = {}
            
            # 只在启动时加载一次
            try:
                self._load_from_disk()
                logger.info("记忆数据加载成功")
            except Exception as e:
                logger.warning(f"记忆数据加载失败: {str(e)}")
                
            logger.info("增强向量记忆系统初始化完成（轻量级模式）")
            
        except Exception as e:
            logger.error(f"增强向量记忆系统初始化失败: {str(e)}", exc_info=True)
            # 确保基本功能可用
            self.dimension = 384
            self.model = None
            self.memory_vectors = []
            self.memory_metadata = {}
            self.structured_memories = {}
            self.faiss_lock = threading.Lock()
            self.redis = None
            self._commit_task = None
            self.index = faiss.IndexFlatL2(self.dimension)
            self.metadata = {}
        
    def _load_from_disk(self):
        """从磁盘加载数据（仅启动时调用）"""
        # 加载FAISS索引
        if os.path.exists(self.index_path):
            self.index = faiss.read_index(self.index_path)
            logger.debug(f"加载FAISS索引，向量数量: {self.index.ntotal}")
        else:
            self.index = faiss.IndexFlatL2(self.dimension)
            
        # 加载元数据
        if os.path.exists(self.metadata_path):
            with open(self.metadata_path, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
                logger.debug(f"加载元数据，数量: {len(self.metadata)}")
        else:
            self.metadata = {}
            
        # 加载结构化记忆
        if os.path.exists(self.structured_memory_path):
            with open(self.structured_memory_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                for user_id, memories_data in data.items():
                    self.structured_memories[user_id] = []
                    for mem_data in memories_data:
                        # 将字符串形式的memory_type转换回MemoryType枚举
                        if isinstance(mem_data.get('memory_type'), str):
                            try:
                                mem_data['memory_type'] = MemoryType(mem_data['memory_type'])
                            except ValueError:
                                # 如果转换失败，使用默认值
                                logger.warning(f"无法识别的memory_type: {mem_data.get('memory_type')}")
                                mem_data['memory_type'] = MemoryType.CONVERSATION
                        self.structured_memories[user_id].append(StructuredMemory(**mem_data))
                logger.debug(f"加载结构化记忆，用户数: {len(self.structured_memories)}")
                
    def _save_to_disk_if_needed(self):
        """仅在需要时保存到磁盘"""
        current_time = time.time()
        if current_time - self._last_save_time < self._save_interval:
            return  # 未到保存时间
            
        if not self.memory_vectors:
            return  # 没有新数据需要保存
            
        try:
            # 将内存中的向量添加到索引
            if self.memory_vectors:
                vectors_array = np.array(self.memory_vectors, dtype=np.float32)
                self.index.add(vectors_array)
                
                # 更新元数据
                base_id = len(self.metadata)
                for i, metadata in enumerate(self.memory_metadata.values()):
                    self.metadata[str(base_id + i)] = metadata
                
                # 清理内存缓存
                self.memory_vectors.clear()
                self.memory_metadata.clear()
                
            # 保存到磁盘（异步执行，不阻塞）
            asyncio.create_task(self._async_save_to_disk())
            self._last_save_time = current_time
            
            logger.debug("记忆数据已标记为需要保存")
            
        except Exception as e:
            logger.error(f"保存记忆数据失败: {str(e)}")
            
    async def _async_save_to_disk(self):
        """异步保存到磁盘"""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._sync_save_to_disk)
            logger.debug("记忆数据异步保存完成")
        except Exception as e:
            logger.error(f"异步保存记忆数据失败: {str(e)}")
            
    def _sync_save_to_disk(self):
        """同步保存到磁盘"""
        try:
            with self.faiss_lock:
                # 保存FAISS索引
                faiss.write_index(self.index, self.index_path)
                
                # 保存元数据
                with open(self.metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(self.metadata, f, ensure_ascii=False, indent=2)
                
                # 保存结构化记忆
                data = {}
                for user_id, memories in self.structured_memories.items():
                    data[user_id] = []
                    for mem in memories:
                        mem_dict = asdict(mem)
                        # 将MemoryType枚举转换为字符串以便JSON序列化
                        mem_dict['memory_type'] = mem.memory_type.value
                        data[user_id].append(mem_dict)
                        
                with open(self.structured_memory_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            logger.error(f"同步保存记忆数据失败: {str(e)}")
            raise

    def calculate_enhanced_importance(self, content: str, memory_type: MemoryType, 
                                    emotional_valence: float, tags: List[str]) -> float:
        """增强的重要性计算"""
        base_score = min(len(content) / 200, 1.0)
        
        # 记忆类型权重
        type_weights = {
            MemoryType.CHARACTER_SETTING: 0.9,
            MemoryType.PLOT_EVENT: 0.8,
            MemoryType.USER_PREFERENCE: 0.7,
            MemoryType.RELATIONSHIP: 0.8,
            MemoryType.EMOTIONAL_STATE: 0.6,
            MemoryType.SCENE_CONTEXT: 0.5,
            MemoryType.CONVERSATION: 0.4
        }
        type_bonus = type_weights.get(memory_type, 0.3)
        
        # 情感强度加权
        emotion_bonus = abs(emotional_valence) * 0.3
        
        # 关键词加权
        important_keywords = [
            "喜欢", "讨厌", "重要", "记住", "偏好", "性格", "特征", 
            "关系", "感情", "秘密", "约定", "承诺", "目标", "梦想",
            "like", "hate", "important", "remember", "preference", 
            "character", "relationship", "secret", "promise", "goal"
        ]
        keyword_bonus = 0.2 * sum(1 for keyword in important_keywords 
                                 if keyword.lower() in content.lower())
        
        # 标签加权
        tag_bonus = min(len(tags) * 0.1, 0.3)
        
        final_score = min(base_score + type_bonus + emotion_bonus + keyword_bonus + tag_bonus, 1.0)
        return final_score

    async def add_structured_memory(self, user_id: str, content: str, 
                                  memory_type: MemoryType, tags: List[str] = None,
                                  related_entities: List[str] = None,
                                  emotional_valence: float = 0.0,
                                  plot_stage: str = None) -> str:
        """添加结构化记忆 - 轻量级版本"""
        try:
            # 输入验证
            if not user_id or not content or not content.strip():
                logger.warning(f"添加记忆失败：用户ID或内容为空")
                return ""
            
            if tags is None:
                tags = []
            if related_entities is None:
                related_entities = []
                
            # 限制内容长度
            content = content[:1000] if len(content) > 1000 else content
            
            memory_id = f"{user_id}_{int(time.time() * 1000000)}"
            timestamp = datetime.now().isoformat()
            
            importance = self.calculate_enhanced_importance(
                content, memory_type, emotional_valence, tags
            )
            
            structured_memory = StructuredMemory(
                id=memory_id,
                user_id=user_id,
                content=content,
                memory_type=memory_type,
                timestamp=timestamp,
                importance=importance,
                tags=tags,
                related_entities=related_entities,
                emotional_valence=emotional_valence,
                plot_stage=plot_stage
            )
            
            # 保存到结构化记忆
            if user_id not in self.structured_memories:
                self.structured_memories[user_id] = []
            self.structured_memories[user_id].append(structured_memory)
            
            # 异步编码并添加到内存向量（不阻塞）
            asyncio.create_task(self._add_vector_async(content, memory_id, user_id, memory_type, timestamp, importance, tags, emotional_valence))
            
            logger.info(f"添加结构化记忆 - 用户: {user_id}, 类型: {memory_type.value}, 重要性: {importance:.2f}")
            
            # 检查是否需要保存到磁盘
            self._save_to_disk_if_needed()
            
            return memory_id
                
        except Exception as e:
            logger.error(f"添加结构化记忆失败，用户: {user_id}, 错误: {str(e)}", exc_info=True)
            return ""
            
    async def _add_vector_async(self, content: str, memory_id: str, user_id: str, 
                               memory_type: MemoryType, timestamp: str, importance: float,
                               tags: List[str], emotional_valence: float):
        """异步添加向量到内存"""
        try:
            # 编码文本
            embedding = await self.encode_text(content)
            
            # 添加到内存向量
            vector_id = len(self.metadata) + len(self.memory_metadata)
            self.memory_vectors.append(embedding)
            self.memory_metadata[vector_id] = {
                "memory_id": memory_id,
                "user_id": user_id,
                "content": content,
                "type": memory_type.value,
                "timestamp": timestamp,
                "importance": importance,
                "tags": tags,
                "emotional_valence": emotional_valence
            }
            
            logger.debug(f"向量已添加到内存，用户: {user_id}")
            
        except Exception as e:
            logger.error(f"异步添加向量失败: {str(e)}")

    async def search_enhanced_memory(self, user_id: str, query: str, 
                                   memory_types: List[MemoryType] = None,
                                   min_importance: float = 0.0,
                                   time_range_days: int = 30,
                                   k: int = 5) -> List[Dict]:
        """增强的记忆搜索 - 轻量级版本"""
        try:
            # 输入验证
            if not query or not query.strip():
                logger.warning(f"用户 {user_id} 搜索查询为空")
                return []
            
            if k <= 0:
                logger.warning(f"用户 {user_id} 搜索数量无效: {k}")
                return []
            
            logger.debug(f"用户 {user_id} 开始记忆搜索: {query[:50]}...")
            
            # 快速编码查询
            try:
                query_embedding = await asyncio.wait_for(
                    self.encode_text(query), 
                    timeout=5.0  # 减少超时时间
                )
            except asyncio.TimeoutError:
                logger.error(f"用户 {user_id} 查询编码超时")
                return []
            
            # 将内存向量临时添加到索引进行搜索
            temp_index = None
            try:
                if self.memory_vectors:
                    # 创建临时索引包含内存向量
                    temp_index = faiss.IndexFlatL2(self.dimension)
                    
                    # 添加现有索引的向量
                    if self.index.ntotal > 0:
                        all_vectors = []
                        for i in range(self.index.ntotal):
                            all_vectors.append(self.index.reconstruct(i))
                        if all_vectors:
                            temp_index.add(np.array(all_vectors, dtype=np.float32))
                    
                    # 添加内存中的向量
                    temp_index.add(np.array(self.memory_vectors, dtype=np.float32))
                    search_index = temp_index
                    
                    # 合并元数据
                    combined_metadata = self.metadata.copy()
                    base_id = len(self.metadata)
                    for i, metadata in enumerate(self.memory_metadata.values()):
                        combined_metadata[str(base_id + i)] = metadata
                else:
                    search_index = self.index
                    combined_metadata = self.metadata
                
                # 检查索引是否为空
                if search_index.ntotal == 0:
                    logger.debug(f"用户 {user_id} 搜索索引为空")
                    return []
                    
                # 快速搜索
                search_k = min(k * 2, search_index.ntotal, 50)  # 减少搜索数量
                distances, indices = search_index.search(
                    np.array([query_embedding], dtype=np.float32), search_k
                )
                
                # 处理搜索结果
                current_time = datetime.now().timestamp()
                time_threshold = current_time - (time_range_days * 24 * 60 * 60)
                
                results = []
                seen_contents = set()
                
                for idx, dist in zip(indices[0], distances[0]):
                    if idx == -1 or idx >= search_index.ntotal:
                        continue
                        
                    try:
                        metadata = combined_metadata.get(str(idx), {})
                        if str(metadata.get("user_id", "")) != str(user_id):
                            continue
                            
                        content = metadata.get("content", "")
                        if not content or content in seen_contents:
                            continue
                            
                        importance = metadata.get("importance", 0)
                        if importance < min_importance:
                            continue
                            
                        # 时间过滤
                        try:
                            timestamp = metadata.get("timestamp", "")
                            if timestamp:
                                memory_time = datetime.fromisoformat(timestamp).timestamp()
                                if memory_time < time_threshold:
                                    continue
                            else:
                                memory_time = current_time
                        except (ValueError, TypeError):
                            memory_time = current_time
                            
                        # 类型过滤
                        if memory_types:
                            memory_type = metadata.get("type", "")
                            if not any(mt.value == memory_type for mt in memory_types):
                                continue
                                
                        # 计算综合得分
                        semantic_score = max(0, 1 - dist)
                        time_factor = max(0, 1 - (current_time - memory_time) / (time_range_days * 24 * 60 * 60))
                        
                        emotional_valence = metadata.get("emotional_valence", 0)
                        emotion_match = 1.0 - abs(emotional_valence) * 0.1
                        
                        final_score = (semantic_score * 0.5 + 
                                      time_factor * 0.2 + 
                                      importance * 0.2 + 
                                      emotion_match * 0.1)
                        
                        results.append({
                            "content": content,
                            "type": metadata.get("type"),
                            "timestamp": metadata.get("timestamp"),
                            "importance": importance,
                            "tags": metadata.get("tags", []),
                            "emotional_valence": emotional_valence,
                            "distance": dist,
                            "score": final_score
                        })
                        
                        seen_contents.add(content)
                        
                    except Exception as e:
                        logger.warning(f"处理搜索结果时出错: {str(e)}")
                        continue
                
                # 按综合得分排序
                results.sort(key=lambda x: x["score"], reverse=True)
                final_results = results[:k]
                
                logger.debug(f"用户 {user_id} 记忆搜索完成，找到 {len(final_results)} 条结果")
                return final_results
                
            finally:
                # 清理临时索引
                if temp_index is not None:
                    del temp_index
                
        except Exception as e:
            logger.error(f"用户 {user_id} 记忆搜索失败: {str(e)}", exc_info=True)
            return []

    async def get_memory_summary(self, user_id: str, memory_types: List[MemoryType] = None) -> Dict:
        """获取记忆摘要"""
        user_memories = self.structured_memories.get(user_id, [])
        
        if memory_types:
            user_memories = [m for m in user_memories if m.memory_type in memory_types]
            
        # 按类型分组
        type_summary = {}
        for memory in user_memories:
            type_name = memory.memory_type.value
            if type_name not in type_summary:
                type_summary[type_name] = {
                    "count": 0,
                    "avg_importance": 0.0,
                    "recent_memories": []
                }
            
            type_summary[type_name]["count"] += 1
            type_summary[type_name]["avg_importance"] += memory.importance
            type_summary[type_name]["recent_memories"].append({
                "content": memory.content[:100],
                "timestamp": memory.timestamp,
                "importance": memory.importance
            })
            
        # 计算平均重要性
        for type_name in type_summary:
            if type_summary[type_name]["count"] > 0:
                type_summary[type_name]["avg_importance"] /= type_summary[type_name]["count"]
            # 只保留最近5条
            type_summary[type_name]["recent_memories"] = sorted(
                type_summary[type_name]["recent_memories"],
                key=lambda x: x["timestamp"],
                reverse=True
            )[:5]
            
        return {
            "total_memories": len(user_memories),
            "type_summary": type_summary,
            "most_important": sorted(user_memories, key=lambda x: x.importance, reverse=True)[:3]
        }

    async def encode_text(self, text: str) -> np.ndarray:
        """异步编码文本 - 优化版本"""
        if not text or not text.strip():
            return np.zeros(self.dimension, dtype=np.float32)
            
        # 限制文本长度
        text = text[:300] if len(text) > 300 else text
        
        cache_key = f"enhanced_embedding:{hashlib.md5(text.encode()).hexdigest()}"
        
        # 快速缓存检查
        if self.redis is not None:
            try:
                cached = await asyncio.wait_for(self.redis.get(cache_key), timeout=1.0)
                if cached:
                    logger.debug(f"从缓存获取文本编码: {text[:30]}...")
                    return pickle.loads(cached)
            except:
                pass  # 忽略缓存错误
            
        # 快速编码
        try:
            loop = asyncio.get_event_loop()
            embedding = await asyncio.wait_for(
                loop.run_in_executor(None, self._encode_text_sync, text),
                timeout=3.0  # 减少超时时间
            )
            
            # 异步缓存（不等待）
            if self.redis is not None:
                asyncio.create_task(self._cache_embedding_async(cache_key, embedding))
            
            return embedding
            
        except asyncio.TimeoutError:
            logger.warning(f"文本编码超时: {text[:30]}...")
            return np.random.normal(0, 0.1, self.dimension).astype(np.float32)
        except Exception as e:
            logger.warning(f"文本编码失败: {text[:30]}..., 错误: {str(e)}")
            return np.random.normal(0, 0.1, self.dimension).astype(np.float32)
    
    def _encode_text_sync(self, text: str) -> np.ndarray:
        """同步编码文本"""
        try:
            if self.model is None:
                return np.random.normal(0, 0.1, self.dimension).astype(np.float32)
            
            # 编码文本
            embedding = self.model.encode([text])[0]
            return embedding.astype(np.float32)
            
        except Exception as e:
            logger.warning(f"同步文本编码失败: {str(e)}")
            return np.random.normal(0, 0.1, self.dimension).astype(np.float32)
    
    async def _cache_embedding_async(self, cache_key: str, embedding: np.ndarray):
        """异步缓存嵌入向量"""
        if self.redis is None:
            return
            
        try:
            await asyncio.wait_for(
                self.redis.setex(cache_key, 3600, pickle.dumps(embedding)),
                timeout=1.0
            )
        except:
            pass  # 忽略缓存错误

    async def commit(self):
        """提交操作 - 轻量级版本"""
        try:
            # 强制保存到磁盘
            self._last_save_time = 0  # 重置时间，强制保存
            self._save_to_disk_if_needed()
            logger.debug("记忆系统提交完成")
        except Exception as e:
            logger.error(f"记忆系统提交失败: {str(e)}")

    async def create_character_profile(self, user_id: str, character_data: Dict) -> str:
        """创建角色档案"""
        profile_content = f"""
角色档案：
姓名：{character_data.get('name', '未知')}
性格：{character_data.get('personality', '未设定')}
外貌：{character_data.get('appearance', '未描述')}
背景：{character_data.get('background', '未知')}
关系：{character_data.get('relationship', '未定义')}
特殊设定：{character_data.get('special_settings', '无')}
        """.strip()
        
        memory_id = await self.add_structured_memory(
            user_id=user_id,
            content=profile_content,
            memory_type=MemoryType.CHARACTER_SETTING,
            tags=["角色档案", "设定", character_data.get('name', '角色')],
            related_entities=[character_data.get('name', '角色')],
            emotional_valence=0.0
        )
        
        return memory_id

    async def start_auto_commit(self):
        """启动自动保存任务"""
        if self._commit_task is None:
            self._commit_task = asyncio.create_task(self._auto_commit())
            logger.info("增强记忆系统自动保存任务已启动")

    async def _auto_commit(self):
        """定期自动保存向量索引"""
        while True:
            await asyncio.sleep(300)  # 每5分钟检查一次
            try:
                self._save_to_disk_if_needed()
            except Exception as e:
                logger.error(f"自动保存失败: {str(e)}")

    async def get_related_memories(self, user_id: str, query: str, limit: int = 5) -> List[Dict]:
        """
        获取相关记忆 - search_enhanced_memory的简化版本
        为了保持向后兼容性而添加的方法
        
        Args:
            user_id: 用户ID
            query: 查询文本
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 相关记忆列表
        """
        try:
            results = await self.search_enhanced_memory(
                user_id=user_id,
                query=query,
                memory_types=None,  # 搜索所有类型
                min_importance=0.0,  # 不限制重要性
                time_range_days=30,  # 搜索最近30天
                k=limit
            )
            
            # 转换为简化格式以保持兼容性
            simplified_results = []
            for result in results:
                simplified_results.append({
                    'content': result.get('content', ''),
                    'timestamp': result.get('timestamp', ''),
                    'importance': result.get('importance', 0.0),
                    'score': result.get('score', 0.0)
                })
            
            return simplified_results
            
        except Exception as e:
            logger.error(f"用户 {user_id} 获取相关记忆失败: {str(e)}")
            return []

# 全局实例
enhanced_memory = EnhancedVectorMemory() 