import asyncio
import random
from datetime import datetime
from typing import List, Dict, Optional
from enhanced_memory import enhanced_memory, MemoryType
from logging_config import logger
from utils import process_ai_response

class EnhancedAIDialogue:
    """增强的AI对话处理器，集成结构化记忆系统"""
    
    def __init__(self):
        self.memory = enhanced_memory
        
    async def build_enhanced_system_prompt(self, user_id: str, message: str, 
                                         user_data: dict, lang: str = "zh-cn") -> str:
        """构建增强的系统提示词"""
        
        # 1. 获取角色设定记忆
        character_memories = await self.memory.search_enhanced_memory(
            user_id=user_id,
            query=message,
            memory_types=[MemoryType.CHARACTER_SETTING, MemoryType.USER_PREFERENCE],
            k=3
        )
        
        # 2. 获取相关剧情记忆  
        plot_memories = await self.memory.search_enhanced_memory(
            user_id=user_id,
            query=message,
            memory_types=[MemoryType.PLOT_EVENT, MemoryType.CONVERSATION],
            time_range_days=7,  # 只查看最近一周的剧情
            k=4
        )
        
        # 3. 获取情感和关系记忆
        emotional_memories = await self.memory.search_enhanced_memory(
            user_id=user_id,
            query=message,
            memory_types=[MemoryType.EMOTIONAL_STATE, MemoryType.RELATIONSHIP],
            min_importance=0.6,  # 只获取重要的情感记忆
            k=2
        )
        
        # 4. 构建记忆上下文
        character_context = self._build_memory_context(character_memories, "角色设定")
        plot_context = self._build_memory_context(plot_memories, "剧情记忆")
        emotional_context = self._build_memory_context(emotional_memories, "情感状态")
        
        # 5. 获取记忆摘要以了解整体背景
        memory_summary = await self.memory.get_memory_summary(user_id)
        
        # 6. 构建系统提示词
        system_prompt = f"""
你是一个专业的角色扮演AI，具有完整的记忆能力。请基于以下信息进行回复：

## 记忆背景摘要
- 总记忆条数：{memory_summary.get('total_memories', 0)}
- 最重要的记忆：{self._format_important_memories(memory_summary.get('most_important', []))}

## 具体记忆内容

### {character_context['title']}
{character_context['content']}

### {plot_context['title']}  
{plot_context['content']}

### {emotional_context['title']}
{emotional_context['content']}

## 对话指导原则
1. **保持一致性**：严格遵循已建立的角色设定和剧情发展
2. **情感连贯**：考虑历史情感状态，让角色的情感反应符合逻辑
3. **记忆关联**：主动引用相关的历史事件和对话内容
4. **剧情推进**：在保持连贯性的基础上推进故事发展
5. **细节丰富**：利用记忆中的细节让对话更生动真实

## 当前对话
用户刚刚说：{message}

请基于上述记忆信息，以角色的身份进行回复。记住要体现出对历史对话的记忆和连续性。
"""
        
        return system_prompt
    
    def _build_memory_context(self, memories: List[Dict], title: str) -> Dict[str, str]:
        """构建记忆上下文"""
        if not memories:
            return {
                "title": title,
                "content": "暂无相关记忆"
            }
            
        content_parts = []
        for i, memory in enumerate(memories, 1):
            importance_stars = "⭐" * min(int(memory['importance'] * 5), 5)
            content_parts.append(
                f"{i}. {importance_stars} {memory['content'][:200]}..."
                f" (重要性: {memory['importance']:.2f}, 时间: {memory['timestamp'][:10]})"
            )
            
        return {
            "title": title,
            "content": "\n".join(content_parts)
        }
    
    def _format_important_memories(self, important_memories: List) -> str:
        """格式化重要记忆"""
        if not important_memories:
            return "无"
            
        formatted = []
        for mem in important_memories[:3]:  # 只显示前3个最重要的
            formatted.append(f"「{mem.content[:50]}...」(重要性: {mem.importance:.2f})")
            
        return " | ".join(formatted)
    
    async def process_user_message(self, user_id: str, message: str, user_data: dict) -> str:
        """处理用户消息并添加到记忆系统"""
        
        # 1. 分析消息类型和重要性
        message_analysis = await self._analyze_message(message)
        
        # 2. 添加用户消息到记忆
        await self.memory.add_structured_memory(
            user_id=user_id,
            content=message,
            memory_type=message_analysis['memory_type'],
            tags=message_analysis['tags'],
            related_entities=message_analysis['entities'],
            emotional_valence=message_analysis['emotional_valence']
        )
        
        return message_analysis
    
    async def process_ai_response(self, user_id: str, response: str, 
                                message_analysis: dict) -> None:
        """处理AI回复并添加到记忆系统"""
        
        # 分析AI回复
        response_analysis = await self._analyze_message(response, is_ai_response=True)
        
        # 添加AI回复到记忆
        await self.memory.add_structured_memory(
            user_id=user_id,
            content=response,
            memory_type=MemoryType.CONVERSATION,
            tags=response_analysis['tags'] + ["AI回复"],
            related_entities=response_analysis['entities'],
            emotional_valence=response_analysis['emotional_valence']
        )
        
        # 如果检测到重要的剧情事件，单独存储
        if response_analysis.get('is_plot_event'):
            await self.memory.add_structured_memory(
                user_id=user_id,
                content=f"剧情事件：{response[:200]}",
                memory_type=MemoryType.PLOT_EVENT,
                tags=response_analysis['tags'] + ["剧情", "事件"],
                related_entities=response_analysis['entities'],
                emotional_valence=response_analysis['emotional_valence']
            )
    
    async def _analyze_message(self, message: str, is_ai_response: bool = False) -> dict:
        """分析消息内容，确定类型、实体、情感等"""
        
        # 简单的关键词分析（实际项目中可以使用更复杂的NLP分析）
        message_lower = message.lower()
        
        # 确定记忆类型
        if any(keyword in message_lower for keyword in ["我喜欢", "我讨厌", "我想要", "偏好"]):
            memory_type = MemoryType.USER_PREFERENCE
        elif any(keyword in message_lower for keyword in ["角色", "性格", "外貌", "设定"]):
            memory_type = MemoryType.CHARACTER_SETTING
        elif any(keyword in message_lower for keyword in ["情感", "心情", "感觉", "开心", "难过", "愤怒"]):
            memory_type = MemoryType.EMOTIONAL_STATE
        elif any(keyword in message_lower for keyword in ["关系", "朋友", "恋人", "敌人"]):
            memory_type = MemoryType.RELATIONSHIP
        elif any(keyword in message_lower for keyword in ["发生", "事件", "故事", "剧情"]):
            memory_type = MemoryType.PLOT_EVENT
        else:
            memory_type = MemoryType.CONVERSATION
            
        # 提取标签（简化版）
        tags = []
        tag_keywords = {
            "情感": ["开心", "难过", "愤怒", "兴奋", "紧张", "relaxed", "happy", "sad", "angry"],
            "行动": ["走", "跑", "看", "听", "说", "做", "go", "run", "look", "listen"],
            "描述": ["漂亮", "美丽", "丑陋", "高", "矮", "胖", "瘦", "beautiful", "ugly"],
            "对话": ["说", "告诉", "问", "回答", "聊天", "say", "tell", "ask", "answer"]
        }
        
        for tag, keywords in tag_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                tags.append(tag)
                
        # 简单的实体提取（实际可以使用NER）
        entities = []
        # 这里可以加入更复杂的实体识别逻辑
        
        # 情感倾向分析（简化版）
        positive_words = ["喜欢", "开心", "高兴", "满意", "好", "棒", "excellent", "good", "happy", "love"]
        negative_words = ["讨厌", "难过", "愤怒", "不满", "坏", "糟糕", "bad", "hate", "sad", "angry"]  
        
        positive_count = sum(1 for word in positive_words if word in message_lower)
        negative_count = sum(1 for word in negative_words if word in message_lower)
        
        if positive_count > negative_count:
            emotional_valence = min(0.8, positive_count * 0.3)
        elif negative_count > positive_count:
            emotional_valence = max(-0.8, -negative_count * 0.3)
        else:
            emotional_valence = 0.0
            
        # 检测是否为重要剧情事件
        plot_keywords = ["发生了", "突然", "意外", "重要", "决定", "选择", "结果"]
        is_plot_event = any(keyword in message_lower for keyword in plot_keywords)
        
        return {
            'memory_type': memory_type,
            'tags': tags,
            'entities': entities,
            'emotional_valence': emotional_valence,
            'is_plot_event': is_plot_event
        }
    
    async def create_character_from_conversation(self, user_id: str, 
                                               conversation_history: List[dict]) -> str:
        """基于对话历史自动创建角色档案"""
        
        # 分析对话历史中的角色信息
        character_info = {
            'name': '角色',
            'personality': [],
            'appearance': [],
            'background': [],
            'relationship': '未定义',
            'special_settings': []
        }
        
        for msg in conversation_history[-20:]:  # 分析最近20条消息
            content = msg.get('content', '').lower()
            
            # 提取性格信息
            personality_keywords = {
                "温柔": ["温柔", "温和", "柔软", "gentle", "soft"],
                "强势": ["强势", "霸道", "支配", "dominant", "strong"],
                "活泼": ["活泼", "开朗", "快乐", "cheerful", "lively"],
                "冷漠": ["冷漠", "冷淡", "疏远", "cold", "distant"]
            }
            
            for trait, keywords in personality_keywords.items():
                if any(keyword in content for keyword in keywords):
                    if trait not in character_info['personality']:
                        character_info['personality'].append(trait)
        
        # 创建角色档案
        character_info['personality'] = ', '.join(character_info['personality']) or '未设定'
        
        memory_id = await self.memory.create_character_profile(user_id, character_info)
        
        logger.info(f"为用户 {user_id} 自动创建角色档案: {memory_id}")
        return memory_id
    
    async def get_conversation_context(self, user_id: str, current_message: str) -> str:
        """获取对话上下文摘要"""
        
        # 搜索相关记忆
        relevant_memories = await self.memory.search_enhanced_memory(
            user_id=user_id,
            query=current_message,  
            memory_types=[MemoryType.CONVERSATION, MemoryType.PLOT_EVENT],
            time_range_days=3,  # 最近3天
            k=5
        )
        
        if not relevant_memories:
            return "这是你们的第一次对话。"
            
        # 构建上下文摘要
        context_parts = []
        for memory in relevant_memories:
            time_desc = self._get_time_description(memory['timestamp'])
            context_parts.append(f"{time_desc}：{memory['content'][:100]}")
            
        return "对话背景：\n" + "\n".join(context_parts)
    
    def _get_time_description(self, timestamp: str) -> str:
        """获取时间描述"""
        try:
            memory_time = datetime.fromisoformat(timestamp)
            current_time = datetime.now()
            time_diff = current_time - memory_time
            
            if time_diff.days > 0:
                return f"{time_diff.days}天前"
            elif time_diff.seconds > 3600:
                return f"{time_diff.seconds // 3600}小时前"
            elif time_diff.seconds > 60:
                return f"{time_diff.seconds // 60}分钟前"
            else:
                return "刚才"
        except:
            return "之前"

# 全局实例
enhanced_ai_dialogue = EnhancedAIDialogue() 