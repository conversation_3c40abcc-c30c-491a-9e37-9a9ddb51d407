import aiosqlite
import json
import os
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from logging_config import logger
from config import DB_PATH, TTS_CONFIG, BACKUP_DIR, DEFAULT_VOICE
from handlers.default_settings import validate_and_complete_user_data

# 默认备份目录（如果 config.py 未定义 BACKUP_DIR）
DEFAULT_BACKUP_DIR = "/botfather/ceshi/backups"


async def _check_and_add_missing_columns(conn):
    """检查并添加缺失的数据库字段"""
    try:
        # 检查 drift_bottles 表的字段
        cursor = await conn.execute("PRAGMA table_info(drift_bottles)")
        columns = await cursor.fetchall()
        column_names = [col[1] for col in columns]

        if 'last_delivered_at' not in column_names:
            logger.info("添加 last_delivered_at 字段到 drift_bottles 表")
            await conn.execute('ALTER TABLE drift_bottles ADD COLUMN last_delivered_at TEXT')
            await conn.commit()
            logger.info("成功添加 last_delivered_at 字段")

        # 检查 user_bottle_daily 表的字段
        cursor = await conn.execute("PRAGMA table_info(user_bottle_daily)")
        columns = await cursor.fetchall()
        column_names = [col[1] for col in columns]

        if 'bottles_picked' not in column_names:
            logger.info("添加 bottles_picked 字段到 user_bottle_daily 表")
            await conn.execute('ALTER TABLE user_bottle_daily ADD COLUMN bottles_picked INTEGER DEFAULT 0')
            await conn.commit()
            logger.info("成功添加 bottles_picked 字段")

        # 更新现有已投递瓶子的 last_delivered_at 字段
        cursor = await conn.execute("""
            SELECT id, delivery_history FROM drift_bottles
            WHERE state = 'delivered' AND last_delivered_at IS NULL
        """)
        bottles_to_update = await cursor.fetchall()

        updated_count = 0
        for bottle_id, delivery_history_json in bottles_to_update:
            try:
                import json
                delivery_history = json.loads(delivery_history_json) if delivery_history_json else []
                if delivery_history:
                    # 获取最后一次投递时间
                    last_delivery = delivery_history[-1]
                    delivered_at = last_delivery.get('delivered_at')
                    if delivered_at:
                        await conn.execute("""
                            UPDATE drift_bottles
                            SET last_delivered_at = ?
                            WHERE id = ?
                        """, (delivered_at, bottle_id))
                        updated_count += 1
            except Exception as e:
                logger.error(f"更新漂流瓶 {bottle_id} 的 last_delivered_at 失败: {str(e)}")

        if updated_count > 0:
            await conn.commit()
            logger.info(f"成功更新 {updated_count} 个漂流瓶的 last_delivered_at 字段")

    except Exception as e:
        logger.error(f"检查和添加缺失字段失败: {str(e)}")
        # 不抛出异常，让程序继续运行

def is_json_object_string(value):
    """检查是否为JSON对象字符串，但排除ISO时间格式"""
    if not isinstance(value, str) or not value.strip():
        return False
    
    # 明确排除ISO时间格式
    try:
        # 尝试解析为datetime，如果成功则不是JSON对象
        datetime.fromisoformat(value)
        return False
    except ValueError:
        pass
    
    # 检查是否为JSON对象字符串
    if value.strip() in ["{}", "[]"]:
        return True
        
    if value.strip().startswith("{") and value.strip().endswith("}"):
        try:
            parsed = json.loads(value)
            return isinstance(parsed, dict)
        except json.JSONDecodeError:
            return False
    
    return False

def validate_membership_expiry(value):
    """验证membership_expiry字段的值"""
    if value is None:
        return None, True
    
    if not isinstance(value, str):
        logger.warning(f"membership_expiry字段类型错误: {type(value)}, 值: {repr(value)}")
        return None, False
    
    # 特殊处理：如果是空的JSON对象字符串，直接返回无效
    if value in ["{}", "[]"]:
        logger.warning(f"membership_expiry字段为空的JSON对象: {repr(value)}")
        return None, False
    
    # 检查是否为有效的ISO时间格式
    try:
        datetime.fromisoformat(value)
        return value, True
    except ValueError:
        # 检查是否为错误的JSON对象
        if is_json_object_string(value):
            logger.warning(f"membership_expiry字段为JSON对象格式: {repr(value)}")
            return None, False
        # 其他无效格式
        logger.warning(f"membership_expiry字段格式无效: {repr(value)}")
        return None, False

def safe_set_membership_expiry(user_data, expiry_datetime):
    """安全设置会员到期时间"""
    if expiry_datetime is None:
        user_data["membership_expiry"] = None
    elif isinstance(expiry_datetime, datetime):
        user_data["membership_expiry"] = expiry_datetime.isoformat()
    elif isinstance(expiry_datetime, str):
        try:
            # 验证是否为有效的ISO格式
            datetime.fromisoformat(expiry_datetime)
            user_data["membership_expiry"] = expiry_datetime
        except ValueError:
            logger.warning(f"无效的membership_expiry值: {expiry_datetime}，重置为None")
            user_data["membership_expiry"] = None
    else:
        logger.warning(f"不支持的membership_expiry类型: {type(expiry_datetime)}，重置为None")
        user_data["membership_expiry"] = None

async def backup_database():
    """备份数据库到指定目录以防止数据丢失"""
    backup_dir = BACKUP_DIR if 'BACKUP_DIR' in globals() else DEFAULT_BACKUP_DIR
    os.makedirs(backup_dir, exist_ok=True)
    backup_file = os.path.join(backup_dir, f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
    async with aiosqlite.connect(DB_PATH) as src_conn:
        async with aiosqlite.connect(backup_file) as dst_conn:
            try:
                await src_conn.backup(dst_conn)
                logger.info(f"数据库备份成功，文件: {backup_file}")
                return backup_file
            except Exception as e:
                logger.error(f"数据库备份失败: {str(e)}", exc_info=True)
                raise

async def migrate_from_backup_if_needed(backup_file):
    """如果有新增数据，从备份文件迁移数据到当前数据库"""
    if not os.path.exists(backup_file):
        logger.warning(f"备份文件 {backup_file} 不存在，跳过迁移")
        return

    logger.info(f"开始从备份文件 {backup_file} 迁移数据到当前数据库 {DB_PATH}")
    try:
        async with aiosqlite.connect(backup_file) as backup_conn:
            async with aiosqlite.connect(DB_PATH) as current_conn:
                # 获取备份数据库的用户数据
                backup_cursor = await backup_conn.execute("SELECT user_id, conversation FROM users")
                backup_data = await backup_cursor.fetchall()
                
                for user_id, conversation in backup_data:
                    # 检查当前数据库中是否存在该用户
                    current_cursor = await current_conn.execute("SELECT conversation FROM users WHERE user_id = ?", (user_id,))
                    current_row = await current_cursor.fetchone()
                    
                    if current_row:
                        current_conversation = json.loads(current_row[0]) if current_row[0] else []
                        backup_conversation = json.loads(conversation) if conversation else []
                        if len(backup_conversation) > len(current_conversation):
                            await current_conn.execute(
                                "UPDATE users SET conversation = ? WHERE user_id = ?",
                                (conversation, user_id)
                            )
                            logger.info(f"用户 {user_id} 的数据已从备份更新到当前数据库")
                    else:
                        await current_conn.execute(
                            "INSERT INTO users (user_id, conversation) VALUES (?, ?)",
                            (user_id, conversation)
                        )
                        logger.info(f"用户 {user_id} 的数据已从备份添加到当前数据库")
                
                await current_conn.commit()
                logger.info(f"从备份文件 {backup_file} 迁移数据完成")
    except Exception as e:
        logger.error(f"从备份文件迁移数据失败: {str(e)}", exc_info=True)
        raise

async def validate_and_fix_json_field(conn, user_id, field_name, value, default_value):
    """验证并修复无效的 JSON 字段"""
    try:
        if value is None or isinstance(value, (str, bytes, bytearray)):
            json.loads(value) if value else default_value
        else:
            # 无效值，修复为默认 JSON 字符串
            logger.warning(f"用户 {user_id} 的 {field_name} 字段无效: {value}，修复为 {default_value}")
            await conn.execute(
                f'UPDATE users SET {field_name} = ? WHERE user_id = ?',
                (json.dumps(default_value), user_id)
            )
            return False
        return True
    except (TypeError, json.JSONDecodeError):
        logger.warning(f"用户 {user_id} 的 {field_name} 字段无法解析为 JSON: {value}，修复为 {default_value}")
        await conn.execute(
            f'UPDATE users SET {field_name} = ? WHERE user_id = ?',
            (json.dumps(default_value), user_id)
        )
        return False

async def validate_and_fix_numeric_field(conn, user_id, field_name, value, default_value, expected_type):
    """验证并修复数值字段"""
    if value is None or not isinstance(value, expected_type):
        logger.warning(f"用户 {user_id} 的 {field_name} 字段类型错误: {value}，修复为 {default_value}")
        await conn.execute(
            f'UPDATE users SET {field_name} = ? WHERE user_id = ?',
            (default_value, user_id)
        )
        return False
    return True
    
async def fix_pending_payment_field(conn):
    """在数据库初始化时修复所有无效的 pending_payment 字段"""
    try:
        cursor = await conn.execute("SELECT user_id, pending_payment FROM users WHERE pending_payment IS NOT NULL")
        rows = await cursor.fetchall()
        fixes = 0
        for user_id, pending_payment in rows:
            try:
                if pending_payment in ('0', '', None) or (isinstance(pending_payment, str) and not pending_payment.strip()):
                    logger.warning(f"用户 {user_id} 的 pending_payment 字段无效: {pending_payment}，修复为 NULL")
                    await conn.execute(
                        'UPDATE users SET pending_payment = ? WHERE user_id = ?',
                        (None, user_id)
                    )
                    fixes += 1
                elif pending_payment and not isinstance(pending_payment, str):
                    logger.warning(f"用户 {user_id} 的 pending_payment 字段类型错误: {pending_payment}，修复为 NULL")
                    await conn.execute(
                        'UPDATE users SET pending_payment = ? WHERE user_id = ?',
                        (None, user_id)
                    )
                    fixes += 1
            except json.JSONDecodeError:
                logger.warning(f"用户 {user_id} 的 pending_payment 字段无法解析为 JSON: {pending_payment}，修复为 NULL")
                await conn.execute(
                    'UPDATE users SET pending_payment = ? WHERE user_id = ?',
                    (None, user_id)
                )
                fixes += 1
        await conn.commit()
        logger.info(f"pending_payment 字段修复完成，修复了 {fixes} 个记录")
        return fixes
    except Exception as e:
        logger.error(f"修复 pending_payment 字段失败: {str(e)}", exc_info=True)
        raise

async def cleanup_invalid_pending_payments():
    """清理数据库中所有无效的 pending_payment 值"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("SELECT user_id, pending_payment FROM users WHERE pending_payment IS NOT NULL")
            rows = await cursor.fetchall()
            fixes = 0
            for user_id, pending_payment in rows:
                try:
                    if pending_payment in ('0', '', None) or (isinstance(pending_payment, str) and not pending_payment.strip()):
                        logger.info(f"清理用户 {user_id} 的无效 pending_payment 值: {pending_payment}")
                        await conn.execute(
                            'UPDATE users SET pending_payment = NULL WHERE user_id = ?',
                            (user_id,)
                        )
                        fixes += 1
                    else:
                        # 尝试解析 JSON
                        json.loads(pending_payment)
                except json.JSONDecodeError:
                    logger.info(f"清理用户 {user_id} 的无效 pending_payment JSON: {pending_payment}")
                    await conn.execute(
                        'UPDATE users SET pending_payment = NULL WHERE user_id = ?',
                        (user_id,)
                    )
                    fixes += 1
            await conn.commit()
            logger.info(f"pending_payment 清理完成，修复了 {fixes} 个记录")
            return fixes
    except Exception as e:
        logger.error(f"清理 pending_payment 失败: {str(e)}", exc_info=True)
        raise

async def migrate_database(conn):
    """迁移数据库，修复旧数据以适配新代码"""
    try:
        # 获取当前表结构
        cursor = await conn.execute("PRAGMA table_info(users)")
        columns = {row[1] for row in await cursor.fetchall()}
        
        # 定义所有可能的字段
        json_fields = [
            ('conversation', []),
            ('photo_file_ids', []),
            ('video_file_ids', []),
            ('master_settings', {}),
            ('scene_settings', {}),
            ('training_preferences', {}),
            ('conversation_analysis', {}),
            ('instruction_states', {}),
            ('processed_payments', []),
            ('pending_payment', None)
        ]
        numeric_fields = [
            ('dice_round', 0, int),
            ('clothes', 0, int),
            ('waiting_for_proof', 0, int),
            ('remaining_count', 0, int),
            ('daily_voice_count', 3, int),
            ('has_used_bot', 0, int),
            ('daily_new_shares', 0, int),
            ('daily_old_shares', 0, int),
            ('voice_count', 0, int),
            ('voice_enabled', 0, int),
            ('free_master_change', 1, int),
            ('free_scene_change', 1, int),
            ('free_training_change', 1, int),
            ('paid_change_count', 0, int),
            ('channel_joined', 0, int),
            ('search_count', 0, int),
            ('has_interacted_with_ai', 0, int),
            ('pending_new_shares', 0, int),
            ('voice_settings_page', 0, int),
            ('random_message_counter', 0, int),
            ('initial_analysis_done', 0, int),
            ('response_delay', 1.0, float),
            ('max_sentences', 7, int),
            ('is_blocked', 0, int),
            ('previous_clothes', 5, int),
            ('dice_fail_count', 0, int),
        ]
        text_fields = [
            ('subscription_expiry', None),
            ('user_api_key', None),
            ('last_share_date', None),
            ('ban_until', None),
            ('daily_voice_date', None),
            ('last_share_report', None),
            ('selected_voice', DEFAULT_VOICE),
            ('last_channel_visit', None),
            ('last_search_date', None),
            ('last_search_time', None),
            ('join_order', None),
            ('shared_by', None),
            ('preferred_language', 'zh-cn')
        ]

        # 构建 SELECT 查询，仅包含存在的字段
        select_fields = ['user_id']
        field_mapping = {}
        for field_name, _ in json_fields:
            if field_name in columns:
                select_fields.append(field_name)
                field_mapping[field_name] = 'json'
        for field_name, _, _ in numeric_fields:
            if field_name in columns:
                select_fields.append(field_name)
                field_mapping[field_name] = 'numeric'
        for field_name, _ in text_fields:
            if field_name in columns:
                select_fields.append(field_name)
                field_mapping[field_name] = 'text'

        select_query = f"SELECT {', '.join(select_fields)} FROM users"
        cursor = await conn.execute(select_query)
        rows = await cursor.fetchall()
        fixes = 0

        for row in rows:
            user_id = row[0]
            row_dict = {select_fields[i]: row[i] for i in range(len(select_fields))}

            # 验证和修复 JSON 字段
            for field_name, default in json_fields:
                if field_name in row_dict:
                    if not await validate_and_fix_json_field(conn, user_id, field_name, row_dict[field_name], default):
                        fixes += 1

            # 验证和修复数值字段
            for field_name, default, expected_type in numeric_fields:
                if field_name in row_dict:
                    if not await validate_and_fix_numeric_field(conn, user_id, field_name, row_dict[field_name], default, expected_type):
                        fixes += 1

            # 验证和修复文本字段
            for field_name, default in text_fields:
                if field_name in row_dict and field_name == 'preferred_language':
                    if row_dict[field_name] not in ['zh-cn', 'en']:
                        logger.warning(f"用户 {user_id} 的 preferred_language 字段无效: {row_dict[field_name]}，修复为 'zh-cn'")
                        await conn.execute(
                            'UPDATE users SET preferred_language = ? WHERE user_id = ?',
                            ('zh-cn', user_id)
                        )
                        fixes += 1

        # 修复 pending_payment 字段
        fixes += await fix_pending_payment_field(conn)

        await conn.commit()
        logger.info(f"数据库迁移完成，修复了 {fixes} 个字段")
    except Exception as e:
        logger.error(f"数据库迁移失败: {str(e)}", exc_info=True)
        raise

async def migrate_from_old_database(old_db_path, new_db_path):
    """从旧数据库迁移数据到新数据库，处理字段不一致问题"""
    if not os.path.exists(old_db_path):
        logger.warning(f"旧数据库文件 {old_db_path} 不存在，跳过迁移")
        return

    logger.info(f"开始从旧数据库 {old_db_path} 迁移数据到新数据库 {new_db_path}")
    try:
        async with aiosqlite.connect(old_db_path) as old_conn, aiosqlite.connect(new_db_path) as new_conn:
            # 获取旧数据库的表结构
            cursor = await old_conn.execute("PRAGMA table_info(users)")
            old_columns = {row[1]: row[2] for row in await cursor.fetchall()}
            logger.info(f"旧数据库字段: {list(old_columns.keys())}")

            # 获取新数据库的表结构
            cursor = await new_conn.execute("PRAGMA table_info(users)")
            new_columns = {row[1]: row[2] for row in await cursor.fetchall()}
            logger.info(f"新数据库字段: {list(new_columns.keys())}")

            # 提取旧数据库中的所有用户数据
            select_fields = list(old_columns.keys())
            select_query = f"SELECT {', '.join(select_fields)} FROM users"
            cursor = await old_conn.execute(select_query)
            rows = await cursor.fetchall()

            migrated_count = 0
            for row in rows:
                user_id = row[0]
                row_dict = {select_fields[i]: row[i] for i in range(len(select_fields))}

                # 构建插入新数据库的数据，处理字段不一致
                insert_data = {}
                for field in new_columns:
                    if field in old_columns:
                        insert_data[field] = row_dict[field]
                    else:
                        # 新字段使用默认值
                        if field in [f[0] for f in [
                            ('conversation', []),
                            ('photo_file_ids', []),
                            ('video_file_ids', []),
                            ('master_settings', {}),
                            ('scene_settings', {}),
                            ('training_preferences', {}),
                            ('conversation_analysis', {}),
                            ('instruction_states', {}),
                            ('processed_payments', []),
                            ('pending_payment', None),
                            ('story_state', {}),
                            ('search_group_invited_users', [])
                        ]]:
                            insert_data[field] = json.dumps([])
                        elif field in [f[0] for f in [
                            ('dice_round', 0, int),
                            ('clothes', 0, int),
                            ('waiting_for_proof', 0, int),
                            ('remaining_count', 0, int),
                            ('daily_voice_count', 3, int),
                            ('has_used_bot', 0, int),
                            ('daily_new_shares', 0, int),
                            ('daily_old_shares', 0, int),
                            ('voice_count', 0, int),
                            ('voice_enabled', 0, int),
                            ('free_master_change', 1, int),
                            ('free_scene_change', 1, int),
                            ('free_training_change', 1, int),
                            ('paid_change_count', 0, int),
                            ('channel_joined', 0, int),
                            ('search_count', 0, int),
                            ('has_interacted_with_ai', 0, int),
                            ('pending_new_shares', 0, int),
                            ('voice_settings_page', 0, int),
                            ('random_message_counter', 0, int),
                            ('initial_analysis_done', 0, int),
                            ('is_blocked', 0, int),
                            ('previous_clothes', 5, int),
                            ('dice_fail_count', 0, int),
                        ]]:
                            insert_data[field] = 0
                        elif field == 'response_delay':
                            insert_data[field] = 1.0
                        elif field == 'max_sentences':
                            insert_data[field] = 7
                        elif field == 'selected_voice':
                            insert_data[field] = DEFAULT_VOICE
                        else:
                            insert_data[field] = None

                # 插入数据到新数据库
                placeholders = ', '.join(['?' for _ in insert_data])
                fields = ', '.join(insert_data.keys())
                insert_query = f"INSERT OR REPLACE INTO users ({fields}) VALUES ({placeholders})"
                await new_conn.execute(insert_query, list(insert_data.values()))
                migrated_count += 1

            await new_conn.commit()
            logger.info(f"数据库迁移成功，从 {old_db_path} 迁移了 {migrated_count} 个用户记录到 {new_db_path}")
    except Exception as e:
        logger.error(f"从旧数据库迁移数据失败: {str(e)}", exc_info=True)
        raise

async def init_database():
    """初始化数据库表结构，添加 membership_expiry 字段"""
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        # 创建漂流瓶表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS drift_bottles (
                id TEXT PRIMARY KEY,
                sender_id TEXT NOT NULL,
                voice_message_id INTEGER NOT NULL,
                chat_id TEXT NOT NULL,
                created_at TEXT NOT NULL,
                state TEXT DEFAULT 'waiting',
                delivery_count INTEGER DEFAULT 0,
                max_deliveries INTEGER DEFAULT 3,
                current_recipient TEXT,
                delivery_history TEXT DEFAULT '[]',
                reply_message_id INTEGER,
                reply_chat_id TEXT,
                replied_by TEXT,
                expires_at TEXT NOT NULL,
                conversation_id TEXT,
                is_conversation_active INTEGER DEFAULT 0,
                last_delivered_at TEXT
            )
        ''')

        # 检查并添加缺失的字段
        await _check_and_add_missing_columns(conn)

        # 创建用户每日漂流瓶使用记录表
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS user_bottle_daily (
                user_id TEXT NOT NULL,
                date TEXT NOT NULL,
                bottles_sent INTEGER DEFAULT 0,
                bottles_picked INTEGER DEFAULT 0,
                PRIMARY KEY (user_id, date)
            )
        ''')



        await conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id TEXT PRIMARY KEY,
                conversation TEXT DEFAULT '[]',
                photo_file_ids TEXT DEFAULT '[]',
                video_file_ids TEXT DEFAULT '[]',
                dice_round INTEGER DEFAULT 0,
                clothes INTEGER DEFAULT 0,
                waiting_for_proof INTEGER DEFAULT 0,
                remaining_count INTEGER DEFAULT 0,
                subscription_expiry TEXT DEFAULT NULL,
                user_api_key TEXT DEFAULT NULL,
                last_share_date TEXT DEFAULT NULL,
                ban_until TEXT DEFAULT NULL,
                daily_voice_count INTEGER DEFAULT 3,
                daily_voice_date TEXT DEFAULT NULL,
                has_used_bot INTEGER DEFAULT 0,
                daily_new_shares INTEGER DEFAULT 0,
                daily_old_shares INTEGER DEFAULT 0,
                last_share_report TEXT DEFAULT NULL,
                voice_count INTEGER DEFAULT 0,
                selected_voice TEXT DEFAULT NULL,
                voice_enabled INTEGER DEFAULT 0,
                master_settings TEXT DEFAULT '{}',
                scene_settings TEXT DEFAULT '{}',
                training_preferences TEXT DEFAULT '{}',
                master_gender TEXT DEFAULT 'male',
                free_master_change INTEGER DEFAULT 1,
                free_scene_change INTEGER DEFAULT 1,
                free_training_change INTEGER DEFAULT 1,
                paid_change_count INTEGER DEFAULT 0,
                channel_joined INTEGER DEFAULT 0,
                last_channel_visit TEXT DEFAULT NULL,
                search_count INTEGER DEFAULT 0,
                last_search_date TEXT DEFAULT NULL,
                last_search_time TEXT DEFAULT NULL,
                has_interacted_with_ai INTEGER DEFAULT 0,
                pending_new_shares INTEGER DEFAULT 0,
                join_order INTEGER DEFAULT NULL,
                shared_by TEXT DEFAULT NULL,
                voice_settings_page INTEGER DEFAULT 0,
                random_message_counter INTEGER DEFAULT 0,
                initial_analysis_done INTEGER DEFAULT 0,
                conversation_analysis TEXT DEFAULT '{}',
                instruction_states TEXT DEFAULT '{}',
                processed_payments TEXT DEFAULT '[]',
                response_delay REAL DEFAULT 1.0,
                max_sentences INTEGER DEFAULT 7,
                preferred_language TEXT DEFAULT 'zh-cn',
                last_notify_time REAL DEFAULT 0,
                is_blocked INTEGER DEFAULT 0,
                pending_payment TEXT DEFAULT NULL,
                story_state TEXT DEFAULT '{}',
                search_group_invited_users TEXT DEFAULT '[]',
                membership_expiry TEXT DEFAULT NULL,
                previous_clothes INTEGER DEFAULT 5,
                dice_fail_count INTEGER DEFAULT 0,
                permanent_member INTEGER DEFAULT 0,
                prompt_mode TEXT DEFAULT 'bdsm_dom_mild',
                custom_training_mode TEXT DEFAULT NULL,
                dog_food INTEGER DEFAULT 25,
                daily_free_dog_food INTEGER DEFAULT 3,
                daily_free_dog_food_date TEXT DEFAULT NULL,
                last_interaction TEXT DEFAULT NULL
            )
        ''')

        await conn.execute('CREATE INDEX IF NOT EXISTS idx_user_id ON users(user_id)')
        logger.info("已为 users 表添加或确认 user_id 索引")

        await conn.execute('''
            CREATE TABLE IF NOT EXISTS redeem_codes (
                code TEXT PRIMARY KEY,
                type TEXT NOT NULL,
                used INTEGER DEFAULT 0
            )
        ''')
        logger.info("已为 redeem_codes 表创建或确认")

        await conn.execute('''
            CREATE TABLE IF NOT EXISTS analysis_cache (
                cache_key TEXT PRIMARY KEY,
                analysis TEXT NOT NULL,
                timestamp TEXT NOT NULL
            )
        ''')
        logger.info("已为 analysis_cache 表创建或确认")

        cursor = await conn.execute("PRAGMA table_info(users)")
        columns = [row[1] for row in await cursor.fetchall()]
        required_columns = {
            "user_id": "TEXT PRIMARY KEY",
            "conversation": "TEXT DEFAULT '[]'",
            "photo_file_ids": "TEXT DEFAULT '[]'",
            "video_file_ids": "TEXT DEFAULT '[]'",
            "dice_round": "INTEGER DEFAULT 0",
            "clothes": "INTEGER DEFAULT 0",
            "waiting_for_proof": "INTEGER DEFAULT 0",
            "remaining_count": "INTEGER DEFAULT 0",
            "subscription_expiry": "TEXT DEFAULT NULL",
            "user_api_key": "TEXT DEFAULT NULL",
            "last_share_date": "TEXT DEFAULT NULL",
            "ban_until": "TEXT DEFAULT NULL",
            "daily_voice_count": "INTEGER DEFAULT 3",
            "daily_voice_date": "TEXT DEFAULT NULL",
            "has_used_bot": "INTEGER DEFAULT 0",
            "daily_new_shares": "INTEGER DEFAULT 0",
            "daily_old_shares": "INTEGER DEFAULT 0",
            "last_share_report": "TEXT DEFAULT NULL",
            "voice_count": "INTEGER DEFAULT 0",
            "selected_voice": "TEXT DEFAULT NULL",
            "voice_enabled": "INTEGER DEFAULT 0",
            "master_settings": "TEXT DEFAULT '{}'",
            "scene_settings": "TEXT DEFAULT '{}'",
            "training_preferences": "TEXT DEFAULT '{}'",
            "master_gender": "TEXT DEFAULT 'male'",
            "free_master_change": "INTEGER DEFAULT 1",
            "free_scene_change": "INTEGER DEFAULT 1",
            "free_training_change": "INTEGER DEFAULT 1",
            "paid_change_count": "INTEGER DEFAULT 0",
            "channel_joined": "INTEGER DEFAULT 0",
            "last_channel_visit": "TEXT DEFAULT NULL",
            "search_count": "INTEGER DEFAULT 0",
            "last_search_date": "TEXT DEFAULT NULL",
            "last_search_time": "TEXT DEFAULT NULL",
            "has_interacted_with_ai": "INTEGER DEFAULT 0",
            "pending_new_shares": "INTEGER DEFAULT 0",
            "join_order": "INTEGER DEFAULT NULL",
            "shared_by": "TEXT DEFAULT NULL",
            "voice_settings_page": "INTEGER DEFAULT 0",
            "random_message_counter": "INTEGER DEFAULT 0",
            "initial_analysis_done": "INTEGER DEFAULT 0",
            "conversation_analysis": "TEXT DEFAULT '{}'",
            "instruction_states": "TEXT DEFAULT '{}'",
            "processed_payments": "TEXT DEFAULT '[]'",
            "response_delay": "REAL DEFAULT 1.0",
            "max_sentences": "INTEGER DEFAULT 7",
            "preferred_language": "TEXT DEFAULT 'zh-cn'",
            "last_notify_time": "REAL DEFAULT 0",
            "is_blocked": "INTEGER DEFAULT 0",
            "pending_payment": "TEXT DEFAULT NULL",
            "story_state": "TEXT DEFAULT '{}'",
            "search_group_invited_users": "TEXT DEFAULT '[]'",
            "membership_expiry": "TEXT DEFAULT NULL",
            "previous_clothes": "INTEGER DEFAULT 5",
            "dice_fail_count": "INTEGER DEFAULT 0",
            "permanent_member": "INTEGER DEFAULT 0",
            "prompt_mode": "TEXT DEFAULT 'bdsm_dom_mild'",
            "custom_training_mode": "TEXT DEFAULT NULL",
            "dog_food": "INTEGER DEFAULT 25",
            "daily_free_dog_food": "INTEGER DEFAULT 3",
            "daily_free_dog_food_date": "TEXT DEFAULT NULL",
            "last_interaction": "TEXT DEFAULT NULL"
        }

        for col, col_type in required_columns.items():
            if col not in columns:
                await conn.execute(f"ALTER TABLE users ADD COLUMN {col} {col_type}")
                logger.info(f"添加缺失列 {col} 到 users 表")

        await conn.execute("UPDATE users SET master_settings = '{}' WHERE master_settings IS NULL OR master_settings = ''")
        await conn.execute("UPDATE users SET scene_settings = '{}' WHERE scene_settings IS NULL OR scene_settings = ''")
        await conn.execute("UPDATE users SET training_preferences = '{}' WHERE training_preferences IS NULL OR training_preferences = ''")
        await conn.execute("UPDATE users SET channel_joined = 0 WHERE channel_joined IS NULL")
        await conn.execute("UPDATE users SET search_count = 0 WHERE search_count IS NULL")
        await conn.execute("UPDATE users SET has_interacted_with_ai = 1 WHERE has_interacted_with_ai IS NULL AND remaining_count < 25")
        await conn.execute("UPDATE users SET has_interacted_with_ai = 0 WHERE has_interacted_with_ai IS NULL AND remaining_count = 25")
        await conn.execute("UPDATE users SET pending_new_shares = 0 WHERE pending_new_shares IS NULL")
        await conn.execute("UPDATE users SET shared_by = NULL WHERE shared_by IS NULL")
        await conn.execute("UPDATE users SET voice_settings_page = 0 WHERE voice_settings_page IS NULL")
        await conn.execute("UPDATE users SET random_message_counter = 0 WHERE random_message_counter IS NULL")
        await conn.execute("UPDATE users SET initial_analysis_done = 0 WHERE initial_analysis_done IS NULL")
        await conn.execute("UPDATE users SET conversation_analysis = '{}' WHERE conversation_analysis IS NULL")
        await conn.execute("UPDATE users SET instruction_states = '{}' WHERE instruction_states IS NULL")
        await conn.execute("UPDATE users SET processed_payments = '[]' WHERE processed_payments IS NULL")
        await conn.execute("UPDATE users SET response_delay = 1.0 WHERE response_delay IS NULL")
        await conn.execute("UPDATE users SET max_sentences = 7 WHERE max_sentences IS NULL")
        await conn.execute("UPDATE users SET preferred_language = 'zh-cn' WHERE preferred_language IS NULL")
        await conn.execute("UPDATE users SET last_notify_time = 0 WHERE last_notify_time IS NULL")
        await conn.execute("UPDATE users SET is_blocked = 0 WHERE is_blocked IS NULL")
        await conn.execute("UPDATE users SET pending_payment = NULL WHERE pending_payment IS NULL")
        await conn.execute("UPDATE users SET story_state = '{}' WHERE story_state IS NULL")
        await conn.execute("UPDATE users SET search_group_invited_users = '[]' WHERE search_group_invited_users IS NULL")
        
        # 修复无效的 membership_expiry 字段
        await conn.execute("UPDATE users SET membership_expiry = NULL WHERE membership_expiry = '{}'")
        await conn.execute("UPDATE users SET membership_expiry = NULL WHERE membership_expiry = '[]'")
        await conn.execute("UPDATE users SET membership_expiry = NULL WHERE membership_expiry LIKE '{%}'")
        logger.info("已清理无效的 membership_expiry 字段值")

        # 确保所有用户都有 prompt_mode 字段的默认值
        await conn.execute("UPDATE users SET prompt_mode = 'bdsm_dom_mild' WHERE prompt_mode IS NULL")
        logger.info("已为所有用户设置默认的 prompt_mode 值")

        cursor = await conn.execute("SELECT user_id, join_order FROM users WHERE join_order IS NULL")
        users_without_order = await cursor.fetchall()
        if users_without_order:
            cursor = await conn.execute("SELECT COALESCE(MAX(join_order), 0) FROM users")
            max_order = (await cursor.fetchone())[0] or 0
            for user_id, _ in users_without_order:
                max_order += 1
                await conn.execute("UPDATE users SET join_order = ? WHERE user_id = ?", (max_order, user_id))
            logger.info(f"已为 {len(users_without_order)} 个用户分配 join_order")

        await conn.commit()

        invalid_users = await clean_invalid_conversations()
        if invalid_users:
            logger.info(f"初始化时发现并修复了 {len(invalid_users)} 个用户的无效数据: {invalid_users}")

        # 执行积分系统升级：合并 remaining_count 和 voice_count 到 dog_food
        await migrate_to_dog_food_system(conn)
        
        # 将 daily_voice_count 重置为 daily_free_dog_food
        await migrate_daily_voice_to_dog_food(conn)
        
        await conn.commit()
        logger.info("积分系统升级完成")

async def check_db(old_db_path=None):
    """检查并初始化数据库，执行数据迁移，如果提供旧数据库路径则进行迁移"""
    if not os.path.exists(DB_PATH):
        logger.info(f"数据库文件 {DB_PATH} 不存在，将创建新数据库")
    
    # 创建备份
    await backup_database()
    
    await init_database()
    
    # 打印实际表结构
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        cursor = await conn.execute("PRAGMA table_info(users)")
        columns = await cursor.fetchall()
        column_names = [row[1] for row in columns]
        logger.info(f"实际数据库表 users 的字段数: {len(column_names)}")
        logger.info(f"实际数据库表 users 的字段名: {column_names}")
    
    # 如果提供了旧数据库路径，则执行迁移
    if old_db_path:
        await migrate_from_old_database(old_db_path, DB_PATH)
    
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        await migrate_database(conn)
    logger.info("数据库检查和迁移完成")

async def get_user_data(user_id, conn=None):
    """获取用户数据，包含容错处理，新增搜索群拉新字段，动态构建插入语句"""
    close_conn = False
    if conn is None:
        conn = await aiosqlite.connect(DB_PATH, timeout=30)
        close_conn = True
    
    try:
        cursor = await conn.execute("SELECT * FROM users WHERE user_id = ?", (str(user_id),))
        row = await cursor.fetchone()
        
        if row is None:
            # 默认值字典，包含所有可能的字段及其默认值
            user_data = {
                "user_id": str(user_id),
                "conversation": [],
                "photo_file_ids": [],
                "video_file_ids": [],
                "dice_round": 0,
                "clothes": 0,
                "waiting_for_proof": False,
                "previous_clothes": 5,  # 添加上一轮衣物数量字段，用于图片验证
                "remaining_count": 0,  # 新系统中文字积分为0
                "subscription_expiry": None,
                "user_api_key": None,
                "last_share_date": None,
                "ban_until": None,
                "daily_voice_count": 3,
                "daily_voice_date": datetime.now().date().isoformat(),
                "has_used_bot": False,
                "daily_new_shares": 0,
                "daily_old_shares": 0,
                "last_share_report": None,
                "voice_count": 0,
                "selected_voice": DEFAULT_VOICE,
                "voice_enabled": False,
                "master_settings": {},
                "scene_settings": {},
                "training_preferences": {},
                "master_gender": "male",
                "free_master_change": 1,
                "free_scene_change": 1,
                "free_training_change": 1,
                "paid_change_count": 0,
                "channel_joined": 0,
                "last_channel_visit": None,
                "search_count": 0,
                "last_search_date": None,
                "last_search_time": None,
                "has_interacted_with_ai": False,
                "pending_new_shares": 0,
                "join_order": None,
                "shared_by": None,
                "voice_settings_page": 0,
                "random_message_counter": 0,
                "initial_analysis_done": False,
                "conversation_analysis": {},
                "instruction_states": {},
                "processed_payments": [],
                "response_delay": 1.0,
                "max_sentences": 7,
                "preferred_language": 'zh-cn',
                "last_notify_time": 0.0,
                "is_blocked": False,
                "pending_payment": None,
                "story_state": {},
                "search_group_invited_users": [],
                "membership_expiry": None,
                "dice_fail_count": 0,
                "permanent_member": 0,
                "prompt_mode": 'bdsm_dom_mild',
                "custom_training_mode": None,
                "dog_food": 25,  # 新用户注册奖励25个狗粮
                "daily_free_dog_food": 3,
                "daily_free_dog_food_date": None
            }
            # 动态获取表结构
            cursor = await conn.execute("PRAGMA table_info(users)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # 构建插入语句
            fields_str = ", ".join(column_names)
            placeholders = ", ".join(["?" for _ in column_names])
            insert_query = f"INSERT INTO users ({fields_str}) VALUES ({placeholders})"
            
            # 构建插入值列表，基于列名从默认值字典中获取值
            insert_values = []
            for col_name in column_names:
                if col_name in user_data:
                    value = user_data[col_name]
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value)
                    insert_values.append(value)
                else:
                    insert_values.append(None)  # 对于未知字段使用 None 作为默认值
            
            # 执行插入
            await conn.execute(insert_query, insert_values)
            await conn.commit()
            logger.info(f"用户 {user_id} 数据未找到，已创建新记录")
            return user_data
        
        def safe_json_load(value, default, field_name):
            try:
                if field_name == 'pending_payment':
                    if value in ('0', '', None) or (isinstance(value, str) and not value.strip()):
                        return default
                    # 尝试解析 JSON，如果失败则返回默认值
                    return json.loads(value) if value is not None else default
                return json.loads(value) if value is not None else default
            except (TypeError, json.JSONDecodeError) as e:
                if field_name == 'pending_payment':
                    # 减少频繁日志，只记录一次
                    logger.debug(f"用户 {user_id} 的 {field_name} 字段无效: {value}，使用默认值 {default}")
                else:
                    logger.warning(f"用户 {user_id} 的 {field_name} 字段无效: {value}，使用默认值 {default}")
                return default
        
        # 动态获取表字段顺序
        cursor = await conn.execute("PRAGMA table_info(users)")
        columns_info = await cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        # 创建字段名到索引的映射
        field_index = {name: idx for idx, name in enumerate(column_names)}
        
        def safe_get_field(field_name, default=None):
            """根据字段名安全获取数据"""
            if field_name in field_index:
                idx = field_index[field_name]
                return row[idx] if len(row) > idx and row[idx] is not None else default
            else:
                logger.warning(f"字段 {field_name} 在数据库表中不存在")
                return default

        user_data = {
            "user_id": safe_get_field("user_id", str(user_id)),
            "conversation": safe_json_load(safe_get_field("conversation"), [], 'conversation'),
            "photo_file_ids": safe_json_load(safe_get_field("photo_file_ids"), [], 'photo_file_ids'),
            "video_file_ids": safe_json_load(safe_get_field("video_file_ids"), [], 'video_file_ids'),
            "dice_round": safe_get_field("dice_round", 0),
            "clothes": safe_get_field("clothes", 0),
            "waiting_for_proof": bool(safe_get_field("waiting_for_proof", 0)),
            "previous_clothes": safe_get_field("previous_clothes", 5),
            "remaining_count": safe_get_field("remaining_count", 0),  # 新系统中文字积分为0
            "subscription_expiry": safe_get_field("subscription_expiry"),
            "user_api_key": safe_get_field("user_api_key"),
            "last_share_date": safe_get_field("last_share_date"),
            "ban_until": safe_get_field("ban_until"),
            "daily_voice_count": safe_get_field("daily_voice_count", 3),
            "daily_voice_date": safe_get_field("daily_voice_date", datetime.now().date().isoformat()),
            "has_used_bot": bool(safe_get_field("has_used_bot", 0)),
            "daily_new_shares": safe_get_field("daily_new_shares", 0),
            "daily_old_shares": safe_get_field("daily_old_shares", 0),
            "last_share_report": safe_get_field("last_share_report"),
            "voice_count": safe_get_field("voice_count", 0),
            "selected_voice": safe_get_field("selected_voice", DEFAULT_VOICE),
            "voice_enabled": bool(safe_get_field("voice_enabled", 0)),
            "master_settings": safe_json_load(safe_get_field("master_settings"), {}, 'master_settings'),
            "scene_settings": safe_json_load(safe_get_field("scene_settings"), {}, 'scene_settings'),
            "training_preferences": safe_json_load(safe_get_field("training_preferences"), {}, 'training_preferences'),
            "master_gender": safe_get_field("master_gender", "male"),
            "free_master_change": safe_get_field("free_master_change", 1),
            "free_scene_change": safe_get_field("free_scene_change", 1),
            "free_training_change": safe_get_field("free_training_change", 1),
            "paid_change_count": safe_get_field("paid_change_count", 0),
            "channel_joined": safe_get_field("channel_joined", 0),
            "last_channel_visit": safe_get_field("last_channel_visit"),
            "search_count": safe_get_field("search_count", 0),
            "last_search_date": safe_get_field("last_search_date"),
            "last_search_time": safe_get_field("last_search_time"),
            "has_interacted_with_ai": bool(safe_get_field("has_interacted_with_ai", 0)),
            "pending_new_shares": safe_get_field("pending_new_shares", 0),
            "join_order": safe_get_field("join_order"),
            "shared_by": safe_get_field("shared_by"),
            "voice_settings_page": safe_get_field("voice_settings_page", 0),
            "random_message_counter": safe_get_field("random_message_counter", 0),
            "initial_analysis_done": bool(safe_get_field("initial_analysis_done", 0)),
            "conversation_analysis": safe_json_load(safe_get_field("conversation_analysis"), {}, 'conversation_analysis'),
            "instruction_states": safe_json_load(safe_get_field("instruction_states"), {}, 'instruction_states'),
            "processed_payments": safe_json_load(safe_get_field("processed_payments"), [], 'processed_payments'),
            "response_delay": safe_get_field("response_delay", 1.0),
            "max_sentences": safe_get_field("max_sentences", 7),
            "preferred_language": safe_get_field("preferred_language", 'zh-cn'),
            "last_notify_time": safe_get_field("last_notify_time", 0.0),
            "is_blocked": bool(safe_get_field("is_blocked", 0)),
            "pending_payment": safe_json_load(safe_get_field("pending_payment"), None, 'pending_payment'),
            "story_state": safe_json_load(safe_get_field("story_state"), {}, 'story_state'),
            "search_group_invited_users": safe_json_load(safe_get_field("search_group_invited_users"), [], 'search_group_invited_users'),
            "membership_expiry": safe_get_field("membership_expiry", None),
            "dice_fail_count": safe_get_field("dice_fail_count", 0),
            "permanent_member": safe_get_field("permanent_member", 0),
            "prompt_mode": safe_get_field("prompt_mode", 'bdsm_dom_mild'),
            "custom_training_mode": safe_get_field("custom_training_mode", None),
            "dog_food": safe_get_field("dog_food", 25),  # 新用户注册奖励25个狗粮
            "daily_free_dog_food": safe_get_field("daily_free_dog_food", 3),
            "daily_free_dog_food_date": safe_get_field("daily_free_dog_food_date", None),
            "last_interaction": safe_get_field("last_interaction", None)
        }
        
        # 验证并修复 membership_expiry 字段
        membership_expiry = user_data.get("membership_expiry")
        if membership_expiry is not None:
            validated_value, is_valid = validate_membership_expiry(membership_expiry)
            if not is_valid:
                logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {repr(membership_expiry)}，保存前重置为 None")
                user_data["membership_expiry"] = None
            else:
                user_data["membership_expiry"] = validated_value
        
        # 处理 join_order
        if user_data["join_order"] is None:
            cursor = await conn.execute("SELECT COALESCE(MAX(join_order), 0) FROM users")
            max_order = (await cursor.fetchone())[0] or 0
            user_data["join_order"] = max_order + 1
            await conn.execute("UPDATE users SET join_order = ? WHERE user_id = ?", 
                              (user_data["join_order"], str(user_id)))
            await conn.commit()
            logger.info(f"用户 {user_id} 被分配 join_order: {user_data['join_order']}")
        
        # 验证并完善用户数据，确保包含完整的默认设定
        user_data = validate_and_complete_user_data(user_data)
        
        logger.debug(f"获取用户 {user_id} 数据成功")
        return user_data
    except Exception as e:
        logger.error(f"获取用户数据失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        raise
    finally:
        if close_conn:
            await conn.close()


async def repair_user_data():
    """检查并修复所有用户数据的字段完整性，确保每个字段都有默认值"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 获取表结构
            cursor = await conn.execute("PRAGMA table_info(users)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]
            logger.info(f"开始修复用户数据，表结构字段数: {len(column_names)}，字段名: {column_names}")

            # 默认值字典
            default_values = {
                "user_id": "",
                "conversation": json.dumps([]),
                "photo_file_ids": json.dumps([]),
                "video_file_ids": json.dumps([]),
                "dice_round": 0,
                "clothes": 0,
                "waiting_for_proof": 0,
                "remaining_count": 0,
                "subscription_expiry": None,
                "user_api_key": None,
                "last_share_date": None,
                "ban_until": None,
                "daily_voice_count": 3,
                "daily_voice_date": datetime.now().date().isoformat(),
                "has_used_bot": 0,
                "daily_new_shares": 0,
                "daily_old_shares": 0,
                "last_share_report": None,
                "voice_count": 0,
                "selected_voice": DEFAULT_VOICE,
                "voice_enabled": 0,
                "master_settings": json.dumps({}),
                "scene_settings": json.dumps({}),
                "training_preferences": json.dumps({}),
                "master_gender": "male",
                "free_master_change": 1,
                "free_scene_change": 1,
                "free_training_change": 1,
                "paid_change_count": 0,
                "channel_joined": 0,
                "last_channel_visit": None,
                "search_count": 0,
                "last_search_date": None,
                "last_search_time": None,
                "has_interacted_with_ai": 0,
                "pending_new_shares": 0,
                "join_order": None,
                "shared_by": None,
                "voice_settings_page": 0,
                "random_message_counter": 0,
                "initial_analysis_done": 0,
                "conversation_analysis": json.dumps({}),
                "instruction_states": json.dumps({}),
                "processed_payments": json.dumps([]),
                "response_delay": 1.0,
                "max_sentences": 7,
                "preferred_language": 'zh-cn',
                "last_notify_time": 0.0,
                "is_blocked": 0,
                "pending_payment": None,
                "story_state": json.dumps({}),
                "search_group_invited_users": json.dumps([]),
                "previous_clothes": 5,
                "dice_fail_count": 0,
                "permanent_member": 0,
                "prompt_mode": 'bdsm_dom_mild',
                "custom_training_mode": None,
                "dog_food": 25,
                "daily_free_dog_food": 3,
                "daily_free_dog_food_date": None
            }

            # 遍历所有用户记录
            cursor = await conn.execute("SELECT user_id FROM users")
            user_ids = await cursor.fetchall()
            repaired_count = 0

            for user_id_tuple in user_ids:
                user_id = user_id_tuple[0]
                cursor = await conn.execute("SELECT * FROM users WHERE user_id = ?", (user_id,))
                row = await cursor.fetchone()
                
                if row is None:
                    continue
                
                # 构建更新语句和值
                update_fields = []
                update_values = []
                for idx, col_name in enumerate(column_names):
                    if idx < len(row) and row[idx] is None:
                        if col_name in default_values:
                            default_val = default_values[col_name]
                            if default_val is not None:  # 只有默认值不为 None 时才记录和更新
                                update_fields.append(f"{col_name} = ?")
                                update_values.append(default_val)
                                logger.info(f"用户 {user_id} 的字段 {col_name} 为 None，修复为默认值 {default_val}")
                                repaired_count += 1
                
                if update_fields:
                    update_query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
                    update_values.append(user_id)
                    await conn.execute(update_query, update_values)
                    await conn.commit()
            
            logger.info(f"用户数据修复完成，修复了 {repaired_count} 个字段缺失或 None 的记录")
            return repaired_count
    except Exception as e:
        logger.error(f"修复用户数据失败，错误: {str(e)}", exc_info=True)
        raise


async def fix_invalid_field(conn, user_id, field_name, correct_value):
    """修复数据库中无效的字段值"""
    try:
        if conn is None:
            async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
                await conn.execute(
                    f"UPDATE users SET {field_name} = ? WHERE user_id = ?",
                    (correct_value, user_id)
                )
                await conn.commit()
        else:
            await conn.execute(
                f"UPDATE users SET {field_name} = ? WHERE user_id = ?",
                (correct_value, user_id)
            )
            await conn.commit()
        logger.info(f"修复用户 {user_id} 的 {field_name} 字段为 {correct_value}")
    except Exception as e:
        logger.error(f"修复用户 {user_id} 的 {field_name} 字段失败: {str(e)}", exc_info=True)
        raise

async def save_user_data(user_id, user_data, conn=None):
    """保存用户数据，确保 JSON 字段包括 story_state 和 search_group_invited_users 正确序列化"""
    close_conn = False
    if conn is None:
        conn = await aiosqlite.connect(DB_PATH, timeout=30)
        close_conn = True
    
    try:
        if user_data["voice_count"] <= 0 and user_data["daily_voice_count"] <= 0:
            user_data["voice_enabled"] = False
        elif (user_data["voice_count"] > 0 or user_data["daily_voice_count"] > 0) and not user_data["voice_enabled"]:
            user_data["voice_enabled"] = True
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.debug(f"用户 {user_id} 开始保存数据到数据库，尝试 {attempt + 1}/{max_retries}")
                # 确保 pending_payment 是有效的 JSON 或 NULL
                pending_payment_value = json.dumps(user_data.get("pending_payment")) if user_data.get("pending_payment") is not None else None
                if user_data.get("pending_payment") is not None and not isinstance(user_data["pending_payment"], (dict, list)):
                    logger.warning(f"用户 {user_id} 的 pending_payment 字段值无效: {user_data['pending_payment']}，已转换为 NULL")
                    pending_payment_value = None
                
                # 验证并修复 membership_expiry 字段
                membership_expiry = user_data.get("membership_expiry")
                if membership_expiry is not None:
                    validated_value, is_valid = validate_membership_expiry(membership_expiry)
                    if not is_valid:
                        logger.warning(f"用户 {user_id} 的 membership_expiry 字段无效: {repr(membership_expiry)}，保存前重置为 None")
                        user_data["membership_expiry"] = None
                    else:
                        user_data["membership_expiry"] = validated_value
                
                # 动态获取表字段
                cursor = await conn.execute("PRAGMA table_info(users)")
                columns = await cursor.fetchall()
                column_names = [row[1] for row in columns]
                logger.debug(f"数据库表 users 的字段数: {len(column_names)}，字段名: {column_names}")
                
                # 构建插入数据
                insert_data = {
                    "user_id": str(user_id),
                    "conversation": json.dumps(user_data["conversation"]),
                    "photo_file_ids": json.dumps(user_data["photo_file_ids"]),
                    "video_file_ids": json.dumps(user_data["video_file_ids"]),
                    "dice_round": user_data["dice_round"],
                    "clothes": user_data["clothes"],
                    "waiting_for_proof": int(user_data["waiting_for_proof"]),
                    "remaining_count": user_data["remaining_count"],
                    "subscription_expiry": user_data["subscription_expiry"],
                    "user_api_key": user_data["user_api_key"],
                    "last_share_date": user_data["last_share_date"],
                    "ban_until": user_data["ban_until"],
                    "daily_voice_count": user_data["daily_voice_count"],
                    "daily_voice_date": user_data["daily_voice_date"],
                    "has_used_bot": int(user_data["has_used_bot"]),
                    "daily_new_shares": user_data["daily_new_shares"],
                    "daily_old_shares": user_data["daily_old_shares"],
                    "last_share_report": user_data["last_share_report"],
                    "voice_count": user_data["voice_count"],
                    "selected_voice": user_data["selected_voice"],
                    "voice_enabled": int(user_data["voice_enabled"]),
                    "master_settings": json.dumps(user_data["master_settings"]),
                    "scene_settings": json.dumps(user_data["scene_settings"]),
                    "training_preferences": json.dumps(user_data["training_preferences"]),
                    "master_gender": user_data["master_gender"],
                    "free_master_change": user_data["free_master_change"],
                    "free_scene_change": user_data["free_scene_change"],
                    "free_training_change": user_data["free_training_change"],
                    "paid_change_count": user_data["paid_change_count"],
                    "channel_joined": user_data["channel_joined"],
                    "last_channel_visit": user_data["last_channel_visit"],
                    "search_count": user_data["search_count"],
                    "last_search_date": user_data["last_search_date"],
                    "last_search_time": user_data["last_search_time"],
                    "has_interacted_with_ai": int(user_data["has_interacted_with_ai"]),
                    "pending_new_shares": user_data["pending_new_shares"],
                    "join_order": user_data["join_order"],
                    "shared_by": user_data["shared_by"],
                    "voice_settings_page": user_data.get("voice_settings_page", 0),
                    "random_message_counter": user_data["random_message_counter"],
                    "initial_analysis_done": int(user_data["initial_analysis_done"]),
                    "conversation_analysis": json.dumps(user_data["conversation_analysis"]),
                    "instruction_states": json.dumps(user_data["instruction_states"]),
                    "processed_payments": json.dumps(user_data["processed_payments"]),
                    "response_delay": user_data.get("response_delay", 1.0),
                    "max_sentences": user_data.get("max_sentences", 7),
                    "preferred_language": user_data.get("preferred_language", 'zh-cn'),
                    "last_notify_time": user_data.get("last_notify_time", 0.0),
                    "is_blocked": int(user_data.get("is_blocked", 0)),
                    "pending_payment": pending_payment_value,
                    "story_state": json.dumps(user_data.get("story_state", {})),
                    "search_group_invited_users": json.dumps(user_data.get("search_group_invited_users", [])),
                    "membership_expiry": user_data["membership_expiry"],
                    "previous_clothes": user_data["previous_clothes"],
                    "dice_fail_count": user_data["dice_fail_count"],
                    "permanent_member": user_data["permanent_member"],
                    "prompt_mode": user_data["prompt_mode"],
                    "custom_training_mode": user_data.get("custom_training_mode", None),
                    "dog_food": user_data["dog_food"],
                    "daily_free_dog_food": user_data["daily_free_dog_food"],
                    "daily_free_dog_food_date": user_data["daily_free_dog_food_date"],
                    "last_interaction": user_data.get("last_interaction", None)
                }
                
                # 确保所有字段都有值，对于未知字段使用默认值 None
                values = []
                for col in column_names:
                    if col in insert_data:
                        values.append(insert_data[col])
                    else:
                        values.append(None)  # 对于未知字段，使用默认值 None
                
                # 构建插入语句
                fields_str = ", ".join(column_names)
                placeholders = ", ".join(["?" for _ in column_names])
                insert_query = f"INSERT OR REPLACE INTO users ({fields_str}) VALUES ({placeholders})"
                
                await conn.execute(insert_query, values)
                await conn.commit()
                logger.debug(f"用户 {user_id} 保存数据成功，random_message_counter: {user_data['random_message_counter']}")
                return
            except Exception as e:
                logger.error(f"保存用户 {user_id} 数据失败，尝试 {attempt + 1}/{max_retries}，错误类型: {type(e).__name__}，错误: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                    continue
                raise
    finally:
        if close_conn:
            await conn.close()

async def use_redeem_code(code, user_id, user_data):
    """使用兑换码，支持会员兑换码"""
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        try:
            cursor = await conn.execute("SELECT type FROM redeem_codes WHERE code = ? AND used = 0", (code,))
            row = await cursor.fetchone()
            if not row:
                logger.warning(f"兑换码 {code} 无效或已使用，用户ID: {user_id}")
                return False, "无效或已使用的兑换码"
            
            code_type = row[0]
            if code_type == "text_100":
                user_data["dog_food"] += 100  # 原100次文字改为100个狗粮
                message = f"获得100个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
            elif code_type == "voice_100":
                user_data["dog_food"] += 100  # 原100次语音改为100个狗粮
                message = f"获得100个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
            elif code_type == "settings":
                user_data["paid_change_count"] += 1
                message = f"获得1次调教设定修改机会！当前剩余付费修改次数：{user_data['paid_change_count']}，狗粮数量：{user_data['dog_food']}"
            elif code_type == "text_50":
                user_data["dog_food"] += 50  # 原50次文字改为50个狗粮
                message = f"获得50个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
            elif code_type == "voice_20":
                user_data["dog_food"] += 20  # 原20次语音改为20个狗粮
                message = f"获得20个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
            elif code_type == "voice_50":
                user_data["dog_food"] += 50  # 原50次语音改为50个狗粮
                message = f"获得50个狗粮🦴！当前狗粮数量：{user_data['dog_food']}，每日免费狗粮：{user_data['daily_free_dog_food']}"
            elif code_type == "membership_30_days":
                current_expiry = user_data.get("membership_expiry")
                # 使用新的验证函数检查当前到期时间
                validated_expiry, is_valid = validate_membership_expiry(current_expiry)
                
                if is_valid and validated_expiry:
                    try:
                        current_expiry_date = datetime.fromisoformat(validated_expiry)
                        new_expiry = current_expiry_date + timedelta(days=30)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"用户 {user_id} 的 membership_expiry 字段解析失败: {validated_expiry}，错误: {str(e)}，从当前时间开始计算")
                        new_expiry = datetime.now() + timedelta(days=30)
                else:
                    logger.info(f"用户 {user_id} 当前无有效会员资格，从当前时间开始计算30天")
                    new_expiry = datetime.now() + timedelta(days=30)
                
                # 使用安全函数设置会员到期时间
                safe_set_membership_expiry(user_data, new_expiry)
                user_data["dog_food"] += 20  # 原10次语音改为20个狗粮
                message = f"获得30天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和20个狗粮🦴！当前狗粮数量：{user_data['dog_food']}"
            elif code_type == "membership_365_days":
                current_expiry = user_data.get("membership_expiry")
                # 使用新的验证函数检查当前到期时间
                validated_expiry, is_valid = validate_membership_expiry(current_expiry)
                
                if is_valid and validated_expiry:
                    try:
                        current_expiry_date = datetime.fromisoformat(validated_expiry)
                        new_expiry = current_expiry_date + timedelta(days=365)
                    except (ValueError, TypeError) as e:
                        logger.warning(f"用户 {user_id} 的 membership_expiry 字段解析失败: {validated_expiry}，错误: {str(e)}，从当前时间开始计算")
                        new_expiry = datetime.now() + timedelta(days=365)
                else:
                    logger.info(f"用户 {user_id} 当前无有效会员资格，从当前时间开始计算365天")
                    new_expiry = datetime.now() + timedelta(days=365)
                
                # 使用安全函数设置会员到期时间
                safe_set_membership_expiry(user_data, new_expiry)
                user_data["dog_food"] += 100  # 原100次语音改为100个狗粮
                message = f"获得365天会员资格（到期时间：{new_expiry.isoformat()[:10]}）和100个狗粮🦴！当前狗粮数量：{user_data['dog_food']}"
            else:
                logger.warning(f"未知兑换码类型 {code_type}，用户ID: {user_id}")
                return False, "无效的兑换码类型"

            await conn.execute("UPDATE redeem_codes SET used = 1 WHERE code = ?", (code,))
            await save_user_data(user_id, user_data, conn)
            await conn.commit()
            logger.info(f"用户 {user_id} 成功使用兑换码 {code}，类型: {code_type}")
            return True, message
        except Exception as e:
            logger.error(f"使用兑换码 {code} 失败，用户ID: {user_id}，错误: {str(e)}")
            await conn.rollback()
            return False, "兑换码处理失败，请稍后再试"



async def backup_conversations():
    """备份用户对话数据"""
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "backup")
    os.makedirs(backup_dir, exist_ok=True)
    backup_file = os.path.join(backup_dir, f"conversations_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        cursor = await conn.execute("SELECT user_id, conversation FROM users WHERE conversation != '[]'")
        rows = await cursor.fetchall()
        backup_data = {row[0]: row[1] for row in rows}
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
    logger.info(f"对话历史已备份到 {backup_file}")
    return backup_file

async def restore_user_conversation(user_id, conversation=None):
    """恢复用户对话记忆"""
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "backup")
    backup_files = [f for f in os.listdir(backup_dir) if f.startswith("conversations_backup_")]
    if not backup_files:
        return False, "没有找到备份文件，无法恢复！"

    latest_backup = max(backup_files, key=lambda x: os.path.getctime(os.path.join(backup_dir, x)))
    backup_path = os.path.join(backup_dir, latest_backup)

    with open(backup_path, "r", encoding="utf-8") as f:
        backup_data = json.load(f)

    if str(user_id) not in backup_data:
        return False, "爷没找到你的备份记录，可能你本来就没啥记忆！"

    conversation = backup_data[str(user_id)]
    try:
        json.loads(conversation)
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            await conn.execute(
                "INSERT OR REPLACE INTO users (user_id, conversation) VALUES (?, ?)",
                (str(user_id), conversation)
            )
            await conn.commit()
        logger.info(f"用户 {user_id} 的对话历史已从 {latest_backup} 恢复")
        return True, f"贱奴！你的狗脑子记忆已从 {latest_backup} 恢复！"
    except json.JSONDecodeError:
        logger.warning(f"用户 {user_id} 的备份数据 JSON 格式错误: {conversation}")
        return False, "你的备份记录是垃圾数据，爷修不好，滚去重来吧！"

async def get_user_number(user_id):
    """获取用户编号"""
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        cursor = await conn.execute("SELECT join_order FROM users WHERE user_id = ?", (str(user_id),))
        row = await cursor.fetchone()
        if row and row[0] is not None:
            index = row[0]
        else:
            cursor = await conn.execute("SELECT COALESCE(MAX(join_order), 0) FROM users")
            index = (await cursor.fetchone())[0] + 1
            await conn.execute("INSERT OR REPLACE INTO users (user_id, join_order) VALUES (?, ?)", 
                              (str(user_id), index))
            await conn.commit()
        if index <= 999999:
            return f"SB{index:06d}"
        return f"SB{index}"

async def clear_user_data(user_id):
    """清除用户数据"""
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        await conn.execute("DELETE FROM users WHERE user_id = ?", (str(user_id),))
        await conn.commit()
    logger.info(f"用户 {user_id} 的所有数据已清除")

async def clear_all_users_data():
    """清除所有用户数据"""
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        await conn.execute("DELETE FROM users")
        await conn.commit()
    logger.info("所有用户数据已清除")

async def clear_all_conversations():
    """清除所有用户对话历史"""
    backup_file = await backup_conversations()
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        await conn.execute("UPDATE users SET conversation = '[]' WHERE conversation != '[]'")
        await conn.commit()
    logger.info(f"所有用户的对话历史已清除，备份保存为 {backup_file}")

async def clear_conversation_memory(user_id):
    """清除用户对话历史"""
    user_data = await get_user_data(user_id)
    user_data["conversation"] = []
    await save_user_data(user_id, user_data)
    logger.info(f"用户 {user_id} 的对话历史已清除")

async def clean_invalid_conversations():
    """清洗无效的 conversation 数据"""
    invalid_users = []
    async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
        cursor = await conn.execute("SELECT user_id, conversation FROM users WHERE conversation IS NOT NULL")
        rows = await cursor.fetchall()

        for user_id, conversation in rows:
            if not conversation:
                await conn.execute(
                    "UPDATE users SET conversation = '[]' WHERE user_id = ?",
                    (user_id,)
                )
                continue

            try:
                json.loads(conversation)
            except json.JSONDecodeError:
                logger.warning(f"用户 {user_id} 的 conversation 数据无效: {conversation}")
                invalid_users.append(user_id)
                await conn.execute(
                    "UPDATE users SET conversation = '[]' WHERE user_id = ?",
                    (user_id,)
                )

        await conn.commit()

    if invalid_users:
        logger.info(f"清洗完成，发现并修复 {len(invalid_users)} 个用户的无效 conversation 数据")
        return invalid_users
    else:
        logger.info("清洗完成，所有 conversation 数据均有效")
        return []

async def get_cached_personality(user_id: str) -> str:
    """获取缓存的用户画像"""
    async with aiosqlite.connect(DB_PATH) as conn:
        try:
            cursor = await conn.execute('SELECT analysis, timestamp FROM analysis_cache WHERE cache_key = ?', (f"personality_{user_id}",))
            row = await cursor.fetchone()
            if row:
                timestamp = datetime.fromisoformat(row[1])
                if datetime.now() - timestamp < timedelta(hours=24):
                    return row[0]
            return None
        except Exception as e:
            logger.error(f"获取缓存用户画像失败，用户ID: {user_id}, 错误: {str(e)}")
            return None

async def cache_personality(user_id: str, personality: str):
    """缓存用户画像"""
    async with aiosqlite.connect(DB_PATH) as conn:
        try:
            await conn.execute(
                'INSERT OR REPLACE INTO analysis_cache (cache_key, analysis, timestamp) VALUES (?, ?, ?)',
                (f"personality_{user_id}", personality, datetime.now().isoformat())
            )
            await conn.commit()
            logger.debug(f"用户画像缓存成功，用户ID: {user_id}, 画像: {personality}")
        except Exception as e:
            logger.error(f"缓存用户画像失败，用户ID: {user_id}, 错误: {str(e)}")

async def migrate_to_dog_food_system(conn):
    """迁移现有积分系统到狗粮系统"""
    try:
        # 检查是否需要迁移 - 查找所有有剩余积分的用户
        cursor = await conn.execute("SELECT COUNT(*) FROM users WHERE remaining_count > 0 OR voice_count > 0")
        need_migration = (await cursor.fetchone())[0]
        
        if need_migration > 0:
            logger.info(f"开始迁移积分系统，需要迁移 {need_migration} 个用户")
            
            # 记录迁移前的用户信息
            cursor = await conn.execute("SELECT user_id, dog_food, remaining_count, voice_count FROM users WHERE remaining_count > 0 OR voice_count > 0")
            users_to_migrate = await cursor.fetchall()
            
            for user_id, dog_food, remaining_count, voice_count in users_to_migrate:
                logger.info(f"迁移用户 {user_id}：原狗粮 {dog_food}，文字积分 {remaining_count}，语音积分 {voice_count} -> 新狗粮 {dog_food + remaining_count + voice_count}")
            
            # 合并文本积分和语音积分到狗粮 - 将原有积分加到现有狗粮上
            await conn.execute("""
                UPDATE users 
                SET dog_food = dog_food + remaining_count + voice_count,
                    remaining_count = 0,
                    voice_count = 0
                WHERE remaining_count > 0 OR voice_count > 0
            """)
            
            logger.info(f"积分系统迁移完成，共迁移了 {len(users_to_migrate)} 个用户的积分")
        else:
            logger.info("积分系统已经是最新版本，无需迁移")
            
    except Exception as e:
        logger.error(f"积分系统迁移失败: {str(e)}", exc_info=True)
        raise


async def migrate_daily_voice_to_dog_food(conn):
    """迁移每日语音积分到每日免费狗粮"""
    try:
        # 检查是否需要迁移
        cursor = await conn.execute("SELECT COUNT(*) FROM users WHERE daily_free_dog_food = 3 AND daily_free_dog_food_date IS NULL")
        need_migration = (await cursor.fetchone())[0]
        
        if need_migration > 0:
            logger.info(f"开始迁移每日语音积分，需要迁移 {need_migration} 个用户")
            
            # 将现有的 daily_voice_count 转换为 daily_free_dog_food
            await conn.execute("""
                UPDATE users 
                SET daily_free_dog_food = daily_voice_count,
                    daily_free_dog_food_date = daily_voice_date
                WHERE daily_free_dog_food_date IS NULL
            """)
            
            logger.info("每日语音积分迁移完成")
        else:
            logger.info("每日语音积分已经是最新版本，无需迁移")
            
    except Exception as e:
        logger.error(f"每日语音积分迁移失败: {str(e)}", exc_info=True)
        raise


async def get_user_number(user_id):
    """获取用户编号"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("SELECT join_order FROM users WHERE user_id = ?", (str(user_id),))
            row = await cursor.fetchone()
            if row:
                return row[0]
            else:
                logger.warning(f"用户 {user_id} 不存在")
                return None
    except Exception as e:
        logger.error(f"获取用户编号失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        return None


async def update_user_last_interaction(user_id):
    """更新用户最后交互时间"""
    try:
        current_time = datetime.now().isoformat()
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            await conn.execute(
                "UPDATE users SET last_interaction = ? WHERE user_id = ?",
                (current_time, str(user_id))
            )
            await conn.commit()
            logger.debug(f"更新用户 {user_id} 最后交互时间: {current_time}")
    except Exception as e:
        logger.error(f"更新用户最后交互时间失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)


async def get_active_users_for_bottle(sender_id, limit=50):
    """获取活跃用户列表用于漂流瓶投递，按优先级排序"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 查询除发送者外的所有用户，排除已屏蔽的用户，按最后交互时间排序
            cursor = await conn.execute("""
                SELECT user_id, last_interaction
                FROM users
                WHERE user_id != ?
                    AND (is_blocked = 0 OR is_blocked IS NULL)
                    AND user_id NOT LIKE '%test%'
                    AND user_id NOT LIKE '%bot%'
                ORDER BY
                    CASE
                        WHEN last_interaction IS NULL THEN 0
                        WHEN datetime(last_interaction) > datetime('now', '-10 minutes') THEN 4
                        WHEN datetime(last_interaction) > datetime('now', '-1 day') THEN 3
                        WHEN datetime(last_interaction) > datetime('now', '-7 days') THEN 2
                        ELSE 1
                    END DESC,
                    last_interaction DESC
                LIMIT ?
            """, (str(sender_id), limit))

            users = await cursor.fetchall()
            logger.info(f"获取到 {len(users)} 个活跃用户用于漂流瓶投递 (发送者: {sender_id})")

            # 如果用户数量太少，记录警告
            if len(users) < 5:
                logger.warning(f"可用于漂流瓶投递的用户数量较少: {len(users)}")

            return users
    except Exception as e:
        logger.error(f"获取活跃用户失败，发送者ID: {sender_id}, 错误: {str(e)}", exc_info=True)
        return []


# 漂流瓶数据库操作函数
async def save_drift_bottle(bottle_data):
    """保存漂流瓶到数据库"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            await conn.execute("""
                INSERT INTO drift_bottles (
                    id, sender_id, voice_message_id, chat_id, created_at,
                    state, delivery_count, max_deliveries, current_recipient,
                    delivery_history, reply_message_id, reply_chat_id,
                    replied_by, expires_at, conversation_id, is_conversation_active,
                    last_delivered_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                bottle_data['id'],
                bottle_data['sender_id'],
                bottle_data['voice_message_id'],
                bottle_data['chat_id'],
                bottle_data['created_at'],
                bottle_data['state'],
                bottle_data['delivery_count'],
                bottle_data['max_deliveries'],
                bottle_data['current_recipient'],
                bottle_data['delivery_history'],
                bottle_data['reply_message_id'],
                bottle_data['reply_chat_id'],
                bottle_data['replied_by'],
                bottle_data['expires_at'],
                bottle_data.get('conversation_id'),
                bottle_data.get('is_conversation_active', 0),
                bottle_data.get('last_delivered_at')
            ))
            await conn.commit()
            logger.info(f"漂流瓶 {bottle_data['id']} 已保存到数据库")
    except Exception as e:
        logger.error(f"保存漂流瓶失败: {str(e)}", exc_info=True)
        raise


async def update_drift_bottle(bottle_id, updates):
    """更新漂流瓶信息"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 构建更新语句
            set_clauses = []
            values = []
            for key, value in updates.items():
                set_clauses.append(f"{key} = ?")
                values.append(value)

            values.append(bottle_id)

            await conn.execute(f"""
                UPDATE drift_bottles
                SET {', '.join(set_clauses)}
                WHERE id = ?
            """, values)
            await conn.commit()
            logger.debug(f"漂流瓶 {bottle_id} 已更新: {updates}")
    except Exception as e:
        logger.error(f"更新漂流瓶失败，ID: {bottle_id}, 错误: {str(e)}", exc_info=True)
        raise


async def get_drift_bottle(bottle_id):
    """获取漂流瓶信息"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT * FROM drift_bottles WHERE id = ?
            """, (bottle_id,))
            row = await cursor.fetchone()

            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            return None
    except Exception as e:
        logger.error(f"获取漂流瓶失败，ID: {bottle_id}, 错误: {str(e)}", exc_info=True)
        return None


async def get_waiting_bottles(limit=100):
    """获取等待投递的漂流瓶"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT * FROM drift_bottles
                WHERE state = 'waiting'
                    AND datetime(expires_at) > datetime('now')
                    AND delivery_count < max_deliveries
                ORDER BY created_at ASC
                LIMIT ?
            """, (limit,))
            rows = await cursor.fetchall()

            bottles = []
            if rows:
                columns = [description[0] for description in cursor.description]
                bottles = [dict(zip(columns, row)) for row in rows]

            return bottles
    except Exception as e:
        logger.error(f"获取等待投递的漂流瓶失败: {str(e)}", exc_info=True)
        return []


async def get_user_bottles(user_id, recipient=False):
    """获取用户的漂流瓶（发送的或接收的）"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            if recipient:
                cursor = await conn.execute("""
                    SELECT * FROM drift_bottles
                    WHERE current_recipient = ?
                        AND state IN ('delivered', 'viewed')
                    ORDER BY created_at DESC
                """, (str(user_id),))
            else:
                cursor = await conn.execute("""
                    SELECT * FROM drift_bottles
                    WHERE sender_id = ?
                    ORDER BY created_at DESC
                """, (str(user_id),))

            rows = await cursor.fetchall()
            bottles = []
            if rows:
                columns = [description[0] for description in cursor.description]
                bottles = [dict(zip(columns, row)) for row in rows]

            return bottles
    except Exception as e:
        logger.error(f"获取用户漂流瓶失败，用户ID: {user_id}, 错误: {str(e)}", exc_info=True)
        return []


async def cleanup_expired_bottles():
    """清理过期的漂流瓶"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 删除过期的漂流瓶
            cursor = await conn.execute("""
                DELETE FROM drift_bottles
                WHERE datetime(expires_at) <= datetime('now')
                    OR state = 'completed'
            """)
            deleted_count = cursor.rowcount
            await conn.commit()

            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 个过期或已完成的漂流瓶")

            return deleted_count
    except Exception as e:
        logger.error(f"清理过期漂流瓶失败: {str(e)}", exc_info=True)
        return 0


async def get_bottle_stats():
    """获取漂流瓶统计信息"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN state = 'waiting' THEN 1 ELSE 0 END) as waiting,
                    SUM(CASE WHEN state = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN state = 'viewed' THEN 1 ELSE 0 END) as viewed,
                    SUM(CASE WHEN state = 'replied' THEN 1 ELSE 0 END) as replied,
                    SUM(CASE WHEN state = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN datetime(expires_at) <= datetime('now') THEN 1 ELSE 0 END) as expired
                FROM drift_bottles
            """)
            row = await cursor.fetchone()

            if row:
                return {
                    'total': row[0] or 0,
                    'waiting': row[1] or 0,
                    'delivered': row[2] or 0,
                    'viewed': row[3] or 0,
                    'replied': row[4] or 0,
                    'completed': row[5] or 0,
                    'expired': row[6] or 0
                }
            return {}
    except Exception as e:
        logger.error(f"获取漂流瓶统计失败: {str(e)}", exc_info=True)
        return {}


async def get_user_daily_bottle_count(user_id, date=None):
    """获取用户当日已发送的漂流瓶数量"""
    try:
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT bottles_sent FROM user_bottle_daily
                WHERE user_id = ? AND date = ?
            """, (str(user_id), date))
            row = await cursor.fetchone()

            return row[0] if row else 0
    except Exception as e:
        logger.error(f"获取用户每日漂流瓶数量失败，用户ID: {user_id}, 错误: {str(e)}")
        return 0


async def increment_user_daily_bottle_count(user_id, date=None):
    """增加用户当日漂流瓶发送数量"""
    try:
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            await conn.execute("""
                INSERT INTO user_bottle_daily (user_id, date, bottles_sent)
                VALUES (?, ?, 1)
                ON CONFLICT(user_id, date)
                DO UPDATE SET bottles_sent = bottles_sent + 1
            """, (str(user_id), date))
            await conn.commit()

    except Exception as e:
        logger.error(f"增加用户每日漂流瓶数量失败，用户ID: {user_id}, 错误: {str(e)}")
        raise


async def get_user_daily_bottle_limit(user_id):
    """获取用户每日漂流瓶限制数量"""
    try:
        user_data = await get_user_data(user_id)

        # 检查是否为会员
        if user_data.get("permanent_member", False):
            return 3  # 终身会员每天3个

        # 检查会员到期时间
        membership_expiry = user_data.get("membership_expiry")
        if membership_expiry:
            try:
                from datetime import datetime
                expiry_date = datetime.fromisoformat(membership_expiry)
                if datetime.now() < expiry_date:
                    return 3  # 会员每天3个
            except (ValueError, TypeError):
                pass

        return 1  # 普通用户每天1个
    except Exception as e:
        logger.error(f"获取用户每日限制失败，用户ID: {user_id}, 错误: {str(e)}")
        return 1  # 默认普通用户限制


async def can_user_send_bottle_today(user_id):
    """检查用户今天是否还能发送漂流瓶"""
    try:
        current_count = await get_user_daily_bottle_count(user_id)
        daily_limit = await get_user_daily_bottle_limit(user_id)

        return current_count < daily_limit, current_count, daily_limit
    except Exception as e:
        logger.error(f"检查用户发送权限失败，用户ID: {user_id}, 错误: {str(e)}")
        return False, 0, 1


async def get_user_daily_pick_count(user_id, date=None):
    """获取用户当日已捞取的漂流瓶数量"""
    try:
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT bottles_picked FROM user_bottle_daily
                WHERE user_id = ? AND date = ?
            """, (str(user_id), date))
            row = await cursor.fetchone()

            return row[0] if row else 0
    except Exception as e:
        logger.error(f"获取用户每日捞瓶子数量失败，用户ID: {user_id}, 错误: {str(e)}")
        return 0


async def increment_user_daily_pick_count(user_id, date=None):
    """增加用户当日捞瓶子数量"""
    try:
        if date is None:
            from datetime import datetime
            date = datetime.now().strftime('%Y-%m-%d')

        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            await conn.execute("""
                INSERT INTO user_bottle_daily (user_id, date, bottles_picked)
                VALUES (?, ?, 1)
                ON CONFLICT(user_id, date)
                DO UPDATE SET bottles_picked = bottles_picked + 1
            """, (str(user_id), date))
            await conn.commit()

    except Exception as e:
        logger.error(f"增加用户每日捞瓶子数量失败，用户ID: {user_id}, 错误: {str(e)}")
        raise


async def get_user_daily_pick_limit(user_id):
    """获取用户每日捞瓶子限制数量"""
    try:
        user_data = await get_user_data(user_id)

        # 检查是否为会员
        if user_data.get("permanent_member", False):
            return 3  # 终身会员每天3个

        # 检查会员到期时间
        membership_expiry = user_data.get("membership_expiry")
        if membership_expiry:
            try:
                from datetime import datetime
                expiry_date = datetime.fromisoformat(membership_expiry)
                if datetime.now() < expiry_date:
                    return 3  # 会员每天3个
            except (ValueError, TypeError):
                pass

        return 1  # 普通用户每天1个
    except Exception as e:
        logger.error(f"获取用户每日捞瓶子限制失败，用户ID: {user_id}, 错误: {str(e)}")
        return 1  # 默认普通用户限制


async def can_user_pick_bottle_today(user_id):
    """检查用户今天是否还能捞取漂流瓶"""
    try:
        current_count = await get_user_daily_pick_count(user_id)
        daily_limit = await get_user_daily_pick_limit(user_id)

        return current_count < daily_limit, current_count, daily_limit
    except Exception as e:
        logger.error(f"检查用户捞瓶子权限失败，用户ID: {user_id}, 错误: {str(e)}")
        return False, 0, 1


async def get_pickable_bottles(user_id, limit=50):
    """获取可捞取的漂流瓶（7天内，未被回复，不是自己的）"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT * FROM drift_bottles
                WHERE sender_id != ?
                AND state IN ('waiting', 'delivered', 'viewed', 'rejected')
                AND replied_by IS NULL
                AND datetime(created_at) >= datetime('now', '-7 days')
                AND datetime(expires_at) > datetime('now')
                ORDER BY RANDOM()
                LIMIT ?
            """, (str(user_id), limit))

            rows = await cursor.fetchall()

            # 转换为字典格式
            bottles = []
            if rows:
                columns = [description[0] for description in cursor.description]
                bottles = [dict(zip(columns, row)) for row in rows]

            return bottles

    except Exception as e:
        logger.error(f"获取可捞取漂流瓶失败，用户ID: {user_id}, 错误: {str(e)}")
        return []


async def get_unviewed_bottles_24h():
    """获取24小时内未查看的已投递漂流瓶"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("""
                SELECT * FROM drift_bottles
                WHERE state = 'delivered'
                AND last_delivered_at IS NOT NULL
                AND datetime(last_delivered_at) <= datetime('now', '-24 hours')
                AND datetime(expires_at) > datetime('now')
                AND delivery_count < max_deliveries
            """)

            rows = await cursor.fetchall()
            bottles = []
            if rows:
                columns = [description[0] for description in cursor.description]
                bottles = [dict(zip(columns, row)) for row in rows]

            return bottles
    except Exception as e:
        logger.error(f"获取24小时未查看漂流瓶失败: {str(e)}")
        return []
