#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漂流瓶文件诊断脚本
检查漂流瓶文件的存储状态和清理机制
"""

import os
import time
import re
from datetime import datetime, timed<PERSON><PERSON>


def check_download_directory():
    """检查下载目录状态"""
    download_dir = "/botfather/ceshi/downloads"
    
    print("🔍 检查下载目录状态")
    print("=" * 60)
    
    if not os.path.exists(download_dir):
        print(f"❌ 下载目录不存在: {download_dir}")
        return
    
    print(f"✅ 下载目录存在: {download_dir}")
    
    # 列出所有文件
    try:
        files = os.listdir(download_dir)
        print(f"📁 目录中共有 {len(files)} 个文件")
        
        # 分类统计
        bottle_files = []
        temp_files = []
        other_files = []
        
        for filename in files:
            if filename.startswith("bottle_voice_"):
                bottle_files.append(filename)
            elif any(filename.startswith(prefix) for prefix in ["temp_", "dice_proof_", "temp_voice_", "temp_segment_"]):
                temp_files.append(filename)
            else:
                other_files.append(filename)
        
        print(f"🍾 漂流瓶文件: {len(bottle_files)} 个")
        print(f"🗑️ 临时文件: {len(temp_files)} 个")
        print(f"📄 其他文件: {len(other_files)} 个")
        
        # 显示漂流瓶文件详情
        if bottle_files:
            print(f"\n📋 漂流瓶文件详情:")
            for filename in bottle_files[:10]:  # 只显示前10个
                file_path = os.path.join(download_dir, filename)
                try:
                    stat = os.stat(file_path)
                    size = stat.st_size
                    mtime = datetime.fromtimestamp(stat.st_mtime)
                    age = datetime.now() - mtime
                    
                    print(f"  📁 {filename}")
                    print(f"     大小: {size} 字节")
                    print(f"     修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"     文件年龄: {age}")
                    print()
                except Exception as e:
                    print(f"  ❌ 无法获取文件信息: {filename}, 错误: {str(e)}")
            
            if len(bottle_files) > 10:
                print(f"  ... 还有 {len(bottle_files) - 10} 个文件未显示")
        
    except Exception as e:
        print(f"❌ 无法读取目录内容: {str(e)}")


def analyze_cleanup_rules():
    """分析清理规则"""
    print("\n🧹 分析文件清理规则")
    print("=" * 60)
    
    print("📋 当前清理规则:")
    print("1. 音频文件 (.ogg, .mp3, .wav):")
    print("   - 清理条件: 以 temp_, dice_proof_, temp_voice_, temp_segment_ 开头")
    print("   - 清理条件: 包含 _compressed")
    print("   - ✅ 排除条件: 以 bottle_voice_ 开头 (已修复)")
    print("   - 时间条件: 超过1小时未使用")
    
    print("\n2. 图片文件 (.jpg, .jpeg, .png):")
    print("   - 清理条件: 以 dice_proof_, temp_ 开头")
    print("   - 清理条件: 包含 _compressed 或以 dice_proof_frame_ 开头")
    
    print("\n3. 视频文件 (.mp4, .avi, .mov):")
    print("   - 清理条件: 以 dice_proof_video_, temp_ 开头")
    print("   - 清理条件: 包含 _ 且有数字")
    
    print("\n🛡️ 漂流瓶文件保护:")
    print("✅ bottle_voice_ 开头的文件现在受到保护")
    print("✅ 只有漂流瓶自己的清理机制会删除这些文件")
    print("✅ 清理条件: 7天过期或流程完成")


def simulate_cleanup_check():
    """模拟清理检查"""
    print("\n🔬 模拟文件清理检查")
    print("=" * 60)
    
    test_files = [
        "bottle_voice_123456_1640995200_abc123.ogg",
        "temp_voice_123456_1640995200.ogg", 
        "dice_proof_123456.ogg",
        "temp_segment_123456.wav",
        "normal_audio_compressed.mp3",
        "user_upload_123456.ogg",
        "bottle_voice_789012_1640995200_def456.ogg"
    ]
    
    print("📋 测试文件清理判断:")
    
    for filename in test_files:
        should_delete = False
        reason = "不清理"
        
        # 模拟清理逻辑
        if filename.endswith((".ogg", ".mp3", ".wav")):
            if (filename.startswith("temp_") or
                filename.startswith("dice_proof_") or
                filename.startswith("temp_voice_") or
                filename.startswith("temp_segment_") or
                "_compressed" in filename):
                # 排除漂流瓶相关文件
                if not filename.startswith("bottle_voice_"):
                    should_delete = True
                    reason = "匹配临时文件规则"
                else:
                    reason = "漂流瓶文件，受保护"
        
        status = "🗑️ 会被清理" if should_delete else "✅ 受保护"
        print(f"  {status} {filename}")
        print(f"     原因: {reason}")


def check_bottle_file_format():
    """检查漂流瓶文件格式"""
    print("\n📝 漂流瓶文件命名格式检查")
    print("=" * 60)
    
    pattern = r"bottle_voice_(\d+)_(\d+)_([a-f0-9]{8})\.ogg"
    
    print(f"📋 标准格式: bottle_voice_{{user_id}}_{{timestamp}}_{{uuid}}.ogg")
    print(f"📋 正则表达式: {pattern}")
    
    test_names = [
        "bottle_voice_123456_1640995200_abc12345.ogg",  # 正确格式
        "bottle_voice_789012_1640995200_def45678.ogg",  # 正确格式
        "bottle_voice_123456_invalid_abc12345.ogg",     # 错误格式
        "temp_voice_123456_1640995200.ogg",             # 临时文件
        "bottle_voice_123456.ogg",                      # 格式不完整
    ]
    
    print(f"\n🧪 格式验证测试:")
    for name in test_names:
        match = re.match(pattern, name)
        if match:
            user_id, timestamp, uuid_part = match.groups()
            try:
                dt = datetime.fromtimestamp(int(timestamp))
                print(f"  ✅ {name}")
                print(f"     用户ID: {user_id}")
                print(f"     时间戳: {timestamp} ({dt.strftime('%Y-%m-%d %H:%M:%S')})")
                print(f"     UUID: {uuid_part}")
            except ValueError:
                print(f"  ❌ {name} (时间戳无效)")
        else:
            print(f"  ❌ {name} (格式不匹配)")


def check_bottle_lifecycle():
    """检查漂流瓶生命周期"""
    print("\n🔄 漂流瓶生命周期检查")
    print("=" * 60)
    
    print("📋 漂流瓶状态流转:")
    print("1. waiting    -> 等待投递")
    print("2. delivered  -> 已投递给用户")
    print("3. viewed     -> 已被查看")
    print("4. replied    -> 已被回复")
    print("5. completed  -> 流程完成")
    print("6. rejected   -> 被拒绝查看")
    print("7. expired    -> 已过期")
    
    print("\n🗑️ 文件清理时机:")
    print("✅ 状态为 completed (流程完成)")
    print("✅ 状态为 expired (7天过期)")
    print("✅ 语音文件丢失时标记为 rejected")
    
    print("\n⏰ 清理频率:")
    print("🔄 每5分钟执行一次清理检查")
    print("📊 每小时记录一次统计信息")


def main():
    """主函数"""
    print("🍾 漂流瓶文件诊断工具")
    print("=" * 80)
    print("用途: 检查漂流瓶文件的存储状态和清理机制")
    print("=" * 80)
    
    check_download_directory()
    analyze_cleanup_rules()
    simulate_cleanup_check()
    check_bottle_file_format()
    check_bottle_lifecycle()
    
    print("\n" + "=" * 80)
    print("🎯 诊断总结:")
    print("1. ✅ 漂流瓶文件现在受到保护，不会被定期清理删除")
    print("2. ✅ 只有漂流瓶自己的清理机制会管理这些文件")
    print("3. ✅ 文件在7天后或流程完成后会被自动清理")
    print("4. ✅ 如果遇到'漂流瓶不存在'错误，请检查:")
    print("   - 漂流瓶是否在内存池中 (重启会清空)")
    print("   - 语音文件是否存在于downloads目录")
    print("   - 文件权限是否正确")
    print("=" * 80)


if __name__ == "__main__":
    main()
