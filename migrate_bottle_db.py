#!/usr/bin/env python3
"""
漂流瓶数据库迁移脚本
添加 last_delivered_at 字段到现有的 drift_bottles 表
"""

import asyncio
import aiosqlite
from config import DB_PATH
from logging_config import logger


async def migrate_bottle_database():
    """迁移漂流瓶数据库，添加新字段"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 检查字段是否已存在
            cursor = await conn.execute("PRAGMA table_info(drift_bottles)")
            columns = await cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'last_delivered_at' not in column_names:
                logger.info("添加 last_delivered_at 字段到 drift_bottles 表")
                await conn.execute('ALTER TABLE drift_bottles ADD COLUMN last_delivered_at TEXT')
                await conn.commit()
                logger.info("成功添加 last_delivered_at 字段")
            else:
                logger.info("last_delivered_at 字段已存在，跳过迁移")
                
            # 更新现有的已投递瓶子的 last_delivered_at 字段
            # 使用 delivery_history 中的最后一次投递时间
            cursor = await conn.execute("""
                SELECT id, delivery_history FROM drift_bottles 
                WHERE state = 'delivered' AND last_delivered_at IS NULL
            """)
            bottles_to_update = await cursor.fetchall()
            
            updated_count = 0
            for bottle_id, delivery_history_json in bottles_to_update:
                try:
                    import json
                    delivery_history = json.loads(delivery_history_json) if delivery_history_json else []
                    if delivery_history:
                        # 获取最后一次投递时间
                        last_delivery = delivery_history[-1]
                        delivered_at = last_delivery.get('delivered_at')
                        if delivered_at:
                            await conn.execute("""
                                UPDATE drift_bottles 
                                SET last_delivered_at = ? 
                                WHERE id = ?
                            """, (delivered_at, bottle_id))
                            updated_count += 1
                except Exception as e:
                    logger.error(f"更新漂流瓶 {bottle_id} 的 last_delivered_at 失败: {str(e)}")
            
            if updated_count > 0:
                await conn.commit()
                logger.info(f"成功更新 {updated_count} 个漂流瓶的 last_delivered_at 字段")
            
            logger.info("漂流瓶数据库迁移完成")
            
    except Exception as e:
        logger.error(f"漂流瓶数据库迁移失败: {str(e)}")
        raise


async def main():
    """主函数"""
    logger.info("开始漂流瓶数据库迁移...")
    await migrate_bottle_database()
    logger.info("漂流瓶数据库迁移完成")


if __name__ == "__main__":
    asyncio.run(main())
