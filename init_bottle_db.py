#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
漂流瓶数据库初始化脚本
"""

import asyncio
import aiosqlite
import os

# 简化的配置
DB_PATH = "user_data.db"


async def init_bottle_database():
    """初始化漂流瓶数据库表"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 创建漂流瓶表
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS drift_bottles (
                    id TEXT PRIMARY KEY,
                    sender_id TEXT NOT NULL,
                    voice_message_id INTEGER NOT NULL,
                    chat_id TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    state TEXT DEFAULT 'waiting',
                    delivery_count INTEGER DEFAULT 0,
                    max_deliveries INTEGER DEFAULT 3,
                    current_recipient TEXT,
                    delivery_history TEXT DEFAULT '[]',
                    reply_message_id INTEGER,
                    reply_chat_id TEXT,
                    replied_by TEXT,
                    expires_at TEXT NOT NULL
                )
            ''')
            
            # 创建索引以提高查询性能
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_bottles_state 
                ON drift_bottles(state)
            ''')
            
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_bottles_sender 
                ON drift_bottles(sender_id)
            ''')
            
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_bottles_recipient 
                ON drift_bottles(current_recipient)
            ''')
            
            await conn.execute('''
                CREATE INDEX IF NOT EXISTS idx_drift_bottles_expires 
                ON drift_bottles(expires_at)
            ''')
            
            await conn.commit()
            print("漂流瓶数据库表初始化完成")

    except Exception as e:
        print(f"初始化漂流瓶数据库失败: {str(e)}")
        raise


async def check_bottle_table_structure():
    """检查漂流瓶表结构"""
    try:
        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            cursor = await conn.execute("PRAGMA table_info(drift_bottles)")
            columns = await cursor.fetchall()
            
            print("漂流瓶表结构:")
            print("-" * 60)
            for column in columns:
                print(f"  {column[1]:<20} {column[2]:<15} {'NOT NULL' if column[3] else 'NULL':<10} {f'DEFAULT {column[4]}' if column[4] else ''}")
            
            # 检查索引
            cursor = await conn.execute("PRAGMA index_list(drift_bottles)")
            indexes = await cursor.fetchall()
            
            print(f"\n索引信息:")
            print("-" * 60)
            for index in indexes:
                print(f"  {index[1]}")
                
    except Exception as e:
        print(f"检查表结构失败: {str(e)}")


async def migrate_old_data():
    """迁移旧数据（如果需要）"""
    try:
        # 这里可以添加从旧格式迁移到新格式的逻辑
        # 例如：从文件系统迁移到数据库
        print("数据迁移检查完成")

    except Exception as e:
        print(f"数据迁移失败: {str(e)}")


async def test_bottle_operations():
    """测试漂流瓶数据库操作"""
    try:
        # 简化测试，直接使用SQL操作
        from datetime import datetime, timedelta
        import uuid
        import json
        
        # 创建测试漂流瓶
        test_bottle = {
            'id': str(uuid.uuid4()),
            'sender_id': 'test_user_123',
            'voice_message_id': 12345,
            'chat_id': 'test_chat_123',
            'created_at': datetime.now().isoformat(),
            'state': 'waiting',
            'delivery_count': 0,
            'max_deliveries': 3,
            'current_recipient': None,
            'delivery_history': '[]',
            'reply_message_id': None,
            'reply_chat_id': None,
            'replied_by': None,
            'expires_at': (datetime.now() + timedelta(weeks=1)).isoformat()
        }
        
        print("测试漂流瓶数据库操作:")
        print("-" * 60)

        async with aiosqlite.connect(DB_PATH, timeout=30) as conn:
            # 测试保存
            await conn.execute("""
                INSERT INTO drift_bottles (
                    id, sender_id, voice_message_id, chat_id, created_at,
                    state, delivery_count, max_deliveries, current_recipient,
                    delivery_history, reply_message_id, reply_chat_id,
                    replied_by, expires_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                test_bottle['id'], test_bottle['sender_id'], test_bottle['voice_message_id'],
                test_bottle['chat_id'], test_bottle['created_at'], test_bottle['state'],
                test_bottle['delivery_count'], test_bottle['max_deliveries'],
                test_bottle['current_recipient'], test_bottle['delivery_history'],
                test_bottle['reply_message_id'], test_bottle['reply_chat_id'],
                test_bottle['replied_by'], test_bottle['expires_at']
            ))
            await conn.commit()
            print(f"✅ 保存漂流瓶: {test_bottle['id']}")

            # 测试获取
            cursor = await conn.execute("SELECT * FROM drift_bottles WHERE id = ?", (test_bottle['id'],))
            retrieved = await cursor.fetchone()
            if retrieved:
                print(f"✅ 获取漂流瓶: {retrieved[0]}")
            else:
                print("❌ 获取漂流瓶失败")
                return

            # 测试更新
            await conn.execute("""
                UPDATE drift_bottles
                SET state = ?, current_recipient = ?, delivery_count = ?
                WHERE id = ?
            """, ('delivered', 'test_recipient_456', 1, test_bottle['id']))
            await conn.commit()
            print(f"✅ 更新漂流瓶状态")

            # 验证更新
            cursor = await conn.execute("SELECT state, current_recipient FROM drift_bottles WHERE id = ?", (test_bottle['id'],))
            updated = await cursor.fetchone()
            if updated and updated[0] == 'delivered':
                print(f"✅ 验证更新成功: 状态={updated[0]}, 接收者={updated[1]}")
            else:
                print("❌ 更新验证失败")

            # 清理测试数据
            await conn.execute("DELETE FROM drift_bottles WHERE id = ?", (test_bottle['id'],))
            await conn.commit()
            print(f"✅ 清理测试数据")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


async def main():
    """主函数"""
    print("🍾 漂流瓶数据库初始化工具")
    print("=" * 80)
    
    try:
        # 初始化数据库
        await init_bottle_database()
        
        # 检查表结构
        await check_bottle_table_structure()
        
        # 迁移旧数据
        await migrate_old_data()
        
        # 测试数据库操作
        await test_bottle_operations()
        
        print("\n" + "=" * 80)
        print("✅ 漂流瓶数据库初始化完成！")
        print("现在可以使用新的转发消息模式了。")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ 初始化失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
