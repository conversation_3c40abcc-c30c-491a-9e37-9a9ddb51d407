import logging
import os
from logging.handlers import TimedRotatingFileHandler
import gzip
import shutil

class CompressedTimedRotatingFileHandler(TimedRotatingFileHandler):
    def doRollover(self):
        super().doRollover()
        # 压缩旧日志文件，仅处理未压缩的文件
        for old_log in [f for f in os.listdir('.') if f.startswith('master_bot.log.') and not f.endswith('.gz')]:
            old_log_path = old_log
            compressed_path = f"{old_log_path}.gz"
            try:
                with open(old_log_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                os.remove(old_log_path)
                logger.info(f"压缩旧日志文件: {compressed_path}")
            except Exception as e:
                logger.error(f"压缩日志文件 {old_log_path} 失败: {str(e)}")

logger = logging.getLogger()
logger.setLevel(logging.INFO)

log_file = "master_bot.log"
handler = CompressedTimedRotatingFileHandler(
    filename=log_file,
    when="midnight",
    interval=1,
    backupCount=30,
    encoding="utf-8"
)
handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s - %(pathname)s:%(lineno)d'))
logger.addHandler(handler)

console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s - %(pathname)s:%(lineno)d'))
logger.addHandler(console_handler)

logging.getLogger("telegram").setLevel(logging.WARNING)
logging.getLogger("httpx").setLevel(logging.WARNING)

logger.info("日志系统初始化完成，当前工作目录: %s", os.getcwd())