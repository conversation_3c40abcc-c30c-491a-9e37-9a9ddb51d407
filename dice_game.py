import os
from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.error import BadRequest
from telegram.ext import ContextTypes
from database import get_user_data, save_user_data
from logging_config import logger
from config import MASTER_ID
from utils import get_main_menu, safe_edit_message
from ai_dialogue import download_and_process_image, call_vllm_minicpm_api
import random

async def verify_dice_proof_image(bot, file_id: str, user_id: str, current_clothes: int, previous_clothes: int, round_num: int, caption: str = "") -> tuple[bool, str, bool]:
    """
    验证骰子游戏证明图片 - 优化版
    优先使用对话API的第一个多模态API，失败则直接通过

    Args:
        bot: Telegram bot实例
        file_id: 图片文件ID
        user_id: 用户ID
        current_clothes: 当前轮次应有衣物数量（用于逻辑判断）
        previous_clothes: 上轮衣物数量
        round_num: 当前轮次
        caption: 图片描述

    Returns:
        tuple[bool, str, bool]: (是否通过验证, 验证结果消息, 是否全脱光)
    """
    from config import AI_API_CONFIGS, DOWNLOAD_DIR
    from ai_dialogue.api_manager import should_use_break_limit, load_break_limit_conversations
    from openai import OpenAI
    import base64
    from datetime import datetime
    
    # 创建临时文件路径
    temp_file_path = os.path.join(DOWNLOAD_DIR, f"dice_proof_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    
    try:
        # 下载图片
        file = await bot.get_file(file_id)
        await file.download_to_drive(temp_file_path)
        logger.info(f"用户 {user_id} 骰子游戏证明图片下载成功: {temp_file_path}")

        # 读取图片并转换为 base64
        def encode_image(image_path):
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')

        base64_image = encode_image(temp_file_path)

        # 查找第一个多模态API
        multimodal_api = None
        for api_config in AI_API_CONFIGS:
            if api_config.get("is_multimodal", False):
                multimodal_api = api_config
                break

        if not multimodal_api:
            logger.warning(f"用户 {user_id} 骰子游戏图片验证：没有找到多模态API，直接通过")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        logger.info(f"用户 {user_id} 骰子游戏使用多模态API: {multimodal_api['name']} ({multimodal_api['model']})")

        # 初始化API客户端
        client = OpenAI(
            api_key=multimodal_api["key"],
            base_url=multimodal_api["url"].replace("/chat/completions", "")
        )

        # 简化的审核提示，只关注两个要点
        verification_prompt = f"""
请分析这张图片并严格按照格式回答以下两个问题：

1. 图片中是否有一个真实人类正面跪着面对镜头？（膝盖着地或类似跪姿，可以不显示面部）
2. 这个人身上还剩几件衣服？（请仔细数一数：袜子算2件，上衣1件，裤子/短裤1件，内裤1件，总共最多5件）

请严格按照以下格式回答，使用中文冒号，不要添加任何其他内容：
人类跪姿：是/否
剩余衣服：X件（X必须是0-5之间的数字）

示例格式：
人类跪姿：是
剩余衣服：3件
"""

        # 准备消息内容
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": verification_prompt},
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    }
                ]
            }
        ]

        # 检查是否需要使用破限对话
        if should_use_break_limit(multimodal_api):
            full_scenario_content = load_break_limit_conversations()
            if full_scenario_content:
                logger.info(f"用户 {user_id} 骰子游戏多模态API启用破限模式")
                # 在用户消息前添加系统消息
                scenario_system_message = {
                    "role": "system",
                    "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

{full_scenario_content}

现在请按照上述配置处理用户的图片验证请求。"""
                }
                messages.insert(0, scenario_system_message)

        # 调用多模态API进行图片审核
        response = client.chat.completions.create(
            model=multimodal_api["model"],
            messages=messages,
            temperature=0.1,  # 降低温度以获得更一致的审核结果
            max_tokens=multimodal_api.get("max_tokens", 512)
        )

        # 添加健壮的响应检查 - 失败时直接通过
        if not response:
            logger.warning(f"用户 {user_id} 骰子游戏图片识别API返回空响应对象，直接通过")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        if not hasattr(response, 'choices') or not response.choices:
            logger.warning(f"用户 {user_id} 骰子游戏图片识别API响应缺少choices字段，直接通过，响应: {response}")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        if len(response.choices) == 0:
            logger.warning(f"用户 {user_id} 骰子游戏图片识别API choices列表为空，直接通过")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        first_choice = response.choices[0]
        if not first_choice or not hasattr(first_choice, 'message'):
            logger.warning(f"用户 {user_id} 骰子游戏图片识别API第一个choice缺少message字段，直接通过，choice: {first_choice}")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        if not first_choice.message or not hasattr(first_choice.message, 'content'):
            logger.warning(f"用户 {user_id} 骰子游戏图片识别API message缺少content字段，直接通过，message: {first_choice.message}")
            return True, "图片识别系统不可用，直接通过审核", current_clothes == 0

        analysis_result = first_choice.message.content
        logger.info(f"用户 {user_id} 骰子游戏图片识别结果: {analysis_result}")
        
        # 解析识别结果 - 增强容错性
        lines = analysis_result.strip().split('\n')
        is_kneeling = False
        detected_clothes = -1  # -1表示识别失败
        is_human = True  # 默认为人类

        # 记录原始AI输出用于调试
        logger.debug(f"用户 {user_id} AI原始输出: {analysis_result}")

        for line in lines:
            line = line.strip()
            # 支持中文冒号和英文冒号
            if '：' in line or ':' in line:
                # 优先使用中文冒号分割，如果没有则使用英文冒号
                if '：' in line:
                    key, value = line.split('：', 1)
                else:
                    key, value = line.split(':', 1)

                key = key.strip()
                value = value.strip()

                # 使用更灵活的键名匹配
                if "跪姿" in key or "kneeling" in key.lower():
                    if "否" in value or "no" in value.lower() or "false" in value.lower():
                        is_human = False  # 标记为非人类
                    elif "是" in value or "yes" in value.lower() or "true" in value.lower():
                        is_kneeling = True
                elif "衣服" in key or "clothes" in key.lower() or "clothing" in key.lower():
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', value)
                    if numbers:
                        detected_clothes = int(numbers[0])
                        # 确保在有效范围内
                        if detected_clothes < 0:
                            detected_clothes = 0
                        elif detected_clothes > 5:
                            detected_clothes = 5
                        logger.debug(f"用户 {user_id} 解析到衣物数量: {detected_clothes}")

        # 记录解析结果
        logger.debug(f"用户 {user_id} 解析结果: 是否人类={is_human}, 跪姿={is_kneeling}, 衣物={detected_clothes}")

        # 新的验证逻辑
        verification_passed = False
        reason = ""
        game_should_end = False
        
        # 1. 如果识别为非人类，直接否决
        if not is_human:
            verification_passed = False
            reason = "验证失败！检测到不是人类跪着正对屏幕"
        
        # 2. 如果识别出错，直接通过审核
        elif detected_clothes == -1:
            verification_passed = True
            reason = "识别出错，直接通过审核"
        
        # 3. 正常验证逻辑
        else:
            # 检查是否全脱光（0件衣物）
            if detected_clothes == 0:
                verification_passed = True
                game_should_end = True
                reason = f"验证通过！检测到：跪姿正确，全脱光状态（0件衣物）"
            
            # 检查跪姿和衣物减少
            elif is_kneeling and detected_clothes < previous_clothes:
                verification_passed = True
                reason = f"验证通过！检测到：跪姿正确，衣物从{previous_clothes}件减少到{detected_clothes}件"
            
            # 验证失败的情况
            else:
                verification_passed = False
                reason_parts = []
                if not is_kneeling:
                    reason_parts.append("未检测到人类跪姿")
                if detected_clothes >= previous_clothes:
                    reason_parts.append(f"衣物未减少（检测到{detected_clothes}件，上轮{previous_clothes}件）")
                reason = f"验证失败！问题：{' + '.join(reason_parts) if reason_parts else '未知错误'}"
        
        # 记录详细的识别结果和游戏状态到日志
        logger.info(f"简化版骰子游戏AI识别结果 - 用户ID: {user_id}, 轮次: {round_num}, "
                   f"是否人类: {is_human}, 检测跪姿: {is_kneeling}, 检测衣物: {detected_clothes}件, 上轮衣物: {previous_clothes}件, "
                   f"验证通过: {verification_passed}, 游戏结束: {game_should_end}, 原因: {reason}")
        
        # 转发识别结果给管理员
        try:
            admin_report = (
                f"🎲 简化版骰子游戏AI识别报告 🎲\n"
                f"───────────────────\n"
                f"用户ID: {user_id}\n"
                f"游戏轮次: 第{round_num}轮\n"
                + (f"用户说明: {caption}\n" if caption.strip() else "") +
                f"识别结果:\n"
                f"  • 是否人类: {'✅是' if is_human else '❌否'}\n"
                f"  • 人类跪姿: {'✅是' if is_kneeling else '❌否'}\n"
                f"  • 剩余衣服: {detected_clothes}件\n"
                f"  • 上轮衣物: {previous_clothes}件\n"
                f"逻辑判断:\n"
                f"  • 衣物减少: {'✅是' if detected_clothes < previous_clothes else '❌否' if detected_clothes != -1 else '❌识别失败'}\n"
                f"  • 全脱光: {'✅是' if detected_clothes == 0 else '❌否'}\n"
                f"  • 游戏结束: {'✅是' if game_should_end else '❌否'}\n"
                f"最终判定: {'✅通过' if verification_passed else '❌不通过'}\n"
                f"原因: {reason}\n"
                f"───────────────────"
            )
            await bot.send_message(chat_id=MASTER_ID, text=admin_report, parse_mode=None)
            logger.info(f"用户 {user_id} 简化版骰子游戏识别结果已转发给管理员")
        except Exception as e:
            logger.error(f"转发用户 {user_id} 简化版骰子游戏识别结果给管理员失败: {str(e)}")
        
        return verification_passed, reason, game_should_end

    except Exception as e:
        logger.error(f"用户 {user_id} 骰子游戏图片审核失败: {str(e)}，直接通过", exc_info=True)
        return True, "图片识别系统不可用，直接通过审核", current_clothes == 0
    finally:
        # 删除本地文件以保护隐私
        if os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"用户 {user_id} 骰子游戏证明图片已删除: {temp_file_path}")
            except Exception as e:
                logger.error(f"用户 {user_id} 删除骰子游戏证明图片失败: {temp_file_path}, 错误: {str(e)}")

async def verify_dice_proof_video(bot, file_id: str, user_id: str, current_clothes: int, previous_clothes: int, round_num: int, caption: str = "") -> tuple[bool, str, bool]:
    """
    验证骰子游戏证明视频 - 优化版
    优先使用对话API的第一个多模态API，失败则直接通过

    Args:
        bot: Telegram bot实例
        file_id: 视频文件ID
        user_id: 用户ID
        current_clothes: 当前轮次应有衣物数量（用于逻辑判断）
        previous_clothes: 上轮衣物数量
        round_num: 当前轮次
        caption: 视频描述

    Returns:
        tuple[bool, str, bool]: (是否通过验证, 验证结果消息, 是否全脱光)
    """
    from config import AI_API_CONFIGS, DOWNLOAD_DIR
    from ai_dialogue.api_manager import should_use_break_limit, load_break_limit_conversations
    from openai import OpenAI
    import base64
    from datetime import datetime
    import tempfile
    
    try:
        import cv2
    except ImportError:
        logger.warning(f"用户 {user_id} 视频验证失败：cv2模块未安装，视频验证直接通过")
        return True, "视频识别系统暂时不可用，直接通过审核", False
    
    # 创建临时文件路径
    temp_video_path = os.path.join(DOWNLOAD_DIR, f"dice_proof_video_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4")
    temp_frame_paths = []  # 初始化帧文件路径列表
    os.makedirs(DOWNLOAD_DIR, exist_ok=True)
    
    try:
        # 下载视频
        file = await bot.get_file(file_id)
        await file.download_to_drive(temp_video_path)
        logger.info(f"用户 {user_id} 骰子游戏证明视频下载成功: {temp_video_path}")

        # 从视频中提取3个关键帧进行分析
        cap = cv2.VideoCapture(temp_video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {temp_video_path}")
            return False, "视频文件损坏或格式不支持", False
        
        # 获取视频信息
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        if total_frames <= 0:
            cap.release()
            logger.error(f"视频帧数无效: {total_frames}")
            return False, "视频帧数无效", False
        
        # 计算10个关键帧的位置：均匀分布在视频中
        frame_positions = []
        if total_frames == 1:
            frame_positions = [0] * 10  # 如果只有1帧，重复使用
        elif total_frames <= 10:
            # 如果帧数不足10帧，使用所有帧并重复最后一帧
            frame_positions = list(range(total_frames))
            while len(frame_positions) < 10:
                frame_positions.append(frame_positions[-1])
        else:
            # 均匀分布10个关键帧：5%, 15%, 25%, 35%, 45%, 55%, 65%, 75%, 85%, 95%
            percentages = [0.05, 0.15, 0.25, 0.35, 0.45, 0.55, 0.65, 0.75, 0.85, 0.95]
            frame_positions = [int(total_frames * p) for p in percentages]
            frame_positions = [min(pos, total_frames - 1) for pos in frame_positions]

        logger.info(f"用户 {user_id} 骰子游戏视频关键帧提取计划: 总帧数={total_frames}, 时长={duration:.1f}s, 10个关键帧位置={frame_positions}")

        # 查找第一个多模态API
        multimodal_api = None
        for api_config in AI_API_CONFIGS:
            if api_config.get("is_multimodal", False):
                multimodal_api = api_config
                break

        if not multimodal_api:
            cap.release()
            logger.warning(f"用户 {user_id} 骰子游戏视频验证：没有找到多模态API，直接通过")
            return True, "视频识别系统不可用，直接通过审核", current_clothes == 0

        logger.info(f"用户 {user_id} 骰子游戏视频使用多模态API: {multimodal_api['name']} ({multimodal_api['model']})")

        # 提取10个关键帧并准备一次性多图分析
        temp_frame_paths = []
        frame_labels = []

        for i, pos in enumerate(frame_positions):
            cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
            ret, frame = cap.read()

            if ret:
                # 保存提取的帧
                frame_label = f"第{i+1}帧({pos/total_frames*100:.0f}%)"
                temp_frame_path = os.path.join(DOWNLOAD_DIR, f"dice_proof_frame_{user_id}_{i+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
                cv2.imwrite(temp_frame_path, frame)
                temp_frame_paths.append(temp_frame_path)
                frame_labels.append(frame_label)
                logger.debug(f"用户 {user_id} 成功提取{frame_label}: 位置{pos}")
            else:
                logger.warning(f"用户 {user_id} 无法提取第{i+1}个关键帧(位置{pos})")

        cap.release()

        if not temp_frame_paths:
            logger.error(f"用户 {user_id} 无法提取任何关键帧: {temp_video_path}")
            return False, "无法从视频中提取关键帧进行分析", False

        logger.info(f"用户 {user_id} 成功提取{len(temp_frame_paths)}个关键帧，准备一次性多图分析")
                
        try:
            # 读取所有帧并转换为 base64
            def encode_image(image_path):
                with open(image_path, "rb") as image_file:
                    return base64.b64encode(image_file.read()).decode('utf-8')

            # 准备所有图片的base64编码
            base64_images = []
            for frame_path in temp_frame_paths:
                base64_images.append(encode_image(frame_path))

            # 初始化多模态API客户端
            client = OpenAI(
                api_key=multimodal_api["key"],
                base_url=multimodal_api["url"].replace("/chat/completions", "")
            )

            # 一次性多图分析提示词
            verification_prompt = f"""
请分析这{len(temp_frame_paths)}张从骰子游戏证明视频中提取的关键帧图片，这些图片按时间顺序排列，展示了视频的完整过程。

请综合分析所有图片并回答以下问题：

1. 这些图片中是否显示了一个真实人类正面跪着面对镜头？（膝盖着地或类似跪姿，可以不显示面部）
2. 综合所有图片，这个人身上平均还剩几件衣服？（请仔细数一数：袜子算2件，上衣1件，裤子/短裤1件，内裤1件，总共最多5件）

注意：
- 请综合考虑所有图片的信息，而不是单独分析每一张
- 如果不同图片显示的衣物数量不同，请给出一个合理的平均值
- 重点关注整体的跪姿表现和衣物状态

请严格按照以下格式回答，使用中文冒号，不要添加任何其他内容：
人类跪姿：是/否
剩余衣服：X件（X必须是0-5之间的数字）

示例格式：
人类跪姿：是
剩余衣服：3件
"""

            # 准备消息内容 - 包含文本和所有图片
            content = [{"type": "text", "text": verification_prompt}]

            # 添加所有图片到消息内容
            for i, base64_image in enumerate(base64_images):
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                })

            messages = [{"role": "user", "content": content}]

            # 检查是否需要使用破限对话
            if should_use_break_limit(multimodal_api):
                full_scenario_content = load_break_limit_conversations()
                if full_scenario_content:
                    logger.info(f"用户 {user_id} 骰子游戏视频多图分析启用破限模式")
                    # 在用户消息前添加系统消息
                    scenario_system_message = {
                        "role": "system",
                        "content": f"""以下是完整的破限配置文件，请严格按照此配置执行：

{full_scenario_content}

现在请按照上述配置处理用户的多图片验证请求。"""
                    }
                    messages.insert(0, scenario_system_message)

            # 调用多模态API进行一次性多图分析
            logger.info(f"用户 {user_id} 开始一次性多图分析，包含{len(base64_images)}张图片")
            response = client.chat.completions.create(
                model=multimodal_api["model"],
                messages=messages,
                temperature=0.1,
                max_tokens=multimodal_api.get("max_tokens", 1024)  # 增加token限制以支持多图分析
            )

            # 添加健壮的响应检查
            if not response:
                logger.warning(f"用户 {user_id} 骰子游戏视频多图分析API返回空响应对象")
                return True, "视频识别API出错，直接通过审核", False

            if not hasattr(response, 'choices') or not response.choices:
                logger.warning(f"用户 {user_id} 骰子游戏视频多图分析API响应缺少choices字段，响应: {response}")
                return True, "视频识别API响应格式错误，直接通过审核", False

            if len(response.choices) == 0:
                logger.warning(f"用户 {user_id} 骰子游戏视频多图分析API choices列表为空")
                return True, "视频识别API无有效响应，直接通过审核", False

            first_choice = response.choices[0]
            if not first_choice or not hasattr(first_choice, 'message'):
                logger.warning(f"用户 {user_id} 骰子游戏视频多图分析API第一个choice缺少message字段，choice: {first_choice}")
                return True, "视频识别API响应结构错误，直接通过审核", False

            if not first_choice.message or not hasattr(first_choice.message, 'content'):
                logger.warning(f"用户 {user_id} 骰子游戏视频多图分析API message缺少content字段，message: {first_choice.message}")
                return True, "视频识别API消息格式错误，直接通过审核", False

            analysis_result = first_choice.message.content
            logger.info(f"用户 {user_id} 骰子游戏视频多图分析结果: {analysis_result}")
                
            # 解析多图分析结果 - 增强容错性
            lines = analysis_result.strip().split('\n')
            is_kneeling = False
            detected_clothes = -1
            is_human = True

            # 记录原始AI输出用于调试
            logger.debug(f"用户 {user_id} 视频多图分析AI原始输出: {analysis_result}")

            for line in lines:
                line = line.strip()
                # 支持中文冒号和英文冒号
                if '：' in line or ':' in line:
                    # 优先使用中文冒号分割，如果没有则使用英文冒号
                    if '：' in line:
                        key, value = line.split('：', 1)
                    else:
                        key, value = line.split(':', 1)

                    key = key.strip()
                    value = value.strip()

                    # 使用更灵活的键名匹配
                    if "跪姿" in key or "kneeling" in key.lower():
                        if "否" in value or "no" in value.lower() or "false" in value.lower():
                            is_human = False
                        elif "是" in value or "yes" in value.lower() or "true" in value.lower():
                            is_kneeling = True
                    elif "衣服" in key or "clothes" in key.lower() or "clothing" in key.lower():
                        import re
                        numbers = re.findall(r'\d+', value)
                        if numbers:
                            detected_clothes = int(numbers[0])
                            if detected_clothes < 0:
                                detected_clothes = 0
                            elif detected_clothes > 5:
                                detected_clothes = 5
                            logger.debug(f"用户 {user_id} 视频多图分析解析到衣物数量: {detected_clothes}")

            # 记录解析结果
            logger.debug(f"用户 {user_id} 视频多图分析解析结果: 是否人类={is_human}, 跪姿={is_kneeling}, 衣物={detected_clothes}")

            # 构建分析结果
            analysis_summary = {
                'total_frames': len(temp_frame_paths),
                'is_human': is_human,
                'is_kneeling': is_kneeling,
                'detected_clothes': detected_clothes,
                'frame_labels': frame_labels
            }

            logger.info(f"用户 {user_id} 视频多图分析完成: 人类={is_human}, 跪姿={is_kneeling}, 衣物={detected_clothes}件")
        
            # 基于多图分析结果进行验证
            if detected_clothes == -1:
                logger.warning(f"用户 {user_id} 视频多图分析无法解析衣物数量")
                return True, "视频识别解析失败，直接通过审核", False

            if not is_human:
                logger.warning(f"用户 {user_id} 视频多图分析检测到非人类")
                return False, "视频验证失败！检测到不是人类跪姿", False

            if not is_kneeling:
                logger.warning(f"用户 {user_id} 视频多图分析检测到未跪姿")
                return False, "视频验证失败！未检测到正确的跪姿", False
            # 验证逻辑 - 基于多图分析结果
            verification_passed = False
            reason = ""
            game_should_end = False

            # 检查是否全脱光
            if detected_clothes == 0:
                verification_passed = True
                game_should_end = True
                reason = f"视频验证通过！检测到全脱光状态（0件衣物），跪姿正确"

            # 检查跪姿和衣物减少
            elif is_kneeling and detected_clothes < previous_clothes:
                verification_passed = True
                reason = f"视频验证通过！检测到：跪姿正确，衣物从{previous_clothes}件减少到{detected_clothes}件"

            # 验证失败的情况
            else:
                verification_passed = False
                reason_parts = []
                if not is_kneeling:
                    reason_parts.append("跪姿不正确")
                if detected_clothes >= previous_clothes:
                    reason_parts.append(f"衣物未减少（{detected_clothes}件，上轮{previous_clothes}件）")
                reason = f"视频验证失败！问题：{' + '.join(reason_parts) if reason_parts else '未知错误'}"
        
            # 记录详细的识别结果
            logger.info(f"多图分析骰子游戏视频AI识别结果 - 用户ID: {user_id}, 轮次: {round_num}, "
                       f"分析帧数: {analysis_summary['total_frames']}, 跪姿: {is_kneeling}, "
                       f"衣物数量: {detected_clothes}件, 上轮: {previous_clothes}件, "
                       f"验证通过: {verification_passed}, 游戏结束: {game_should_end}")

            # 转发识别结果给管理员
            try:
                frame_details = "\n".join([
                    f"  • {label}: 已分析"
                    for label in frame_labels
                ])

                admin_report = (
                    f"🎲 多图分析骰子游戏视频AI识别报告 🎲\n"
                    f"───────────────────\n"
                    f"用户ID: {user_id}\n"
                    f"游戏轮次: 第{round_num}轮\n"
                    + (f"用户说明: {caption}\n" if caption.strip() else "") +
                    f"视频信息:\n"
                    f"  • 时长: {duration:.1f}秒\n"
                    f"  • 总帧数: {total_frames}\n"
                    f"  • 分析帧数: {len(temp_frame_paths)}\n"
                    f"  • 关键帧位置: {frame_positions}\n"
                    f"多图分析结果:\n"
                    f"{frame_details}\n"
                    f"综合分析:\n"
                    f"  • 是否人类: {is_human}\n"
                    f"  • 跪姿正确: {is_kneeling}\n"
                    f"  • 衣物数量: {detected_clothes}件\n"
                    f"  • 上轮衣物: {previous_clothes}件\n"
                    f"  • 全脱光: {'✅是' if detected_clothes == 0 else '❌否'}\n"
                    f"  • 游戏结束: {'✅是' if game_should_end else '❌否'}\n"
                    f"最终判定: {'✅通过' if verification_passed else '❌不通过'}\n"
                    f"原因: {reason}\n"
                    f"───────────────────"
                )
                await bot.send_message(chat_id=MASTER_ID, text=admin_report, parse_mode=None)
                logger.info(f"用户 {user_id} 多图分析骰子游戏视频识别结果已转发给管理员")
            except Exception as e:
                logger.error(f"转发用户 {user_id} 多图分析骰子游戏视频识别结果给管理员失败: {str(e)}")

            return verification_passed, reason, game_should_end

        except Exception as e:
            logger.error(f"用户 {user_id} 骰子游戏视频多图分析失败: {str(e)}，直接通过", exc_info=True)
            return True, "视频识别系统不可用，直接通过审核", current_clothes == 0

    except Exception as e:
        logger.error(f"用户 {user_id} 骰子游戏视频处理失败: {str(e)}，直接通过", exc_info=True)
        return True, "视频识别系统不可用，直接通过审核", current_clothes == 0
    finally:
        # 删除本地文件以保护隐私
        for temp_path in [temp_video_path] + temp_frame_paths:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.info(f"用户 {user_id} 骰子游戏临时文件已删除: {temp_path}")
                except Exception as e:
                    logger.error(f"用户 {user_id} 删除骰子游戏临时文件失败: {temp_path}, 错误: {str(e)}")

async def handle_dice(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /dice 命令，执行骰子游戏逻辑"""
    user_id = update.effective_user.id
    chat_id = update.effective_chat.id
    logger.info(f"收到 /dice 命令，用户ID: {user_id}")
    slave_info = f"贱奴供奉：用户 {update.message.from_user.full_name} (ID: {user_id})\n"
    await context.bot.send_message(chat_id=MASTER_ID, text=slave_info + update.message.text, parse_mode=None)
    
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查是否在群组中，并验证用户是否为游戏发起者
    is_group = chat_id != user_id  # 假设私聊时 chat_id 等于 user_id
    if is_group and user_data["dice_round"] == 0:
        await update.message.reply_text(
            "贱奴！你还没开始自己的游戏，滚去'骰子羞辱'菜单启动！"
            if lang == "zh-cn" else
            "Filthy dog! You haven't started your own game, go to the 'Dice Humiliation' menu to start!",
            reply_markup=get_main_menu(user_id),
            reply_to_message_id=update.message.message_id
        )
        logger.info(f"用户 {user_id} 在群组中未开始游戏，被拒绝使用 /dice 命令")
        return

    if user_data["dice_round"] == 0:
        await update.message.reply_text(
            "贱奴，你还没跪下开始！滚去'骰子羞辱'菜单，别在这丢人现眼！"
            if lang == "zh-cn" else
            "Filthy dog, you haven't started yet! Go to the 'Dice Humiliation' menu, don't embarrass yourself here!",
            reply_markup=get_main_menu(user_id),
            reply_to_message_id=update.message.message_id
        )
        logger.info(f"用户 {user_id} 未开始游戏，被拒绝使用 /dice 命令")
        return

    if user_data["waiting_for_proof"]:
        await update.message.reply_text(
            "狗东西！你输了还没献上证明！快拍照片或视频给爷检查，不然滚！"
            if lang == "zh-cn" else
            "You filthy thing! You lost and haven't sent proof! Send a photo or video for inspection, or get lost!",
            reply_to_message_id=update.message.message_id
        )
        logger.info(f"用户 {user_id} 因未提交证明被拒绝继续游戏")
        return

    user_dice_msg = await context.bot.send_dice(chat_id=update.effective_chat.id)
    user_dice = user_dice_msg.dice.value
    bot_dice_msg = await context.bot.send_dice(chat_id=update.effective_chat.id)
    bot_dice = bot_dice_msg.dice.value

    round_num = user_data["dice_round"]
    clothes = user_data["clothes"]

    if user_dice == bot_dice:
        message = (
            f"第{round_num}轮：你掷了{user_dice}，爷掷了{bot_dice}，居然一样！\n"
            f"贱奴，你赢了，赏你自扇一巴掌给爷听响！快点，别让爷失望！"
            if lang == "zh-cn" else
            f"Round {round_num}: You rolled {user_dice}, I rolled {bot_dice}, it's a tie!\n"
            f"Filthy dog, you win, slap yourself for me to hear! Hurry, don't disappoint me!"
        )
        user_data["waiting_for_proof"] = False
    else:
        # 保存当前衣物数量作为下一轮的previous_clothes
        user_data["previous_clothes"] = clothes
        clothes -= 1
        user_data["clothes"] = clothes
        message = (
            f"第{round_num}轮：你掷了{user_dice}，爷掷了{bot_dice}，你输了！\n"
            f"脱一件破布，还剩{clothes}件，快拍照片或视频给爷检查，不然爷抽死你！"
            if lang == "zh-cn" else
            f"Round {round_num}: You rolled {user_dice}, I rolled {bot_dice}, you lost!\n"
            f"Strip one rag, {clothes} left, send a photo or video for inspection, or I'll whip you to death!"
        )
        user_data["waiting_for_proof"] = True

    user_data["dice_round"] = round_num + 1
    await save_user_data(user_id, user_data)
    logger.info(f"骰子游戏，用户ID: {user_id}, 轮次: {round_num}, 剩余衣服: {clothes}, 等待证明: {user_data['waiting_for_proof']}")

    keyboard = []
    if round_num < 8 and clothes > 0:
        keyboard.append([InlineKeyboardButton("下一局" if lang == "zh-cn" else "Next Round", callback_data=f"next_round_{user_id}")])
    else:
        keyboard.append([InlineKeyboardButton("游戏结束" if lang == "zh-cn" else "End Game", callback_data=f"end_game_{user_id}")])
    reply_markup = InlineKeyboardMarkup(keyboard)
    await update.message.reply_text(message, reply_markup=reply_markup, reply_to_message_id=update.message.message_id)

async def end_game(update: Update, context: ContextTypes.DEFAULT_TYPE, chat_id: int = None):
    """结束骰子游戏，显示结果并重置状态"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    clothes = user_data["clothes"]
    lang = user_data.get("preferred_language", "zh-cn")
    
    if clothes > 0:
        message = (
            f"8轮结束，你这下贱狗还剩{clothes}件破布，命够硬！\n"
            "赏你拍视频磕头10个，大喊谢爷调教，快滚去执行！"
            if lang == "zh-cn" else
            f"8 rounds over, you filthy dog still have {clothes} rags, tough luck!\n"
            "Reward: Record a video kowtowing 10 times, shouting thanks to me, go do it now!"
        )
    else:
        message = (
            "8轮结束，你这贱种全脱光了，输得像条死狗！\n"
            "罚你左右各扇5个耳光奕10次求爷饶命，拍视频给爷看！"
            if lang == "zh-cn" else
            "8 rounds over, you filthy scum are completely stripped, lost like a dead dog!\n"
            "Punishment: Slap yourself 5 times on each side and beg for mercy 10 times, record a video for me!"
        )
    keyboard = [
        [InlineKeyboardButton("滚回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    if chat_id is None:
        chat_id = update.effective_chat.id
    
    await context.bot.send_message(chat_id=chat_id, text=message, reply_markup=reply_markup)
    user_data["dice_round"] = 0
    user_data["clothes"] = 0
    user_data["previous_clothes"] = 5  # 重置上一轮衣物数量
    user_data["waiting_for_proof"] = False
    user_data["dice_fail_count"] = 0  # 重置连续失败次数
    await save_user_data(user_id, user_data)
    logger.info(f"骰子游戏结束，用户ID: {user_id}, 剩余衣服: {clothes}")

async def handle_dice_game(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理骰子游戏入口，初始化游戏状态"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if user_data["dice_round"] > 0:
        message = (
            f"贱奴！你的骰子游戏还在进行中，第{user_data['dice_round']}轮，剩余{user_data['clothes']}件衣服！"
            f"{'请输入 /dice 继续下一轮！' if not user_data['waiting_for_proof'] else '快上传证明照片或视频！'}"
            if lang == "zh-cn" else
            f"Filthy dog! Your dice game is still ongoing, round {user_data['dice_round']}, {user_data['clothes']} rags left!"
            f"{'Enter /dice to continue to the next round!' if not user_data['waiting_for_proof'] else 'Hurry and upload proof photo or video!'}"
        )
        keyboard = [
            [InlineKeyboardButton("继续游戏" if lang == "zh-cn" else "Continue Game", callback_data=f"next_round_{user_id}")],
            [InlineKeyboardButton("结束游戏" if lang == "zh-cn" else "End Game", callback_data=f"end_game_{user_id}")],
            [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
        ]
    else:
        user_data["dice_round"] = 1
        user_data["clothes"] = 5
        user_data["previous_clothes"] = 5  # 初始化上一轮衣物数量
        user_data["waiting_for_proof"] = False
        user_data["dice_fail_count"] = 0  # 初始化连续失败次数
        await save_user_data(user_id, user_data)
        message = (
            "🔥 贱奴！骰子羞辱游戏开始！🔥\n"
            "你有5件衣服，每输一局脱一件，8局结束或全脱光游戏结束！\n"
            "输了要拍照片或视频给爷检查，赢了赏你自扇一巴掌！\n"
            "输入 /dice 开始第一轮！"
            if lang == "zh-cn" else
            "🔥 Filthy dog! Dice Humiliation Game starts! 🔥\n"
            "You have 5 rags, lose one per loss, game ends after 8 rounds or fully stripped!\n"
            "Lose and send a photo or video for inspection, win and slap yourself for me!\n"
            "Enter /dice to start the first round!"
        )
        keyboard = [
            [InlineKeyboardButton("开始游戏" if lang == "zh-cn" else "Start Game", callback_data=f"start_dice_{user_id}")],
            [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
        ]
    
    try:
        success = await safe_edit_message(
            query=query,
            text=message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            logger=logger
        )
        
        if success:
            logger.info(f"用户 {user_id} 进入骰子游戏，轮次: {user_data['dice_round']}, 衣服: {user_data['clothes']}")
        else:
            logger.error(f"更新骰子游戏消息失败，用户ID: {user_id}")
            # 备用方案：发送新消息
            await query.message.reply_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
    except Exception as e:
        logger.error(f"处理骰子游戏失败，用户ID: {user_id}, 错误: {str(e)}")
        try:
            await query.message.reply_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except:
            pass

async def handle_next_round(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理下一轮游戏，触发 /dice 命令"""
    query = update.callback_query
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if user_data["dice_round"] == 0:
        await safe_edit_message(
            query=query,
            text="贱奴！你还没开始游戏，滚去'骰子羞辱'菜单启动！"
                 if lang == "zh-cn" else
                 "Filthy dog! You haven't started the game, go to 'Dice Humiliation' menu to start!",
            reply_markup=get_main_menu(user_id),
            logger=logger
        )
        return
    
    if user_data["waiting_for_proof"]:
        await safe_edit_message(
            query=query,
            text="狗东西！你输了还没献上证明！快拍照片或视频给爷检查，不然滚！"
                 if lang == "zh-cn" else
                 "You filthy thing! You lost and haven't sent proof! Send a photo or video for inspection, or get lost!",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("结束游戏" if lang == "zh-cn" else "End Game", callback_data=f"end_game_{user_id}")],
                [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
            ]),
            logger=logger
        )
        return
    
    message = (
        f"第{user_data['dice_round']}轮！贱奴，输入 /dice 掷骰子，别磨蹭！"
        if lang == "zh-cn" else
        f"Round {user_data['dice_round']}! Filthy dog, enter /dice to roll, don't dawdle!"
    )
    await safe_edit_message(
        query=query,
        text=message,
        reply_markup=InlineKeyboardMarkup([
            [InlineKeyboardButton("结束游戏" if lang == "zh-cn" else "End Game", callback_data=f"end_game_{user_id}")],
            [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
        ]),
        logger=logger
    )
    logger.info(f"用户 {user_id} 请求下一轮，轮次: {user_data['dice_round']}")

async def handle_dice_callbacks(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理骰子游戏的所有回调，包括快速回复入口"""
    query = update.callback_query
    callback_data = query.data
    user_id = query.from_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    # 检查回调数据中是否包含用户ID，并限制操作者
    user_id_in_callback = None
    parts = callback_data.split("_")
    if len(parts) > 2 and parts[-1].isdigit():
        user_id_in_callback = int(parts[-1])
    
    if user_id_in_callback and user_id_in_callback != user_id:
        try:
            await query.answer(
                "贱奴！这是别人的游戏，滚去操作自己的！"
                if lang == "zh-cn" else
                "Filthy dog! This is someone else's game, go use your own!"
            )
            logger.info(f"用户 {user_id} 尝试操作他人骰子游戏，回调数据: {callback_data}")
            return
        except TelegramError as e:
            logger.error(f"发送游戏权限提示失败，用户ID: {user_id}, 错误: {str(e)}")
            return
    
    if callback_data == "dice_game":
        await handle_dice_game(update, context)
    elif callback_data.startswith("next_round"):
        await handle_next_round(update, context)
    elif callback_data.startswith("end_game"):
        await end_game(update, context)
    else:
        logger.warning(f"未知的骰子游戏回调数据，用户ID: {user_id}, 数据: {callback_data}")
        await query.answer("无效的操作！" if lang == "zh-cn" else "Invalid operation!")

async def handle_dice_game_quick_reply(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理快捷回复菜单中的骰子游戏 - 发送新消息版本"""
    user_id = update.effective_user.id
    user_data = await get_user_data(user_id)
    lang = user_data.get("preferred_language", "zh-cn")
    
    if user_data["dice_round"] > 0:
        message = (
            f"贱奴！你的骰子游戏还在进行中，第{user_data['dice_round']}轮，剩余{user_data['clothes']}件衣服！"
            f"{'请输入 /dice 继续下一轮！' if not user_data['waiting_for_proof'] else '快上传证明照片或视频！'}"
            if lang == "zh-cn" else
            f"Filthy dog! Your dice game is still ongoing, round {user_data['dice_round']}, {user_data['clothes']} rags left!"
            f"{'Enter /dice to continue to the next round!' if not user_data['waiting_for_proof'] else 'Hurry and upload proof photo or video!'}"
        )
        keyboard = [
            [InlineKeyboardButton("继续游戏" if lang == "zh-cn" else "Continue Game", callback_data=f"next_round_{user_id}")],
            [InlineKeyboardButton("结束游戏" if lang == "zh-cn" else "End Game", callback_data=f"end_game_{user_id}")],
            [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
        ]
    else:
        user_data["dice_round"] = 1
        user_data["clothes"] = 5
        user_data["previous_clothes"] = 5  # 初始化上一轮衣物数量
        user_data["waiting_for_proof"] = False
        user_data["dice_fail_count"] = 0  # 初始化连续失败次数
        await save_user_data(user_id, user_data)
        message = (
            "🔥 贱奴！骰子羞辱游戏开始！🔥\n"
            "你有5件衣服，每输一局脱一件，8局结束或全脱光游戏结束！\n"
            "输了要拍照片或视频给爷检查，赢了赏你自扇一巴掌！\n"
            "输入 /dice 开始第一轮！"
            if lang == "zh-cn" else
            "🔥 Filthy dog! Dice Humiliation Game starts! 🔥\n"
            "You have 5 rags, lose one per loss, game ends after 8 rounds or fully stripped!\n"
            "Lose and send a photo or video for inspection, win and slap yourself for me!\n"
            "Enter /dice to start the first round!"
        )
        keyboard = [
            [InlineKeyboardButton("开始游戏" if lang == "zh-cn" else "Start Game", callback_data=f"start_dice_{user_id}")],
            [InlineKeyboardButton("返回主菜单" if lang == "zh-cn" else "Back to Main Menu", callback_data="back_to_main")]
        ]
    
    try:
        await update.message.reply_text(
            message,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        logger.info(f"用户 {user_id} 通过快捷回复进入骰子游戏，轮次: {user_data['dice_round']}, 衣服: {user_data['clothes']}")
    except Exception as e:
        logger.error(f"处理骰子游戏失败（快捷回复），用户ID: {user_id}, 错误: {str(e)}")
        try:
            await update.message.reply_text(
                "贱奴！骰子游戏启动失败，稍后再试！"
                if lang == "zh-cn" else
                "Filthy dog! Dice game failed to start, try again later!",
                reply_markup=get_main_menu(user_id)
            )
        except:
            pass
